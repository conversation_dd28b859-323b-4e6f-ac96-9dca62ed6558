{"ts-node": {"files": true}, "files": ["./node_modules/svm-nest-lib-v3/dist/types/global.d.ts"], "compilerOptions": {"module": "commonjs", "types": ["node"], "declaration": true, "removeComments": true, "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": false, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./", "incremental": true, "skipLibCheck": true}, "include": ["src/**/*"]}