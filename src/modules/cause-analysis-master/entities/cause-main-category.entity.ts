import { IdentifyEntity } from 'svm-nest-lib-v3';
import { Entity, Column, ManyToOne, OneToMany, Index } from 'typeorm';
import { DBIndexes } from '../../../commons/consts/db.const';
import { MainSubEnum } from '../../../commons/enums';
import { Company } from '../../company/company.entity';
import { PriorityMaster } from '../../priority-master/priority-master.entity';

@Entity()
@Index(DBIndexes.IDX_MAIN_CAUSE_ANALYSIS_MASTER_ID_MAIN_CATEGORY_NO, ['causeAnalysisMasterId', 'mainCategoryNo'], {
  unique: true,
  where: 'deleted = false',
})
@Index(DBIndexes.IDX_MAIN_CAUSE_ANALYSIS_MASTER_ID_MAIN_CATEGORY_NAME, ['causeAnalysisMasterId', 'mainCategoryName'], {
  unique: true,
  where: 'deleted = false',
})
export class CauseMainCategory extends IdentifyEntity {
  @Column()
  public mainCategoryNo: string;

  @Column()
  public mainCategoryName: string;

  @Column({ type: 'uuid' })
  public potentialRiskId: string;

  @Column({ type: 'enum', enum: MainSubEnum, default: MainSubEnum.PUBLISHED })
  public status: string;

  @Column()
  public causeAnalysisMasterId: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @ManyToOne('CauseAnalysisMaster', 'causeMainCategories', { onDelete: 'CASCADE' })
  causeAnalysisMaster: any;

  @OneToMany('CauseSubCategory', 'causeMainCategory', {
    onDelete: 'CASCADE',
  })
  causeSubCategories: any[];

  @ManyToOne(() => PriorityMaster)
  potentialRisk: PriorityMaster;

  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  company: Company;
}
