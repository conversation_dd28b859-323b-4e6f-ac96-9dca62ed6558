import { Connection, EntityRepository, In } from 'typeorm';
import {
  BaseError,
  CommonStatus,
  LoggerCommon,
  TokenPayloadModel,
  TypeORMRepository,
} from 'svm-nest-lib-v3';
import { CauseAnalysisMaster } from '../entities/cause-analysis-master.entity';
import { CompanyFeatureVersionRepository } from '../../commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../../commons/company-feature-version/feature-version.config';
import * as momentTZ from 'moment-timezone';
import { CauseMainCategory } from '../entities/cause-main-category.entity';
import { CauseSubCategory } from '../entities/cause-sub-category.entity';
import { leadingZero } from '../../../utils';
import { Company } from '../../company/company.entity';
import { ListCauseAnalysisMasterDto } from '../dto';
import { UpdateCauseAnalysisMasterDto } from '../dto/update-cause-analysis-master.dto';
import { Cause<PERSON>ype<PERSON>num, StatusCommon } from '../../../commons/enums';

@EntityRepository(CauseAnalysisMaster)
export class CauseAnalysisMasterRepository extends TypeORMRepository<CauseAnalysisMaster> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async createCauseAnalysisMaster(
    causeAnalysisMaster: CauseAnalysisMaster,
    preparedMains: CauseMainCategory[],
    preparedSubs: CauseSubCategory[],
  ) {
    try {
      await this.connection.transaction(async (manager) => {
        const timezone = causeAnalysisMaster.timezone || 'Asia/Ho_Chi_Minh';
        const currYear = momentTZ.tz(timezone).year();
        const counter = await manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager,
            companyId: causeAnalysisMaster.companyId,
            feature: FeatureVersionConfig.CAUSE_ANALYSIS_REF_NO,
            year: Number(currYear),
          });

        causeAnalysisMaster.refNo = await this._getCauseAnalysisCode(
          causeAnalysisMaster.companyId,
          counter,
          Number(currYear),
        );

        await manager.insert(CauseAnalysisMaster, causeAnalysisMaster);
        await manager.insert(CauseMainCategory, preparedMains);
        await manager.insert(CauseSubCategory, preparedSubs);

        // Handle active/inactive logic
        if (causeAnalysisMaster.status === CommonStatus.ACTIVE) {
          await this._deactivateOtherRecords(
            manager,
            causeAnalysisMaster.companyId,
            causeAnalysisMaster.causeType,
            causeAnalysisMaster.id,
          );
        }
      });
    } catch (error) {
      LoggerCommon.error('[createCauseAnalysisMaster]', error);
      throw error;
    }
  }

  async updateCauseAnalysisMaster(
    id: string,
    updateData: UpdateCauseAnalysisMasterDto,
    preparedMains: CauseMainCategory[],
    preparedSubs: CauseSubCategory[],
    user: TokenPayloadModel,
  ) {
    try {
      await this.connection.transaction(async (manager) => {
        // Update the main record
        const updateFields: Partial<CauseAnalysisMaster> = {
          updatedUserId: user.id,
        };

        if (updateData.status) {
          updateFields.status = updateData.status;
        }
        if (updateData.timezone) {
          updateFields.timezone = updateData.timezone;
        }
        if (updateData.udfVersionNo) {
          updateFields.udfVersionNo = updateData.udfVersionNo;
        }

        await manager.update(CauseAnalysisMaster, { id }, updateFields);

        // Delete existing categories and subcategories
        const existingMainCategories = await manager.find(CauseMainCategory, {
          where: { causeAnalysisMasterId: id },
          select: ['id'],
        });

        if (existingMainCategories.length > 0) {
          const mainCategoryIds = existingMainCategories.map((mc) => mc.id);
          await manager.delete(CauseSubCategory, { causeMainCategoryId: In(mainCategoryIds) });
        }

        await manager.delete(CauseMainCategory, { causeAnalysisMasterId: id });

        // Insert new categories and subcategories
        if (preparedMains.length > 0) {
          await manager.insert(CauseMainCategory, preparedMains);
        }
        if (preparedSubs.length > 0) {
          await manager.insert(CauseSubCategory, preparedSubs);
        }

        // Handle active/inactive logic for specific cause types
        if (updateData.status === CommonStatus.ACTIVE) {
          const existingRecord = await manager.findOne(CauseAnalysisMaster, { where: { id } });
          if (existingRecord && existingRecord.causeType !== CauseTypeEnum.TYPE_OF_LOSS) {
            await this._deactivateOtherRecords(
              manager,
              user.companyId,
              existingRecord.causeType,
              id,
            );
          }
        }
      });
    } catch (error) {
      LoggerCommon.error('[updateCauseAnalysisMaster]', error);
      throw error;
    }
  }

  async getListCauseAnalysisMaster(query: ListCauseAnalysisMasterDto, user: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('causeAnalysisMaster')
      .leftJoin('causeAnalysisMaster.company', 'company')
      .select([
        'causeAnalysisMaster.id',
        'causeAnalysisMaster.causeType',
        'causeAnalysisMaster.udfVersionNo',
        'causeAnalysisMaster.refNo',
        'causeAnalysisMaster.status',
        'causeAnalysisMaster.createdAt',
        'causeAnalysisMaster.updatedAt',
      ])
      .where('(causeAnalysisMaster.companyId = :companyId OR company.parentId = :companyId)', {
        companyId: user.companyId,
      });

    if (query.causeType) {
      queryBuilder.andWhere('causeAnalysisMaster.causeType = :causeType', {
        causeType: query.causeType,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('causeAnalysisMaster.status = :status', {
        status: query.status,
      });
    }

    if (query.search) {
      queryBuilder.andWhere(
        '(causeAnalysisMaster.udfVersionNo ILIKE :search OR causeAnalysisMaster.refNo ILIKE :search)',
        {
          search: `%${query.search}%`,
        },
      );
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'causeAnalysisMaster.createdAt:-1',
        advanceConditions: {
          createdAtFrom: query.createdAtFrom,
          createdAtTo: query.createdAtTo,
        },
      },
    );
  }

  async getDetailCauseAnalysisMaster(id: string, user: TokenPayloadModel) {
    const causeAnalysisMaster = await this.getOneQB(
      this.createQueryBuilder('causeAnalysisMaster')
        .leftJoinAndSelect('causeAnalysisMaster.causeMainCategories', 'causeMainCategories')
        .leftJoinAndSelect('causeMainCategories.potentialRisk', 'potentialRisk')
        .leftJoinAndSelect(
          'causeMainCategories.causeSubCategories',
          'causeSubCategories',
          'causeSubCategories.parentId IS NULL',
        )
        .leftJoinAndSelect('causeSubCategories.children', 'children')
        .leftJoin('causeAnalysisMaster.company', 'company')
        .select()
        .where(
          '(causeAnalysisMaster.id = :id AND (causeAnalysisMaster.companyId = :companyId OR company.parentId = :companyId))',
          {
            id,
            companyId: user.companyId,
          },
        ),
    );
    if (causeAnalysisMaster) {
      return causeAnalysisMaster;
    }
    throw new BaseError({ status: 404, message: 'causeAnalysisMaster.NOT_FOUND' });
  }

  private async _getCauseAnalysisCode(companyId: string, counter: number, currYear: number) {
    const version = leadingZero(counter, 5);
    const company = await this.manager.findOne(Company, {
      where: { id: companyId },
      select: ['code'],
    });
    return `CAUSE${company.code}${currYear}${version}`;
  }

  private async _deactivateOtherRecords(
    manager: any,
    companyId: string,
    causeType: string,
    excludeId: string,
  ) {
    await manager
      .createQueryBuilder()
      .update(CauseAnalysisMaster)
      .set({ status: StatusCommon.INACTIVE })
      .where('companyId = :companyId', { companyId })
      .andWhere('causeType = :causeType', { causeType })
      .andWhere('id != :excludeId', { excludeId })
      .execute();
  }

  async deleteCauseAnalysisMaster(id: string, user: TokenPayloadModel) {
    try {
      return await this.connection.transaction(async (manager) => {
        // First check if the record exists and user has access
        const existingRecord = await manager.findOne(CauseAnalysisMaster, {
          where: { id, companyId: user.companyId, deleted: false },
        });

        if (!existingRecord) {
          throw new BaseError({ status: 404, message: 'causeAnalysisMaster.NOT_FOUND' });
        }

        // Check for dependencies (if needed)
        // You can add dependency checks here similar to other master modules
        // const hasRef = await this.hasRefInGivenTables(id, 'causeAnalysisMasterId', [...]);
        // if (hasRef) {
        //   throw new BaseError({ status: 400, message: 'common.CANNOT_DELETE_DUE_TO_REF' });
        // }

        // Soft delete the main record
        const updateResult = await manager.update(
          CauseAnalysisMaster,
          { id, companyId: user.companyId, deleted: false },
          { deleted: true, updatedUserId: user.id },
        );

        if (updateResult.affected === 0) {
          throw new BaseError({ status: 404, message: 'causeAnalysisMaster.NOT_FOUND' });
        }

        // Soft delete related main categories
        await manager.update(
          CauseMainCategory,
          { causeAnalysisMasterId: id, deleted: false },
          { deleted: true },
        );

        // Soft delete related sub categories
        const mainCategories = await manager.find(CauseMainCategory, {
          where: { causeAnalysisMasterId: id },
          select: ['id'],
        });

        if (mainCategories.length > 0) {
          const mainCategoryIds = mainCategories.map((mc) => mc.id);
          await manager.update(
            CauseSubCategory,
            { causeMainCategoryId: In(mainCategoryIds), deleted: false },
            { deleted: true },
          );
        }

        return { deleted: true };
      });
    } catch (error) {
      LoggerCommon.error('[deleteCauseAnalysisMaster]', error);
      if (error instanceof BaseError) {
        throw error;
      }
      throw new BaseError({
        status: 500,
        message: `Failed to delete cause analysis master: ${error.message}`,
      });
    }
  }

  async getActiveCauseAnalysisMaster(companyId: string) {
    const causeAnalysisMasterList = await this.createQueryBuilder('causeAnalysisMaster')
      .leftJoin('causeAnalysisMaster.company', 'company')
      .select('causeAnalysisMaster.id AS id')
      .where('(causeAnalysisMaster.companyId = :companyId OR company.parentId = :companyId)', {
        companyId,
      })
      .andWhere('causeAnalysisMaster.status = :status', {
        status: CommonStatus.ACTIVE,
      })
      .getRawMany();
    return causeAnalysisMasterList.length ? causeAnalysisMasterList.map((item) => item.id) : [];
  }
}
