import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CauseAnalysisMasterController } from './cause-analysis-master.controller';
import { CauseAnalysisMasterRepository } from './repositories/cause-analysis-master.repository';
import { CauseAnalysisMasterService } from './cause-analysis-master.service';
import { CauseMainCategoryRepository } from './repositories/cause-main-category.repository';
import { CauseSubCategoryRepository } from './repositories/cause-sub-category.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CauseAnalysisMasterRepository,
      CauseMainCategoryRepository,
      CauseSubCategoryRepository,
    ]),
  ],
  controllers: [CauseAnalysisMasterController],
  providers: [CauseAnalysisMasterService],
  exports: [CauseAnalysisMasterService],
})
export class CauseAnalysisMasterModule {}
