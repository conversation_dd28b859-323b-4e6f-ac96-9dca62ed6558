import { Injectable } from '@nestjs/common';
import { TokenPayloadModel, Utils, BaseError } from 'svm-nest-lib-v3';
import { ListCauseAnalysisMasterDto } from './dto';
import { CreateCauseAnalysisMasterDto } from './dto/create-cause-analysis-master.dto';
import { UpdateCauseAnalysisMasterDto } from './dto/update-cause-analysis-master.dto';
import { CauseMainCategory } from './entities/cause-main-category.entity';
import { CauseSubCategory } from './entities/cause-sub-category.entity';
import { CauseAnalysisMaster } from './entities/cause-analysis-master.entity';
import { CauseAnalysisMasterRepository } from './repositories/cause-analysis-master.repository';

@Injectable()
export class CauseAnalysisMasterService {
  constructor(private readonly causeAnalysisMasterRepo: CauseAnalysisMasterRepository) {}

  async createCauseAnalysisMaster(
    entityParam: CreateCauseAnalysisMasterDto,
    user: TokenPayloadModel,
  ) {
    try {
      const causeAnalysisMasterId = Utils.strings.generateUUID();
      const causeAnalysisMaster = new CauseAnalysisMaster();
      causeAnalysisMaster.id = causeAnalysisMasterId;
      causeAnalysisMaster.causeType = entityParam.causeType;
      causeAnalysisMaster.udfVersionNo = entityParam.udfVersionNo;
      causeAnalysisMaster.status = entityParam.status;
      causeAnalysisMaster.timezone = entityParam.timezone;
      causeAnalysisMaster.companyId = user.companyId;
      causeAnalysisMaster.createdUserId = user.id;

      const preparedMains: CauseMainCategory[] = [];
      const preparedSubs: CauseSubCategory[] = [];

      for (const mainCategoryData of entityParam.causeMainCategories) {
        const mainCategoryId = Utils.strings.generateUUID();
        const mainCategory = new CauseMainCategory();
        mainCategory.id = mainCategoryId;
        mainCategory.mainCategoryNo = mainCategoryData.mainCategoryNo;
        mainCategory.mainCategoryName = mainCategoryData.mainCategoryName;
        mainCategory.potentialRiskId = mainCategoryData.potentialRiskId;
        mainCategory.causeAnalysisMasterId = causeAnalysisMasterId;
        mainCategory.companyId = user.companyId;
        preparedMains.push(mainCategory);

        if (mainCategoryData.causeSubCategories) {
          for (const subCategoryData of mainCategoryData.causeSubCategories) {
            this._prepareSubCategories(
              subCategoryData,
              mainCategoryId,
              user.companyId,
              preparedSubs,
              null,
              1,
            );
          }
        }
      }

      await this.causeAnalysisMasterRepo.createCauseAnalysisMaster(
        causeAnalysisMaster,
        preparedMains,
        preparedSubs,
      );
      return causeAnalysisMasterId;
    } catch (error) {
      if (error instanceof BaseError) {
        throw error;
      }
      throw new BaseError({
        status: 500,
        message: `Failed to create cause analysis master: ${error.message}`,
      });
    }
  }

  private _prepareSubCategories(
    subCategoryData: any,
    mainCategoryId: string,
    companyId: string,
    preparedSubs: CauseSubCategory[],
    parentId: string | null,
    level: number,
  ) {
    const subCategoryId = Utils.strings.generateUUID();
    const subCategory = new CauseSubCategory();
    subCategory.id = subCategoryId;
    subCategory.subCategoryName = subCategoryData.subCategoryName;
    subCategory.subRefNo = subCategoryData.subRefNo;
    subCategory.level = level;
    subCategory.parentId = parentId;
    subCategory.causeMainCategoryId = mainCategoryId;
    subCategory.companyId = companyId;
    preparedSubs.push(subCategory);

    // Handle children (second sub categories)
    if (subCategoryData.children && subCategoryData.children.length > 0) {
      for (const childData of subCategoryData.children) {
        this._prepareSubCategories(
          childData,
          mainCategoryId,
          companyId,
          preparedSubs,
          subCategoryId,
          2,
        );
      }
    }
  }

  async getListCauseAnalysisMaster(query: ListCauseAnalysisMasterDto, user: TokenPayloadModel) {
    return this.causeAnalysisMasterRepo.getListCauseAnalysisMaster(query, user);
  }

  async getDetailCauseAnalysisMaster(id: string, user: TokenPayloadModel) {
    return this.causeAnalysisMasterRepo.getDetailCauseAnalysisMaster(id, user);
  }

  async updateCauseAnalysisMaster(
    id: string,
    entityParam: UpdateCauseAnalysisMasterDto,
    user: TokenPayloadModel,
  ) {
    try {
      // Verify record exists and user has access
      await this.causeAnalysisMasterRepo.getDetailCauseAnalysisMaster(id, user);

      // Prepare updated data
      const preparedMains: CauseMainCategory[] = [];
      const preparedSubs: CauseSubCategory[] = [];

      if (entityParam.causeMainCategories) {
        for (const mainCategoryData of entityParam.causeMainCategories) {
          const mainCategoryId = mainCategoryData.id || Utils.strings.generateUUID();
          const mainCategory = new CauseMainCategory();
          mainCategory.id = mainCategoryId;
          mainCategory.mainCategoryNo = mainCategoryData.mainCategoryNo;
          mainCategory.mainCategoryName = mainCategoryData.mainCategoryName;
          mainCategory.potentialRiskId = mainCategoryData.potentialRiskId;
          mainCategory.causeAnalysisMasterId = id;
          mainCategory.companyId = user.companyId;
          preparedMains.push(mainCategory);

          if (mainCategoryData.causeSubCategories) {
            for (const subCategoryData of mainCategoryData.causeSubCategories) {
              this._prepareSubCategories(
                subCategoryData,
                mainCategoryId,
                user.companyId,
                preparedSubs,
                null,
                1,
              );
            }
          }
        }
      }

      // Update the master record
      await this.causeAnalysisMasterRepo.updateCauseAnalysisMaster(
        id,
        entityParam,
        preparedMains,
        preparedSubs,
        user,
      );

      return id;
    } catch (error) {
      if (error instanceof BaseError) {
        throw error;
      }
      throw new BaseError({
        status: 500,
        message: `Failed to update cause analysis master: ${error.message}`,
      });
    }
  }

  async deleteCauseAnalysisMaster(id: string, user: TokenPayloadModel) {
    return this.causeAnalysisMasterRepo.deleteCauseAnalysisMaster(id, user);
  }

  async getActiveCauseAnalysisMaster(companyId: string) {
    return this.causeAnalysisMasterRepo.getActiveCauseAnalysisMaster(companyId);
  }
}
