import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ListQueryDto } from '../../../commons/dtos/list-query.dto';
import { CauseTypeEnum } from '../../../commons/enums';

export class ListCauseAnalysisMasterDto extends ListQueryDto {
  @ApiProperty({
    enum: CauseTypeEnum,
    required: false,
    description: 'Filter by cause type',
  })
  @IsOptional()
  @IsEnum(CauseTypeEnum)
  causeType?: string;

  @ApiProperty({
    type: 'string',
    required: false,
    description: 'Search by version number or reference number',
  })
  @IsOptional()
  @IsString()
  search?: string;
}
