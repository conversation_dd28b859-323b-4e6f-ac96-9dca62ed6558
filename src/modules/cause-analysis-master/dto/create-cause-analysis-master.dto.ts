import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  MaxLength,
  ValidateNested,
  IsString,
  ArrayMinSize,
} from 'class-validator';
import { CommonStatus, IsMomentTimeZoneName } from 'svm-nest-lib-v3';
import { CauseTypeEnum } from '../../../commons/enums';
import { CreateCauseMainCategoryDto } from './create-cause-main-category.dto';

export class CreateCauseAnalysisMasterDto {
  @ApiProperty({ enum: CauseTypeEnum })
  @IsNotEmpty()
  @IsEnum(CauseTypeEnum)
  causeType: string;

  @ApiProperty({ enum: CommonStatus })
  @IsEnum(CommonStatus)
  status: string;

  @ApiProperty({ type: 'string', example: 'CAUSESVMSG202100001' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(20)
  udfVersionNo: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsString()
  @IsMomentTimeZoneName()
  timezone?: string;

  @IsNotEmpty()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateCauseMainCategoryDto)
  @ApiProperty({
    type: [CreateCauseMainCategoryDto],
    description: 'Cause main categories',
    required: true,
  })
  causeMainCategories: CreateCauseMainCategoryDto[];
}
