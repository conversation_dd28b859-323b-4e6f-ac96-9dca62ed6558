import { constant, pick, set } from 'lodash';
import * as momentTZ from 'moment-timezone';
import {
  BaseError,
  ConstLib,
  ForbiddenError,
  <PERSON><PERSON><PERSON>om<PERSON>,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityManager, EntityRepository, getConnection, In } from 'typeorm';

import {
  ActionValueChangeEnum,
  AuditEntity,
  CarVerificationStatusEnum,
  EmailTypeEnum,
  FindingItemWorkflowStatus,
  FindingStatus,
  GlobalStatusEnum,
  IARVerificationStatus,
  InternalAuditReportStatus,
  MailTemplate,
  ModuleEnum,
  ModulePathEnum,
  PlanningRequestStatus,
  PushTypeEnum,
  ReportFindingFormStatus,
  UserRelationship,
  WorkflowPermission,
} from '../../../commons/enums';
import {
  convertFilterField,
  createSelectSql,
  createWhereSql,
  customSort,
  handleResponse,
  isDoingGrouping,
  leadingZero,
  MySet,
  PayloadAGGridDto,
} from '../../../utils';
import { ListFindingItemOfIARQueryDTO } from '../../audit-workspace/dto';
import { FeatureVersionConfig } from '../../commons/company-feature-version/feature-version.config';
import {
  ApproveIARDTO,
  ApproveIARQueryDTO,
  CloseoutIARDTO,
  INTERNAL_AUDIT_REPORT_FILTER_FIELDS,
  ListInternalAuditReportDTO,
  ListPreviousIARQueryDTO,
  ReassignIARDTO,
  ReportFindingItem3Dto,
  ReviewFindingItemDto,
  ReviewIARDTO,
  ReviewIARQueryDTO,
  SubmitIARQueryDTO,
  SubmitInternalAuditReportDTO,
  TriggerCreateInternalAuditReportDTO,
  UpdateFindingItemAndAssignPICDto,
  UpdateFindingItemForPICDto,
  UpdateIARReportHeaderDTO,
} from '../dto';

import { ReportFindingItem } from '../../audit-workspace/entities/report-finding-item.entity';
import { Company } from '../../company/company.entity';
import { ReportTemplate } from '../../report-template-master/report-template/report-template.entity';
import { IARAuditType } from '../entities/iar-audit-type.entity';
import { IARPlanningRequest } from '../entities/iar-planning-request.entity';
import { IARReportHeaderDescription } from '../entities/iar-report-header-description.entity';
import { IARReportHeader } from '../entities/iar-report-header.entity';
import { IARUser } from '../entities/iar-user.entity';
import { InternalAuditReportComment } from '../entities/internal-audit-report-comment.entity';
import { InternalAuditReportHistory } from '../entities/internal-audit-report-history.entity';
import { InternalAuditReportOfficeComment } from '../entities/internal-audit-report-office-comment.entity';
import { InternalAuditReport } from '../entities/internal-audit-report.entity';

import { ReportFindingItemRepository } from '../../audit-workspace/repositories/report-finding-item.repository';
import { CompanyFeatureVersionRepository } from '../../commons/company-feature-version/company-feature-version.repository';
import { PlanningRequestRepository } from '../../planning-request/repositories/planning-request.repository';
import { UserRepository } from '../../user/user.repository';

import { AppConst } from '../../../commons/consts/app.const';
import { CreatedUserHistoryModel } from '../../../commons/models';
import APP_CONFIG from '../../../configs/app.config';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  IUser,
} from '../../../micro-services/async/notification.producer';
import { AuditActivityEnum, AuditModuleEnum } from '../../audit-log/audit-log.entity';
import { AuditLogRepository } from '../../audit-log/audit-log.repository';
import { FindingItemHistory } from '../../audit-workspace/entities/finding-item-history.entity';
import { PlanningRequest } from '../../planning-request/entities/planning-request.entity';
import { ReportHeader } from '../../report-template-master/report-header/report-header.entity';
import { UserAssignment } from '../../user-assignment/user-assignment.entity';
import { ModuleType } from '../../user-assignment/user-assignment.enum';
import { UserAssignmentRepository } from '../../user-assignment/user-assignment.repository';
import { VesselCharterer } from '../../vessel/entity/vessel-charterer.entity';
import { VesselDocHolder } from '../../vessel/entity/vessel-doc-holder.entity';
import { VesselOwner } from '../../vessel/entity/vessel-owner.entity';
import { VesselChartererRepository } from '../../vessel/repository/vessel-charterer.repository';
import { VesselDocHolderRepository } from '../../vessel/repository/vessel-doc-holder.repository';
import { VesselOwnerRepository } from '../../vessel/repository/vessel-owner.repository';
import { IARBusiness } from '../business/iar.business';
import { IARReportHeaderRepository } from './iar-report-header.repository';
import { InternalAuditReportHistoryRepository } from './internal-audit-report-history.repository';
import { InternalAuditReportOfficeCommentRepository } from './internal-audit-report-office-comment.repository';
import { Department } from '../../department-master/department.entity';
import { ReportFindingFormRepository } from 'src/modules/report-finding/repositories/report-finding-form.repository';
import { commonCheckValueChange } from '../../../commons/functions/value-change-history';
import { CARRepository } from '../../corrective-action-request/repositories/car.repository';
import { MetaConfig } from 'src/modules-qa/catalog/entity/meta-config.entity';
import { CatalogConst } from 'src/modules-qa/catalog/catalog-key.const';
import { CAPRepository } from '../../corrective-action-request/repositories/cap.repository';
import {
  _supportCheckRoleScopeForGetList,
  _supportWhereDOCChartererOwner,
} from '../../../commons/functions';
import { ValueChangeHistory } from '../../value-change-history/value-change-history.entity';
import { InspectionMappingRepository } from 'src/modules/inspection-mapping/repositories/inspection-mapping.repository';
import { FillSAChecklistQuestionRepository } from 'src/modules/audit-workspace/repositories/fill-sa-checklist-question.repository';
import { SAFindingItem } from 'src/modules/audit-workspace/entities/sa-finding-items.entity';

@EntityRepository(InternalAuditReport)
export class InternalAuditReportRepository extends TypeORMRepository<InternalAuditReport> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async listFindingItemsOfIAR(
    id: string,
    query: ListFindingItemOfIARQueryDTO,
    token: TokenPayloadModel,
  ) {
    return this.connection
      .getCustomRepository(ReportFindingItemRepository)
      .listFindingItemsOfIAR(id, query, token);
  }

  async _triggerCreateInternalAuditReport(
    managerTrans: EntityManager,
    params: TriggerCreateInternalAuditReportDTO,
    token?: TokenPayloadModel,
    createdUser?: CreatedUserHistoryModel,
  ) {
    try {
      const internalAuditReportId = Utils.strings.generateUUID();

      params.timezone = params.timezone || AppConst.DEFAULT_TIMEZONE;
      const currYear = momentTZ.tz(params.timezone).year();

      // List all RTM suitable for office or vessel plan
      let selectedReportTemplates: ReportTemplate[];
      // Check if in case audit vessel
      if (params.vesselId) {
        selectedReportTemplates = await managerTrans
          .getRepository(ReportTemplate)
          .createQueryBuilder('reportTemplate')
          .leftJoin('reportTemplate.vesselTypes', 'vesselTypes')
          .leftJoin('vesselTypes.vessels', 'vessels')
          .leftJoin('reportTemplate.auditTypes', 'auditTypes')
          .leftJoin('auditTypes.planningRequests', 'planningRequests')
          .leftJoinAndSelect(
            'reportTemplate.reportHeaders',
            'reportHeaders',
            'reportHeaders.deleted = false',
          )
          .where(
            `(reportTemplate.auditEntity = :auditEntity AND
              reportTemplate.deleted = false AND
              reportTemplate.status = 'active' AND
              vessels.id = :vesselId AND
              planningRequests.id = :planningRequestId)`,
            {
              auditEntity: AuditEntity.VESSEL,
              vesselId: params.vesselId,
              planningRequestId: params.planningRequestId,
            },
          )
          .select()
          .addSelect(['auditTypes.id', 'auditTypes.name'])
          .orderBy('reportTemplate.createdAt', 'DESC')
          .addOrderBy('reportHeaders.serialNumber', 'ASC')
          .getMany();
      } else {
        // case audit office
        selectedReportTemplates = await managerTrans
          .getRepository(ReportTemplate)
          .createQueryBuilder('reportTemplate')
          .leftJoin('reportTemplate.auditTypes', 'auditTypes')
          .leftJoin('auditTypes.planningRequests', 'planningRequests')
          .leftJoinAndSelect(
            'reportTemplate.reportHeaders',
            'reportHeaders',
            'reportHeaders.deleted = false',
          )
          .where(
            `reportTemplate.auditEntity = :auditEntity AND 
              reportTemplate.deleted = false AND
              reportTemplate.status = 'active' AND
              planningRequests.id = :planningRequestId`,
            {
              auditEntity: AuditEntity.OFFICE,
              planningRequestId: params.planningRequestId,
            },
          )
          .select()
          .addSelect(['auditTypes.id', 'auditTypes.name'])
          .orderBy('reportTemplate.createdAt', 'DESC')
          .addOrderBy('reportHeaders.serialNumber', 'ASC')
          .getMany();
      }

      if (!selectedReportTemplates.length) {
        throw new BaseError({
          status: 400,
          message: params.vesselId
            ? 'internalAuditReport.CANNOT_CREATE_DUE_TO_NO_REPORT_TEMPLATE_1'
            : 'internalAuditReport.CANNOT_CREATE_DUE_TO_NO_REPORT_TEMPLATE_2',
        });
      }

      const selectedReportHeaders = IARBusiness.mergeReportHeadersFromReportTemplates(
        IARBusiness.getReportTemplatesWithUniqueAuditTypes(selectedReportTemplates),
      );

      // Map report headers to IAR_report_header
      const preparedIARReportHeaders: IARReportHeader[] = [];
      for (let i = 0; i < selectedReportHeaders.length; i++) {
        const reportHeader: ReportHeader & { auditTypes?: string[] } = selectedReportHeaders[i];
        preparedIARReportHeaders.push({
          headerComment: null,
          internalAuditReportId,
          reportHeaderId: reportHeader.id,
          createdUserId: token?.id || createdUser?.id,
          serialNumber: reportHeader.serialNumber,
          topic: reportHeader.topic,
          topicType: reportHeader.topicType,
          minScore: reportHeader.minScore,
          maxScore: reportHeader.maxScore,
          isDefault: reportHeader.isDefault,
          parentId: reportHeader.parentId,
          printOption: reportHeader.printOption,
          isPrint: reportHeader.isPrint,
          type: reportHeader.type,
          auditTypes: [...new Set(reportHeader.auditTypes)],
        } as IARReportHeader);
      }

      const counter = await managerTrans
        .getCustomRepository(CompanyFeatureVersionRepository)
        .getNextVersion({
          manager: managerTrans,
          companyId: token?.companyId || createdUser?.companyId || params.companyId,
          feature: FeatureVersionConfig.INTERNAL_AUDIT_REPORT_REF_ID,
          year: Number(currYear),
        });

      const leadingCounter = leadingZero(counter, 3);
      const companyFound = await this.manager.findOne(Company, {
        where: { id: token?.companyId || createdUser?.companyId || params.companyId },
        select: ['code'],
      });

      // let listROFItems: any = await managerTrans.find(ReportFindingItem, {
      //   reportFindingFormId: params.reportFindingFormId,
      // });

      // const listIarItemIds = [];

      const numVerifiedAndClosedROFItem = 0;
      // const preparedFindingItems = listROFItems.map((x) => {
      //   const iarItemId = Utils.strings.generateUUID();
      //   listIarItemIds.push(iarItemId);
      //   const item = { ...x, id: iarItemId };
      //   Reflect.deleteProperty(item, 'reportFindingFormId');

      //   if (item.isVerify === true && item.isOpen === false) {
      //     numVerifiedAndClosedROFItem += 1;
      //   }
      //   item.findingAttachments = decryptAttachmentValues(x.findingAttachments);

      //   return {
      //     ...item,
      //     internalAuditReportId,
      //     createdUserId: token.id,
      //   };
      // });

      // link rofItem with iarItem
      // listROFItems = listROFItems.map((item, index) => {
      //   item.findingAttachments = decryptAttachmentValues(item.findingAttachments);
      //   return {
      //     ...item,
      //     iarItemId: listIarItemIds[index],
      //     isSyncToIAR: true,
      //   };
      // });

      // check verification status
      let verificationStatus = IARVerificationStatus.YET_TO_VERIFIED;
      if (numVerifiedAndClosedROFItem === 0) {
        verificationStatus = IARVerificationStatus.YET_TO_VERIFIED;
      }
      // else if (
      //   numVerifiedAndClosedROFItem > 0 &&
      //   numVerifiedAndClosedROFItem < listROFItems.length
      // ) {
      //   verificationStatus = IARVerificationStatus.PARTIALLY_VERIFIED;
      // } else if (numVerifiedAndClosedROFItem === listROFItems.length) {
      //   verificationStatus = IARVerificationStatus.ALL_VERIFIED;
      // }

      // prepared planningRequestId for created IAR
      // const reportForm = await managerTrans.findOne(ReportFindingForm, {
      //   id: params.reportFindingFormId,
      // });
      // const planningRequestId = reportForm.planningRequestId;
      const planningRequestId = params.planningRequestId;
      // Check and get PR
      const pr = await this.connection
        .getCustomRepository(PlanningRequestRepository)
        ._checkAndGetPROnTrigger(planningRequestId, token);

      // Prepared caches: IAR planning request
      const preparedIARPlanningRequest: IARPlanningRequest = {
        internalAuditReportId: internalAuditReportId,
        planningRequestId: pr.id,
        vesselId: pr.vesselId,
        vesselName: pr.vessel?.name,
        // countryFlag: pr.vessel?.countryFlag,
        countryId: pr.vessel?.countryId,
        vesselTypeId: pr.vessel?.vesselTypeId,
        vesselTypeName: pr.vessel?.vesselType.name,
        // fleetId: pr.vessel?.fleetId,
        departmentName: pr.department?.name,
        departmentId: pr.department?.id,
        auditCompanyName: pr.auditCompany?.name,
        auditCompanyId: pr.auditCompany?.id,
        fleetName: pr.vessel?.fleetName,
        fromPortId: pr.fromPortId,
        fromPortName: pr.fromPort?.name ? pr.fromPort?.name : null,
        toPortId: pr.toPortId,
        toPortName: pr.toPort?.name ? pr.toPort?.name : null,
      } as IARPlanningRequest;

      if (pr.departments.length > 0) {
        Object.assign(preparedIARPlanningRequest, {
          departments: pr.departments.map(
            (department) => ({ id: department.id, name: department.name } as Department),
          ),
        });
      }

      // Prepare caches: rof users
      const preparedIARUsers: IARUser[] = [];
      for (let i = 0; i < pr.auditors.length; i++) {
        preparedIARUsers.push({
          internalAuditReportId: internalAuditReportId,
          userId: pr.auditors[i].id,
          username: pr.auditors[i].username,
          relationship:
            pr.auditors[i].id === pr.leadAuditorId
              ? UserRelationship.LEAD_AUDITOR
              : UserRelationship.AUDITOR,
        } as IARUser);
      }

      for (let i = 0; i < pr.vessel?.owners.length; i++) {
        preparedIARUsers.push({
          internalAuditReportId,
          userId: pr.vessel.owners[i].id,
          username: pr.vessel.owners[i].username,
          relationship: UserRelationship.VESSEL_MANAGER,
        } as IARUser);
      }

      // Prepare caches: rof audit type
      const preparedIARAuditTypes: IARAuditType[] = [];
      for (let i = 0; i < pr.auditTypes.length; i++) {
        preparedIARAuditTypes.push({
          internalAuditReportId,
          auditTypeId: pr.auditTypes[i].id,
          auditTypeName: pr.auditTypes[i].name,
        } as IARAuditType);
      }

      const preparedIAR = {
        id: internalAuditReportId,
        refId: `${companyFound?.code}/IR/${leadingCounter}/${currYear}`,
        serialNumber: `IR${companyFound.code}${currYear}${leadingCounter}`,
        vesselId: params.vesselId,
        //reportFindingFormId: params.reportFindingFormId,
        reportFindingFormId: null,
        createdUserId: token?.id || createdUser?.id,
        departmentId: pr.department?.id,
        auditCompanyId: pr.auditCompany?.id,
        entityType: pr.entityType,
        companyId: token?.companyId || params?.companyId,
        status: InternalAuditReportStatus.DRAFT,
        previousStatus: InternalAuditReportStatus.DRAFT,
        planningRequestId,
        verificationStatus,
        //leadAuditorId: reportForm.leadAuditorId,
        leadAuditorId: pr.leadAuditorId,
      };

      if (pr.departments.length > 0) {
        Object.assign(preparedIAR, {
          departments: pr.departments.map((department) => ({ id: department.id } as Department)),
        });
      }

      // Create IAR
      await managerTrans.save(InternalAuditReport, preparedIAR);

      // Get user info
      if (!createdUser) {
        createdUser = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token?.id);
      }

      await Promise.all([
        // Create IAR report header
        managerTrans.insert(IARReportHeader, preparedIARReportHeaders),

        // create caches: IAR planning request
        managerTrans.insert(IARPlanningRequest, preparedIARPlanningRequest),

        // create caches: IAR user
        managerTrans.insert(IARUser, preparedIARUsers),

        // create caches: IAR audit type
        managerTrans.insert(IARAuditType, preparedIARAuditTypes),

        // Clone ROF item for IAR
        //managerTrans.insert(ReportFindingItem, preparedFindingItems),

        // Mark finding items as sync to IAR and update iarItemId for rofItemId
        //managerTrans.save(ReportFindingItem, listROFItems),

        // Create IAR History
        managerTrans.insert(InternalAuditReportHistory, {
          internalAuditReportId,
          createdUser,
          status: InternalAuditReportStatus.DRAFT,
        }),

        // Update ROF carId to Null after clone items to IAR
        // managerTrans.update(
        //   ReportFindingItem,
        //   { id: In(listROFItems.map((item) => item.id)) },
        //   { carId: null },
        // ),
      ]);

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.INSPECTION_REPORT,
          planningId: planningRequestId,
        },
        null,
        [AuditActivityEnum.CREATED],
        createdUser,
      );
      return 1;
    } catch (ex) {
      LoggerCommon.error('[InternalAuditReportRepository] createIAR error ', ex.message || ex);
      throw ex;
    }
  }

  async _triggerUpdateInternalAuditReport(
    managerTrans: EntityManager,
    internalAuditReport: InternalAuditReport,
    reportFindingFormId: string,
    planningRequestId: string,
    token: TokenPayloadModel,
    createdUser?: CreatedUserHistoryModel,
  ) {
    try {
      // List finding items haven't sync to IAR
      const listROFItems = await managerTrans.find(ReportFindingItem, {
        reportFindingFormId,
        isSyncToIAR: false,
      });

      const preparedFindingItems = listROFItems.map((x) => {
        const item = { ...x };
        // Reflect.deleteProperty(item, 'id');
        // Reflect.deleteProperty(item, 'reportFindingFormId');

        return {
          ...item,
          internalAuditReportId: internalAuditReport.id,
          createdUserId: token.id,
        };
      });

      // Update IAR
      await managerTrans.update(
        InternalAuditReport,
        { id: internalAuditReport.id },
        {
          updatedUserId: token.id,
          status: InternalAuditReportStatus.DRAFT,
          previousStatus: internalAuditReport.status,
        },
      );

      // Update PL status to InProgress when re-closeout ROF
      await managerTrans.update(
        PlanningRequest,
        { id: planningRequestId },
        {
          status: PlanningRequestStatus.IN_PROGRESS,
        },
      );

      // Get user info
      if (!createdUser) {
        createdUser = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token.id);
      }

      await Promise.all([
        // Save ROF item for IAR
        managerTrans.save(ReportFindingItem, preparedFindingItems),

        // Mark finding items as sync to IAR
        managerTrans.update(
          ReportFindingItem,
          { id: In(listROFItems.map((item) => item.id)) },
          { isSyncToIAR: true },
        ),

        // Create IAR History
        managerTrans.insert(InternalAuditReportHistory, {
          internalAuditReportId: internalAuditReport.id,
          createdUser,
          status: InternalAuditReportStatus.DRAFT,
        }),
      ]);

      // Handle add Audit log
      await managerTrans.getCustomRepository(AuditLogRepository).createAuditLog(
        {
          module: AuditModuleEnum.INSPECTION_REPORT,
          planningId: planningRequestId,
        },
        null,
        [AuditActivityEnum.UPDATED_INFO],
        createdUser,
      );
      return 1;
    } catch (ex) {
      LoggerCommon.error('[InternalAuditReportRepository] triggerUpdate error ', ex.message || ex);
      throw ex;
    }
  }

  async submitInternalAuditReport(
    internalAuditReportId: string,
    params: SubmitInternalAuditReportDTO,
    preparedIAROfficeComments: InternalAuditReportOfficeComment[],
    preparedIARReportHeaders: IARReportHeader[],
    preparedIARReportHeaderDescriptions: IARReportHeaderDescription[],
    query: SubmitIARQueryDTO,
    token: TokenPayloadModel,
  ) {
    try {
      const IARFound = await this.createQueryBuilder('internalAuditReport')
        .where(
          `internalAuditReport.id = :internalAuditReportId AND 
            internalAuditReport.companyId = :companyId AND 
            internalAuditReport.status != :status`,
          {
            internalAuditReportId,
            companyId: token.companyId,
            status: InternalAuditReportStatus.CLOSEOUT,
          },
        )
        .leftJoin('internalAuditReport.reportFindingForm', 'reportFindingForm')
        .leftJoinAndSelect('internalAuditReport.planningRequest', 'planningRequest')
        .leftJoinAndSelect(
          'internalAuditReport.internalAuditReportComment',
          'internalAuditReportComment',
        )
        .leftJoinAndSelect(
          'internalAuditReport.internalAuditReportHistories',
          'internalAuditReportHistories',
        )
        .leftJoinAndSelect('internalAuditReport.userAssignments', 'userAssignments')
        .leftJoinAndSelect(
          'internalAuditReport.internalAuditReportOfficeComments',
          'internalAuditReportOfficeComments',
        )
        .leftJoinAndSelect('internalAuditReport.IARReportHeaders', 'IARReportHeaders')
        .leftJoinAndSelect(
          'IARReportHeaders.IARReportHeaderDescriptions',
          'IARReportHeaderDescriptions',
        )
        .leftJoinAndSelect('planningRequest.auditWorkspace', 'auditWorkspace')
        .orderBy('internalAuditReportHistories.createdAt', 'DESC')
        .getOne();

      if (!IARFound) {
        throw new BaseError({ message: 'internalAuditReport.NOT_FOUND' });
      }

      if (!IARFound?.reportFindingFormId) {
        throw new BaseError({
          message: 'internalAuditReport.FILL_CHKLIST_FINDING_ITEMS_BEFORE_SUBMITTED',
        });
      }

      if (!IARFound?.planningRequest?.auditWorkspace?.isGenerateROF) {
        throw new BaseError({
          message: 'internalAuditReport.GENERATE_ROF_BEFORE_SUBMITTED',
        });
      }

      // Get user history info
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(token.id);

      //#region Prepare Office Comments
      const listCurrentIAROfficeComments = IARFound.internalAuditReportOfficeComments;
      const listCurrentIAROfficeCommentIds: string[] = [];
      const listNewIAROfficeCommentIds: string[] = [];

      if (listCurrentIAROfficeComments && listCurrentIAROfficeComments.length > 0) {
        listCurrentIAROfficeComments.forEach((officeComment: InternalAuditReportOfficeComment) => {
          listCurrentIAROfficeCommentIds.push(officeComment.id);
        });
      }

      if (preparedIAROfficeComments && preparedIAROfficeComments.length > 0) {
        for (let i = 0; i < preparedIAROfficeComments.length; i++) {
          set(preparedIAROfficeComments[i], 'createdUser', createdUser);
          listNewIAROfficeCommentIds.push(preparedIAROfficeComments[i].id);
        }
      }

      const listIAROfficeCommentUpdateIds = MySet.intersect(
        new Set(listCurrentIAROfficeCommentIds),
        new Set(listNewIAROfficeCommentIds),
      );

      const listIAROfficeCommentCreateIds = MySet.difference(
        new Set(listNewIAROfficeCommentIds),
        new Set(listCurrentIAROfficeCommentIds),
      );

      const listIAROfficeCommentDeleteIds = MySet.difference(
        new Set(listCurrentIAROfficeCommentIds),
        new Set(listNewIAROfficeCommentIds),
      );

      const listIAROfficeCommentCreate = preparedIAROfficeComments.filter((item) =>
        listIAROfficeCommentCreateIds.has(item.id),
      );

      const listIAROfficeCommentUpdate = preparedIAROfficeComments.filter((item) =>
        listIAROfficeCommentUpdateIds.has(item.id),
      );

      //#endregion Prepare Comments

      //#region Prepare IAR Header description
      const {
        listHeaderDescriptionsCreate,
        listHeaderDescriptionsUpdate,
        listHeaderDescriptionDeleteIds,
      } = this._handleIARReportHeaderDescription(IARFound, preparedIARReportHeaderDescriptions);
      //#endregion

      const preparedComment = {
        ...params.internalAuditComment,
        id: params.internalAuditComment.id
          ? params.internalAuditComment.id
          : Utils.strings.generateUUID(),
        internalAuditReportId,
        createdUserId: token.id,
        updatedUserId: token.id,
      } as InternalAuditReportComment;

      const prepareROFItems = [];
      if (params.reportFindingItems && params.reportFindingItems.length > 0) {
        params.reportFindingItems.forEach((item: ReportFindingItem3Dto) => {
          prepareROFItems.push({
            ...item,
            planedCompletionDate: item.planedCompletionDate
              ? new Date(item.planedCompletionDate)
              : null,
            actualCompletionDate: item.actualCompletionDate
              ? new Date(item.actualCompletionDate)
              : null,
          });
        });
      }
      const countReassign = this._getLastReviewAndCountReassign(
        IARFound.internalAuditReportHistories,
      );
      const submiter = IARFound.userAssignments.filter(
        (user) => user.permission === WorkflowPermission.CREATOR && user.userId == token.id,
      );
      const isSubmitterSave =
        countReassign.countReassign - countReassign.maxReview == 1 && submiter.length > 0;

      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      return this.connection.transaction(async (manager) => {
        if (query.isSubmitted && query.isSubmitted.toString() === 'false') {
          if (
            IARFound.status === InternalAuditReportStatus.DRAFT ||
            (IARFound.status === InternalAuditReportStatus.REASSIGNED && isSubmitterSave)
          ) {
            await manager.update(
              InternalAuditReport,
              {
                id: internalAuditReportId,
                companyId: token.companyId,
              },
              {
                updatedUserId: token.id,
                attachments: params.attachments,
                coverImage: params.coverImage ? params.coverImage : null,
                status: InternalAuditReportStatus.DRAFT,
                previousStatus: isSubmitterSave
                  ? IARFound.previousStatus
                  : InternalAuditReportStatus.DRAFT,
                workflowRemarks: params.workflowRemarks,
                background: params.background,
              },
            );
          }

          if (
            IARFound.status === InternalAuditReportStatus.SUBMITTED ||
            IARFound.status === InternalAuditReportStatus.APPROVED ||
            IARFound?.status?.includes('reviewed')
          ) {
            await manager.update(
              InternalAuditReport,
              {
                id: internalAuditReportId,
                companyId: token.companyId,
              },
              {
                updatedUserId: token.id,
                coverImage: params.coverImage ? params.coverImage : null,
                attachments: params.attachments,
                workflowRemarks: params.workflowRemarks,
                background: params.background,
              },
            );
          }
        }

        const preparedIARHistory = {
          internalAuditReportId,
          remark: params.remark,
          status: isSubmitterSave ? InternalAuditReportStatus.DRAFT : IARFound.status, // IAR draft or reassign --> create history DRAFT
          createdUser,
        };
        if (query.isSubmitted && query.isSubmitted.toString() === 'true') {
          // status history
          preparedIARHistory.status = InternalAuditReportStatus.SUBMITTED;
          // Update IAR status --> SUBMITTED
          await manager.update(
            InternalAuditReport,
            {
              // reportFindingFormId: IARFound.reportFindingFormId,
              id: IARFound.id,
              companyId: token.companyId,
              // status: InternalAuditReportStatus.DRAFT,
            },
            {
              updatedUserId: token.id,
              attachments: params.attachments,
              coverImage: params.coverImage ? params.coverImage : null,
              status: InternalAuditReportStatus.SUBMITTED,
              previousStatus: InternalAuditReportStatus.DRAFT,
              workflowRemarks: params.workflowRemarks,
              background: params.background,
            },
          );

          //duplicate CAR here
          // if (IARFound.reportFindingFormId) {
          //   await this.manager
          //     .getCustomRepository(ReportFindingFormRepository)
          //     ._triggerCreateCar(manager, token, IARFound.reportFindingFormId);
          // }

          // trigger update status report finding form
          await manager
            .getCustomRepository(ReportFindingFormRepository)
            ._triggerFindingManagementStatus(
              IARFound.reportFindingFormId,
              ReportFindingFormStatus.Under_Review,
            );

          // No need to check the below condition
          // Update global status
          // if (
          //   this.manager
          //     .getCustomRepository(CAPRepository)
          //     ._checkNeedUpdateGlobalStatus(
          //       IARFound.planningRequest.globalStatus,
          //       GlobalStatusEnum.SUBMITTED_REPORT,
          //     )
          // ) {
          await manager.update(
            PlanningRequest,
            {
              id: IARFound.planningRequestId,
            },
            {
              globalStatus: GlobalStatusEnum.SUBMITTED_REPORT,
            },
          );
          // }
          if (preparedIARHistory.status !== IARFound.status) {
            await manager.insert(InternalAuditReportHistory, preparedIARHistory);
          }
          if (params.userAssignment) {
            // Update user assignment
            await manager
              .getCustomRepository(UserAssignmentRepository)
              .updateUserAssignment(
                manager,
                ModuleType.INTERNAL_AUDIT_REPORT,
                IARFound.id,
                params.userAssignment.usersPermissions,
              );
          }
        }

        await Promise.all([
          // update comment
          manager.save(InternalAuditReportComment, preparedComment),
          // create IAR header description
          manager.save(IARReportHeaderDescription, listHeaderDescriptionsCreate),
          // update IAR header description
          manager.save(IARReportHeaderDescription, listHeaderDescriptionsUpdate),
          //update IAR report header
          manager.save(IARReportHeader, preparedIARReportHeaders),
          // create new Office Comment
          manager.save(InternalAuditReportOfficeComment, listIAROfficeCommentCreate),
          // update existed Office Comment
          manager.save(InternalAuditReportOfficeComment, listIAROfficeCommentUpdate),
          // delete old Office Comment
          manager.delete(InternalAuditReportOfficeComment, {
            id: In(Array.from(listIAROfficeCommentDeleteIds)),
          }),
          // delete old IAR header description
          manager.delete(IARReportHeaderDescription, {
            id: In(Array.from(listHeaderDescriptionDeleteIds)),
          }),
          // Update OBS, NC
          manager.save(ReportFindingItem, prepareROFItems),
        ]);

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_REPORT,
            planningId: IARFound.planningRequestId,
          },
          null,
          [
            preparedIARHistory.status === InternalAuditReportStatus.SUBMITTED
              ? AuditActivityEnum.SUBMITTED
              : AuditActivityEnum.UPDATED_INFO,
          ],
          createdUser,
        );
        //#region Handle push noti
        // list User Assignment
        const listUserAssignment = await manager
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.INTERNAL_AUDIT_REPORT, IARFound.id);
        let listReceiverNoti = [];
        listReceiverNoti = listReceiverNoti.concat(
          listUserAssignment[WorkflowPermission.REVIEWER1],
        );
        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle', 'email']);
        dataNoti.push({
          receivers: listReceiverNoti,
          module: ModuleType.INTERNAL_AUDIT_REPORT,
          recordId: IARFound.id,
          recordRef: IARFound.refId,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: InternalAuditReportStatus.SUBMITTED,
          previousStatus: IARFound.status,
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNoti) {
          dataSendMail.push({
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: IARFound.refId,
              recordId: IARFound.id,
              path: ModulePathEnum.INTERNAL_AUDIT_REPORT,
              currentStatus: InternalAuditReportStatus.SUBMITTED,
              previousStatus: IARFound.status,
              performer: performer,
              executedAt: new Date(),
            },
          });
        }

        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      LoggerCommon.error('[InternalAuditReportRepository] submitIAR error ', ex.message || ex);
      throw ex;
    }
  }

  async listInternalAuditReport(
    query: ListInternalAuditReportDTO,
    token: TokenPayloadModel,
    body?: PayloadAGGridDto,
  ) {
    const pageSize = Number(query.pageSize) || ConstLib.PAGE_SIZE;
    const page = Number(query.page) || ConstLib.PAGE_NUMBER;

    const queryBuilder = this.createQueryBuilder('internalAuditReport')
      .where(
        `(((internalAuditReport.companyId = '${token.companyId}') AND (iarUsers.relationship = 'leadAuditor'))
        OR ((internalAuditReport.companyId = '${token.companyId}') AND (iarUsers.relationship = 'leadAuditor') AND (internalAuditReport.reportFindingFormId IS NULL))) AND 
        internalAuditReport.deleted = false`,
      )
      .leftJoin('internalAuditReport.reportFindingForm', 'reportFindingForm')
      .leftJoin('internalAuditReport.iarPlanningRequest', 'iarPlanningRequest')
      .leftJoin('iarPlanningRequest.country', 'country')
      .leftJoin('internalAuditReport.iarUsers', 'iarUsers')
      .leftJoin(
        'internalAuditReport.reportFindingItems',
        'reportFindingItems',
        'reportFindingItems.deleted = false',
      )
      .leftJoin('reportFindingItems.natureFinding', 'natureFinding')
      //.leftJoin('reportFindingForm.planningRequest', 'planningRequest')
      .leftJoin('internalAuditReport.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditWorkspace', 'auditWorkspace')
      .leftJoin('planningRequest.vessel', 'vesselPlan')
      .leftJoin(
        'vesselPlan.vesselDocHolders',
        'vesselDocHolders',
        `vesselDocHolders.vesselId = vesselPlan.id AND vesselDocHolders.status = 'active'`,
      )
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      .leftJoin('planningRequest.auditors', 'auditors')
      .leftJoin('internalAuditReport.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
      .leftJoin('planningRequest.auditTypes', 'auditTypes')
      .leftJoin('planningRequest.voyageType', 'voyageType')
      // .leftJoin('internalAuditReport.reportFindingItems', 'reportFindingItemsOfIar')
      .leftJoin('internalAuditReport.company', 'company')
      // .leftJoin(
      //   `(SELECT "internalAuditReportId" AS "id", string_agg(x->>'name', ', ') AS "departmentNames"
      //   FROM iar_planning_request ipr 
      //   CROSS JOIN jsonb_array_elements(ipr.departments) x(element) 
      //   GROUP BY "internalAuditReportId")`,
      //   'internalAuditReportDepartments',
      //   `"internalAuditReportDepartments"."id" = "iarPlanningRequest"."internalAuditReportId"`,
      // )
      .select()
      .addSelect([
        `ARRAY_AGG(DISTINCT "reportFindingItems"."picId" ) AS "picIdList"`,
        'companyVesselDocHolders.name AS "holderCompany"',
        'planningRequest.auditNo AS "prAuditNo"',
        'planningRequest.refId AS "prRefId"',
        'planningRequest.globalStatus',
        'planningRequest.plannedFromDate',
        'planningRequest.plannedToDate',
        'planningRequest.leadAuditorId',
        'planningRequest.customerRef',
        'planningRequest.auditTypesName AS "auditTypesName"',
        'auditWorkspace.submittedDate',
        'auditWorkspace.submittedDate',
        'auditWorkspace.submittedDate_Year',
        'auditWorkspace.submittedDate_Month',
        'auditWorkspace.mobileInspectionStartDate',
        'auditWorkspace.mobileInspectionEndDate',
        'iarPlanningRequest.vesselName AS "vesselName"',
        'iarPlanningRequest.vesselTypeName AS "vesselTypeName"',
        'iarPlanningRequest.countryId',
        'iarPlanningRequest.country',
        'country.id',
        'country.name AS "countryFlag"',
        'iarPlanningRequest.fleetName AS "fleetName"',
        'iarPlanningRequest.auditCompanyName AS "auditCompanyName"',
        'iarPlanningRequest.departmentName AS "departmentName"',
        'iarPlanningRequest.departments AS "departments"',
        'iarUsers.username AS "leadAuditorName"',
        'reportFindingForm.totalFindings AS "totalFindings"',
        'reportFindingForm.totalNonConformity AS "totalNonConformity"',
        'reportFindingForm.totalObservation AS "totalObservation"',
        `COUNT ( DISTINCT CASE WHEN "reportFindingItems"."isOpen" = true AND "natureFinding"."name" = 'Non Conformity' THEN "reportFindingItems"."id" END ) AS "totalOpenNC"`,
        `COUNT ( DISTINCT CASE WHEN "reportFindingItems"."isOpen" = false AND "natureFinding"."name" = 'Non Conformity' THEN "reportFindingItems"."id" END ) AS "totalCloseNC"`,
        `COUNT ( DISTINCT CASE WHEN "reportFindingItems"."isOpen" = true AND "natureFinding"."name" = 'Observation' THEN "reportFindingItems"."id" END ) AS "totalOpenOBS"`,
        `COUNT ( DISTINCT CASE WHEN "reportFindingItems"."isOpen" = false AND "natureFinding"."name" = 'Observation' THEN "reportFindingItems"."id" END ) AS "totalCloseOBS"`,
        `COUNT ( DISTINCT CASE WHEN "reportFindingItems"."findingStatus"::text = 'Opened' THEN "reportFindingItems"."id" END ) AS "numberNotCloseRoF"`,
        'auditTimeTable.actualFrom AS "actualFrom"',
        'auditTimeTable.actualTo AS "actualTo"',
        'auditTimeTable.actualFrom_Year AS "actualFrom_Year"',
        'auditTimeTable.actualTo_Year AS "actualTo_Year"',
        'auditTimeTable.actualFrom_Month AS "actualFrom_Month"',
        'auditTimeTable.actualTo_Month AS "actualTo_Month"',
        'company.id',
        'company.name',
        'voyageType.name',
      ])
      .groupBy('internalAuditReport.id')
      .addGroupBy('reportFindingForm.id')
      .addGroupBy('reportFindingForm.totalFindings')
      .addGroupBy('reportFindingForm.totalNonConformity')
      .addGroupBy('reportFindingForm.totalObservation')
      .addGroupBy('planningRequest.id')
      .addGroupBy('auditTimeTable.id')
      .addGroupBy('planningRequest.refId')
      .addGroupBy('planningRequest.auditNo')
      .addGroupBy('auditWorkspace.submittedDate')
      .addGroupBy('auditWorkspace.submittedDate_Year')
      .addGroupBy('auditWorkspace.submittedDate_Month')
      .addGroupBy('auditWorkspace.mobileInspectionStartDate')
      .addGroupBy('auditWorkspace.mobileInspectionEndDate')
      .addGroupBy('auditTimeTable.actualFrom')
      .addGroupBy('auditTimeTable.actualTo')
      .addGroupBy('iarPlanningRequest.vesselName')
      .addGroupBy('iarPlanningRequest.vesselTypeName')
      // .addGroupBy('iarPlanningRequest.countryFlag')
      .addGroupBy('iarPlanningRequest.countryId')
      .addGroupBy('iarPlanningRequest.fleetName')
      .addGroupBy('iarPlanningRequest.auditCompanyName')
      .addGroupBy('iarPlanningRequest.departmentName')
      .addGroupBy('iarPlanningRequest.departments')
      // .addGroupBy(`"internalAuditReportDepartments"."departmentNames"`)
      .addGroupBy('company.id')
      .addGroupBy('company.name')
      .addGroupBy('iarUsers.id')
      .addGroupBy('country.id')
      .addGroupBy('companyVesselDocHolders.id')
      .addGroupBy('voyageType.name');

    if (!RoleScopeCheck.isAdmin(token)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        token.explicitCompanyId,
        'internalAuditReport',
      );
      queryBuilder
        .leftJoin('vesselPlan.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users')
        .leftJoin('vesselPlan.vesselOwners', 'vesselOwners')
        .leftJoin('vesselOwners.company', 'companyVesselOwnersPlans')
        .leftJoin('vesselPlan.vesselCharterers', 'vesselCharterers')
        .leftJoin('vesselCharterers.company', 'companyVesselCharterers');
      const isGetList = query.entityType && query.entityType === AuditEntity.VESSEL;
      await _supportCheckRoleScopeForGetList(
        this.manager,
        queryBuilder,
        token,
        whereForMainAndInternal,
        whereForExternal,
        'internalAuditReport',
        false,
        isGetList,
      );
    }

    if (query.content) {
      queryBuilder.andWhere('(internalAuditReport.refId ILIKE :content)', {
        content: `%${query.content}%`,
      });
    }

    if (query.planningFrom) {
      queryBuilder.andWhere(
        `planningRequest.plannedFromDate >= '${new Date(query.planningFrom).toISOString()}'`,
      );
    }

    if (query.planningTo) {
      queryBuilder.andWhere(
        `planningRequest.plannedFromDate <= '${new Date(query.planningTo).toISOString()}'`,
      );
    }

    if (query.status) {
      queryBuilder.andWhere(`internalAuditReport.status = '${query.status}'`);
    }

    if (query.entityType) {
      queryBuilder.andWhere(`internalAuditReport.entityType = '${query.entityType}'`);
    }

    if (query.fromDate) {
      const fromDate = new Date(query.fromDate).toISOString();
      queryBuilder.andWhere(`auditTimeTable.actualFrom >= '${fromDate}'`);
    }

    if (query.toDate) {
      const toDate = new Date(query.toDate).toISOString();
      queryBuilder.andWhere(`auditTimeTable.actualTo <= '${toDate}'`);
    }

    if (query.createdAtFrom) {
      queryBuilder.andWhere(
        `internalAuditReport.createdAt >= '${new Date(query.createdAtFrom).toISOString()}'`,
      );
    }

    if (query.createdAtTo) {
      queryBuilder.andWhere(
        `internalAuditReport.createdAt <= '${new Date(query.createdAtTo).toISOString()}'`,
      );
    }

    if (query.ids?.length) {
      queryBuilder.andWhere(`"internalAuditReport"."id" IN (${query.ids.map((id) => `'${id}'`)})`);
    }

    let count;
    let listIAR;
    let dataFilters;
    if (body) {
      convertFilterField(body, INTERNAL_AUDIT_REPORT_FILTER_FIELDS);
      queryBuilder.distinctOn(['internalAuditReport.id']);
      const connection = getConnection();
      const subQueryBuilder = connection.createQueryBuilder();
      subQueryBuilder.select().from(`(${queryBuilder.getQuery()})`, 'subquery');

      createWhereSql(body, subQueryBuilder);
      createSelectSql(body, subQueryBuilder, null, '"internalAuditReport_id"');

      const sort = customSort(body?.sortModel);
      if (isDoingGrouping(body)) {
        return await handleResponse(body, subQueryBuilder, query, sort, 'internalAuditReport');
      } else {
        const pageSize = query?.pageSize > 5 ? query?.pageSize : 100;
        const pageNumber = query?.page || query?.page > 1 ? query?.page : 1;
        // count = (await subQueryBuilder.getRawMany()).length;
        // if (query?.pageSize >= 0) {
        //   subQueryBuilder.offset((query?.page - 1) * query?.pageSize || 0);
        //   subQueryBuilder.limit(query?.pageSize);
        // }
        const sortType = body?.sortModel[0]?.sort || 'DESC';
        const col = body?.sortModel[0]?.colId || `"internalAuditReport_createdAt"`;
        subQueryBuilder.orderBy(col, sortType);
        // listIAR = await subQueryBuilder.getRawMany();
        dataFilters = await subQueryBuilder.getRawMany();
        if (pageSize >= 0) {
          const start = (pageNumber - 1) * pageSize;
          const end = start + pageSize;
          listIAR = dataFilters?.slice(start, end);
        }
      }
    } else {
      // count = await queryBuilder.getCount();
      // if (pageSize > 0) {
      //   queryBuilder.limit(pageSize).offset((page - 1) * pageSize);
      // }
      const pageSize = query?.pageSize > 5 ? query?.pageSize : 100;
      const pageNumber = query?.page || query?.page > 1 ? query?.page : 1;
      if (query.sort) {
        const sortValues: string[] = query.sort.split(';');
        sortValues.forEach((sv: string) => {
          const value: string[] = sv.split(':');
          if (value[0].includes('.')) {
            queryBuilder.addOrderBy(value[0], Number(value[1]) === 1 ? 'ASC' : 'DESC');
          } else {
            queryBuilder.addOrderBy(
              `internalAuditReport.${value[0]}`,
              Number(value[1]) === 1 ? 'ASC' : 'DESC',
            );
          }
        });
      } else if (!query.sort) {
        queryBuilder.addOrderBy('internalAuditReport.createdAt', 'DESC');
      }
      dataFilters = await queryBuilder.getRawMany();
      if (pageSize >= 0) {
        const start = (pageNumber - 1) * pageSize;
        const end = start + pageSize;
        listIAR = dataFilters?.slice(start, end);
      }
    }

    const listIARIds = listIAR?.map((iar) => iar?.internalAuditReport_id);

    if (listIARIds.length > 0) {
      const [listIarAssignments, iarHistories] = await Promise.all([
        this.manager
          .createQueryBuilder(UserAssignment, 'userAssignment')
          .leftJoin('userAssignment.user', 'user')
          .where('"userAssignment"."internalAuditReportId" IN (:...listIARIds)', { listIARIds })
          .select()
          .addSelect(['user.id', 'user.username'])
          .getMany(),
        this.manager
          .createQueryBuilder(InternalAuditReportHistory, 'internalAuditReportHistory')
          .leftJoin('internalAuditReportHistory.internalAuditReport', 'internalAuditReport')
          .where('"internalAuditReportHistory"."internalAuditReportId" IN (:...listIARIds)', {
            listIARIds,
          })
          .select()
          .getMany(),
      ]);

      const listIARVesselIds = listIAR?.map((iar) => iar?.internalAuditReport_vesselId);
      const [vesselDocHolders, vesselCharterers, vesselOwners] = await Promise.all(
        !listIARVesselIds.length
          ? [[], [], []]
          : [
              this.manager
                .getCustomRepository(VesselDocHolderRepository)
                .createQueryBuilder('VesselDocHolder')
                .leftJoin('VesselDocHolder.company', 'companyVesselDocHolder')
                .where(`(VesselDocHolder.vesselId IN (:...listIARVesselIds))`, {
                  listIARVesselIds,
                })
                .select()
                .addSelect(['companyVesselDocHolder.name'])
                .getMany(),
              this.manager
                .getCustomRepository(VesselChartererRepository)
                .createQueryBuilder('VesselCharterer')
                .where(`(VesselCharterer.vesselId IN (:...listIARVesselIds))`, {
                  listIARVesselIds,
                })
                .getMany(),
              this.manager
                .getCustomRepository(VesselOwnerRepository)
                .createQueryBuilder('VesselOwner')
                .where(`(VesselOwner.vesselId IN (:...listIARVesselIds))`, {
                  listIARVesselIds,
                })
                .getMany(),
            ],
      );

      listIAR = listIAR.map((iar) => {
        return {
          ...iar,
          userAssignments: listIarAssignments.filter(
            (assignment) => assignment.internalAuditReportId == iar.internalAuditReport_id,
          ),
          iarHistories: iarHistories.filter(
            (iarHistory) => iarHistory.internalAuditReportId == iar.internalAuditReport_id,
          ),
          vesselDocHolders: vesselDocHolders.filter(
            (vesselDocHolder) => vesselDocHolder.vesselId == iar.internalAuditReport_vesselId,
          ),
          vesselCharterers: vesselCharterers.filter(
            (vesselCharterer) => vesselCharterer.vesselId == iar.internalAuditReport_vesselId,
          ),
          vesselOwners: vesselOwners.filter(
            (vesselOwner) => vesselOwner.vesselId == iar.internalAuditReport_vesselId,
          ),
        };
      });
    }

    if (pageSize > 0) {
      return {
        data: listIAR,
        page,
        pageSize,
        totalItem: dataFilters?.length,
        totalPage: Math.ceil(dataFilters?.length / pageSize),
      };
    } else {
      return { data: listIAR };
    }
  }

  async listReassignedIar(query: ListInternalAuditReportDTO, user: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('internalAuditReport')
      .leftJoinAndSelect('internalAuditReport.internalAuditReportHistories', 'iarHistories')
      .leftJoin('internalAuditReport.iarPlanningRequest', 'iarPlanningRequest')
      .leftJoin('internalAuditReport.company', 'company')
      .leftJoin('internalAuditReport.planningRequest', 'planningRequest')
      .leftJoin('planningRequest.auditCompany', 'auditCompany')
      .leftJoin('planningRequest.vessel', 'vesselPlan')
      .leftJoin('vesselPlan.vesselDocHolders', 'vesselDocHolders')
      .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
      // .leftJoin('iar.vessel', 'vessel')
      .select([
        'internalAuditReport.id',
        'internalAuditReport.refId',
        'internalAuditReport.serialNumber',
        'internalAuditReport.status',
        'internalAuditReport.createdAt',
        'internalAuditReport.updatedAt',
        'internalAuditReport.companyId',
      ])
      // .addSelect(['vessel.id', 'vessel.name'])
      .addSelect([
        'planningRequest.id',
        'auditCompany.name',
        'iarPlanningRequest.vesselId',
        'iarPlanningRequest.vesselName',
        'iarPlanningRequest.departmentId',
        'iarPlanningRequest.departmentName',
        'iarPlanningRequest.departments',
        'iarPlanningRequest',
        'iarHistories.id',
        'iarHistories.status',
        'iarHistories.remark',
        'iarHistories.createdAt',
        'company.name',
        'vesselDocHolders.id',
        'vesselDocHolders.companyId',
        'vesselDocHolders.fromDate',
        'vesselDocHolders.toDate',
        'vesselDocHolders.responsiblePartyInspection',
        'vesselDocHolders.responsiblePartyQA',
        'vesselDocHolders.status',
        'companyVesselDocHolders.name',
      ])
      .where('internalAuditReport.companyId = :companyId', {
        companyId: user.companyId,
      });

    if (query.status) {
      queryBuilder.andWhere('internalAuditReport.status = :status', {
        status: query.status,
      });
    } else {
      queryBuilder.andWhere('internalAuditReport.status = :status', {
        status: InternalAuditReportStatus.REASSIGNED,
      });
    }

    if (query.fromDate) {
      queryBuilder.andWhere(`internalAuditReport.createdAt >= :fromDate`, {
        fromDate: new Date(query.fromDate),
      });
    }

    if (query.toDate) {
      queryBuilder.andWhere(`internalAuditReport.createdAt <= :toDate`, {
        toDate: new Date(query.toDate),
      });
    }

    if (query.entityType) {
      queryBuilder.andWhere('internalAuditReport.entityType = :entityType', {
        entityType: query.entityType,
      });
    }

    if (!RoleScopeCheck.isAdmin(user)) {
      const { whereForExternal, whereForMainAndInternal } = await _supportWhereDOCChartererOwner(
        this.manager,
        user.explicitCompanyId,
        'internalAuditReport',
      );
      queryBuilder
        // .leftJoin('planningRequest.vessel', 'vesselPlan')
        .leftJoin('planningRequest.auditors', 'auditors')
        .leftJoin('internalAuditReport.userAssignments', 'userAssignments')
        .leftJoin('userAssignments.user', 'user')
        .leftJoin('vesselPlan.divisionMapping', 'divisionMapping')
        .leftJoin('divisionMapping.division', 'division')
        .leftJoin('division.users', 'users')
        // .leftJoin('vesselPlan.vesselDocHolders', 'vesselDocHolders')
        // .leftJoin('vesselDocHolders.company', 'companyVesselDocHolders')
        .leftJoin('vesselPlan.vesselOwners', 'vesselOwners')
        .leftJoin('vesselPlan.vesselCharterers', 'vesselCharterers');
      await _supportCheckRoleScopeForGetList(
        this.manager,
        queryBuilder,
        user,
        whereForMainAndInternal,
        whereForExternal,
        'internalAuditReport',
      );
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'internalAuditReport.createdAt:-1;iarHistories.createdAt:1',
      },
    );
  }

  async getDetailIARById(id: string, token: TokenPayloadModel, isForPdf?: boolean) {
    try {
      const reportFindings = await this.createQueryBuilder('internalAuditReport')
        .leftJoin('internalAuditReport.reportFindingForm', 'reportFindingForm')
        .leftJoin('internalAuditReport.planningRequest', 'planningRequest')
        .where('(internalAuditReport.id = :id AND internalAuditReport.deleted = :deleted)', {
          id,
          deleted: false,
        })
        .select()
        .addSelect(['reportFindingForm.id', 'planningRequest.isSA'])
        .getOne();

      let rofItemsWithchkQuestionIds = [];
      let rofItemsWithoutchkQuestionIds = [];
      let totalFindings = 0;
      let rofItems;
      if (!reportFindings?.planningRequest?.isSA) {
        rofItems = await this.manager
          .getCustomRepository(ReportFindingItemRepository)
          .listFindingItemsByPR(reportFindings?.planningRequestId, token);

        // Pass distinct findings which are original findings if any duplicate findings have
        rofItemsWithchkQuestionIds = rofItems.filter((X) => X.chkQuestionId !== null);
        rofItemsWithoutchkQuestionIds = rofItems.filter((X) => X.chkQuestionId === null);
        totalFindings = new Set(rofItemsWithchkQuestionIds.map((X) => X.chkQuestionId)).size;
      } else {
        rofItems = await this.manager
          .getCustomRepository(FillSAChecklistQuestionRepository)
          .listFindingItemsByPR(reportFindings?.planningRequestId, token);

        // Pass distinct findings which are original findings if any duplicate findings have
        rofItemsWithchkQuestionIds = rofItems.filter((X) => X.elementMasterId !== null);
        rofItemsWithoutchkQuestionIds = rofItems.filter((X) => X.elementMasterId === null);
        totalFindings = new Set(rofItemsWithchkQuestionIds.map((X) => X.elementMasterId)).size;
      }

      if (reportFindings) {
        totalFindings = totalFindings + rofItemsWithoutchkQuestionIds.length;

        await this.manager.getCustomRepository(ReportFindingFormRepository).update(
          {
            id: reportFindings?.reportFindingForm?.id,
          },
          {
            totalFindings: totalFindings,
            totalNonConformity: rofItems.filter(
              (item) =>
                item?.natureFinding?.name === AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
            ).length,
            totalObservation: rofItems.filter(
              (item) => item?.natureFinding?.name === AppConst.NATURE_FINDING_DEFAULT.OBSERVATION,
            ).length,
          },
        );
      }
      const queryBuilder = this.createQueryBuilder('internalAuditReport')
        .leftJoin('internalAuditReport.reportFindingForm', 'reportFindingForm')
        //.leftJoin('reportFindingForm.planningRequest', 'planningRequest')
        .leftJoin('internalAuditReport.planningRequest', 'planningRequest')
        .leftJoin('planningRequest.auditTypes', 'auditTypes')
        .leftJoin('auditTypes.inspectionMapping', 'inspectionMapping')
        .leftJoin('planningRequest.leadAuditor', 'leadAuditor')
        .leftJoin('planningRequest.auditWorkspace', 'auditWorkspace')
        .leftJoin('planningRequest.auditCompany', 'auditCompany')
        .leftJoin('planningRequest.location', 'location')
        .leftJoinAndSelect('internalAuditReport.iarPlanningRequest', 'iarPlanningRequest')
        .leftJoin('iarPlanningRequest.country', 'country')
        .leftJoinAndSelect('internalAuditReport.iarAuditTypes', 'iarAuditTypes')
        .leftJoinAndSelect('internalAuditReport.iarUsers', 'iarUsers')
        .leftJoin('iarUsers.user', 'iarUser')
        .leftJoin('internalAuditReport.vessel', 'vessel')
        .leftJoin('internalAuditReport.company', 'company')
        .leftJoin('planningRequest.auditTimeTable', 'auditTimeTable')
        .leftJoin('planningRequest.voyageType', 'voyageType')
        .leftJoinAndSelect(
          'internalAuditReport.internalAuditReportComment',
          'internalAuditReportComment',
        )
        // .leftJoinAndSelect(
        //   'internalAuditReport.internalAuditReportHistories',
        //   'internalAuditReportHistories',
        // )
        // .leftJoinAndSelect(
        //   'internalAuditReport.internalAuditReportOfficeComments',
        //   'internalAuditReportOfficeComments',
        // )
        // .leftJoinAndSelect('internalAuditReport.IARReportHeaders', 'IARReportHeaders')
        // .leftJoin('IARReportHeaders.reportHeader', 'reportHeader')
        // .orderBy('IARReportHeaders.serialNumber', 'ASC')
        // .leftJoinAndSelect(
        //   'IARReportHeaders.IARReportHeaderDescriptions',
        //   'IARReportHeaderDescriptions',
        // )
        .leftJoin('internalAuditReport.userAssignments', 'userAssignments')
        .leftJoin('userAssignments.user', 'user')
        .leftJoin('user.company', 'userCompany')
        .leftJoin('user.divisions', 'userDivisions')
        .where(
          '(internalAuditReport.id = :id AND internalAuditReport.deleted = :deleted AND (internalAuditReport.companyId = :companyId OR company.parentId = :companyId))',
          {
            id,
            deleted: false,
            companyId: token.companyId,
          },
        )
        .select()
        .addSelect([
          'reportFindingForm.id',
          'reportFindingForm.refNo',
          'reportFindingForm.status',
          'reportFindingForm.totalFindings',
          'reportFindingForm.totalNonConformity',
          'reportFindingForm.totalObservation',
          'reportFindingForm.planningRequestId',
          'planningRequest.id',
          'planningRequest.status',
          'planningRequest.refId',
          'planningRequest.typeOfAudit',
          'planningRequest.globalStatus',
          'planningRequest.workingType',
          'planningRequest.plannedFromDate',
          'planningRequest.plannedToDate',
          'planningRequest.customerRef',
          'planningRequest.isSA',
          'auditCompany.name',
          'auditCompany.isCompanyRestricted',
          'auditWorkspace.id',
          'auditWorkspace.mobileInspectionStartDate',
          'auditWorkspace.mobileInspectionEndDate',
          'auditWorkspace.inspectionMappingIds',
          'location.id',
          'location.name',
          'vessel.vesselClass',
          'vessel.deadWeightTonnage',
          'vessel.imoNumber',
          'vessel.isVesselRestricted',
          'vessel.isCompanyRestricted',
          'company.name',
          'company.id',
          'auditTimeTable.actualFrom',
          'auditTimeTable.actualTo',
          // 'internalAuditReportOfficeComments.id',
          // 'internalAuditReportOfficeComments.comment',
          // 'IARReportHeaders.printOption',
          'userAssignments.id',
          'userAssignments.permission',
          'user.id',
          'user.username',
          'user.jobTitle',
          'user.roles',
          'userCompany.name',
          'userCompany.code',
          'userDivisions.name',
          'userDivisions.code',
          'iarPlanningRequest.countryId',
          'iarPlanningRequest.country',
          'country.id',
          'country.name',
          'iarUser.email',
          'leadAuditor.username',
          'voyageType.id',
          'voyageType.name',
          'auditTypes.name',
          'inspectionMapping.isSA'
        ]);
      // .addOrderBy('internalAuditReportHistories.createdAt', 'ASC');

      const qbIARHistories = this.manager
        .getCustomRepository(InternalAuditReportHistoryRepository)
        .createQueryBuilder('internalAuditReportHistories')
        .where(`(internalAuditReportHistories.internalAuditReportId = :id)`, {
          id,
        })
        .orderBy('"internalAuditReportHistories"."createdAt"', 'ASC');

      const qbIAROfficeComments = this.manager
        .getCustomRepository(InternalAuditReportOfficeCommentRepository)
        .createQueryBuilder('internalAuditReportOfficeComments')
        .where(`(internalAuditReportOfficeComments.internalAuditReportId = :id)`, {
          id,
        });

      const printOptions = [];
      if (token.companyLevel === 'External Company') {
        printOptions.push('External', 'All');
      } else if (token.companyLevel === 'Internal Company') {
        printOptions.push('Internal', 'All');
      } else {
        printOptions.push('External', 'Internal', 'All');
      }
      // iar report header
      const qbIARHeaders = await this.manager
        .getCustomRepository(IARReportHeaderRepository)
        .createQueryBuilder('IARReportHeaders')
        .leftJoin('IARReportHeaders.reportHeader', 'reportHeader')
        .orderBy(`NULLIF("IARReportHeaders"."serialNumber", '')::TEXT`, 'ASC')
        .leftJoinAndSelect(
          'IARReportHeaders.IARReportHeaderDescriptions',
          'IARReportHeaderDescriptions',
        )
        .where(
          `(IARReportHeaders.internalAuditReportId = :id) and IARReportHeaders.printOption IN (:...printOption)`,
          {
            id,
            printOption: printOptions,
          },
        );
      if (isForPdf) {
        qbIARHeaders.andWhere(`IARReportHeaders.isPrint = :isPrint`, {
          isPrint: true,
        });
      }
      const [IARFound, iarHistories, iarComments, iarHeaders] = await Promise.all([
        queryBuilder.getOne(),
        qbIARHistories.getMany(),
        qbIAROfficeComments.getMany(),
        qbIARHeaders.getMany(),
      ]);

      if (!IARFound?.planningRequest?.isSA) {
        delete IARFound?.planningRequest?.auditTypes;
      }

      const inspectionCompany = await this.connection
        .getCustomRepository(UserRepository)
        .createQueryBuilder('user')
        .leftJoin('user.company', 'company')
        .addSelect(['user.id', 'company.name'])
        .where('user.username = :username', {
          username: IARFound?.planningRequest?.leadAuditor?.username,
        })
        .getOne();
      if (inspectionCompany) {
        Object.assign(IARFound, { inspectionCompany: inspectionCompany?.company?.name });
      }
      if (IARFound) {
        let nonConformities;
        let observations;
        let otherFindingTypes;
        let lastAuditFindings;
        if (IARFound.reportFindingFormId) {
          const [listNC, listOBS, listOtherFindings, listLastAuditFinding] = await Promise.all([
            this.connection.getCustomRepository(ReportFindingItemRepository).listFindingItemOfForm(
              null,
              {
                natureFinding: AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
                internalAuditReportId: id,
                pageSize: -1,
              },
              token,
              IARFound?.planningRequest?.isSA || false,
            ),
            // this.connection
            //   .getCustomRepository(ReportFindingItemRepository)
            //   .listPreviousOpenNCItemsOfIARByVessel(IARFound.reportFindingFormId, {}, token),
            this.connection.getCustomRepository(ReportFindingItemRepository).listFindingItemOfForm(
              IARFound.reportFindingFormId,
              {
                natureFinding: AppConst.NATURE_FINDING_DEFAULT.OBSERVATION,
                internalAuditReportId: id,
                pageSize: -1,
              },
              token,
              IARFound?.planningRequest?.isSA || false,
            ),
            this.connection.getCustomRepository(ReportFindingItemRepository).listFindingItemOfForm(
              IARFound.reportFindingFormId,
              {
                natureFinding: AppConst.NATURE_FINDING_DEFAULT.OTHER,
                internalAuditReportId: id,
                pageSize: -1,
              },
              token,
              IARFound?.planningRequest?.isSA || false,
            ),
            this.connection
              .getCustomRepository(ReportFindingItemRepository)
              .listPreviousOpenNCItemsOfIARByVessel(IARFound.reportFindingForm.id, null, token),
          ]);

          nonConformities = listNC;
          observations = listOBS;
          otherFindingTypes = listOtherFindings;
          lastAuditFindings = listLastAuditFinding;
        }
        if (IARFound.userAssignments.length) {
          const userIds = IARFound.userAssignments.map((userAssignment) => userAssignment.user?.id);
          if (userIds.length) {
            const roleMapByUser = await this.manager
              .getCustomRepository(UserRepository)
              .getUserRoles(userIds);
            IARFound.userAssignments.forEach((userAssignment) => {
              const roles = roleMapByUser.has(userAssignment.user?.id)
                ? roleMapByUser.get(userAssignment.user.id)
                : [];
              Object.assign(userAssignment.user.roles, roles);
            });
          }
        }

        if (IARFound.vesselId) {
          const queryVesselDocHolders = this.manager
            .getCustomRepository(VesselDocHolderRepository)
            .createQueryBuilder('VesselDocHolder')
            .leftJoin('VesselDocHolder.company', 'company')
            .select()
            .addSelect(['company.name', 'company.code'])
            .where(`(VesselDocHolder.vesselId = :vesselId) AND VesselDocHolder.status = 'active'`, {
              vesselId: IARFound.vesselId,
            });
          const queryVesselCharterers = this.manager
            .getCustomRepository(VesselChartererRepository)
            .createQueryBuilder('VesselCharterer')
            .where(`(VesselCharterer.vesselId = :vesselId)`, {
              vesselId: IARFound.vesselId,
            });
          const queryVesselOwners = this.manager
            .getCustomRepository(VesselOwnerRepository)
            .createQueryBuilder('VesselOwner')
            .where(`(VesselOwner.vesselId = :vesselId)`, {
              vesselId: IARFound.vesselId,
            });
          const [vesselDocHolders, vesselCharterers, vesselOwners] = await Promise.all([
            queryVesselDocHolders.getMany(),
            queryVesselCharterers.getMany(),
            queryVesselOwners.getMany(),
          ]);
          Object.assign(IARFound, { vesselDocHolders, vesselCharterers, vesselOwners });
        }

        Object.assign(IARFound, {
          internalAuditReportHistories: iarHistories,
          internalAuditReportOfficeComments: iarComments,
          IARReportHeaders: iarHeaders,
        });

        // fetching the inspection mapping data
        if (IARFound?.planningRequest?.auditWorkspace?.inspectionMappingIds?.length) {
          const inspectionMapping = [];
          const { inspectionMappingIds } = IARFound?.planningRequest?.auditWorkspace;
          for (const inspectionMappingId of inspectionMappingIds) {
            const detailInspectionMapping = await this.connection
              .getCustomRepository(InspectionMappingRepository)
              .createQueryBuilder('inspectionMapping')
              .where('inspectionMapping.id = :inspectionMappingId', {
                inspectionMappingId,
              })
              .getOne();
            inspectionMapping.push(detailInspectionMapping);
          }
          if (inspectionMapping?.length) {
            IARFound.planningRequest.auditWorkspace['inspectionMapping'] = inspectionMapping;
          }
        }

        return {
          ...IARFound,
          nonConformities: IARFound.reportFindingFormId ? nonConformities : [],
          observations: IARFound.reportFindingFormId ? observations : [],
          otherFindingTypes: IARFound.reportFindingFormId ? otherFindingTypes : [],
          lastAuditFindings: IARFound.reportFindingFormId ? lastAuditFindings : [],
        };
      } else {
        throw new BaseError({ status: 404, message: 'internalAuditReport.NOT_FOUND' });
      }
    } catch (ex) {
      LoggerCommon.error('[InternalAuditReportRepository] detailIAR error ', ex.message || ex);
      throw ex;
    }
  }

  async listPreviousIAR(id: string, query: ListPreviousIARQueryDTO, token: TokenPayloadModel) {
    try {
      const IARFound = await this.createQueryBuilder('internalAuditReport')
        .leftJoin('internalAuditReport.company', 'company')
        .where(
          '(internalAuditReport.id = :id AND internalAuditReport.deleted = :deleted AND internalAuditReport.companyId = :companyId)',
          {
            id,
            deleted: false,
            companyId: token.companyId,
          },
        )
        .select([
          'internalAuditReport.id',
          'internalAuditReport.vesselId',
          'internalAuditReport.createdAt',
          'internalAuditReport.auditCompanyId',
          'internalAuditReport.departmentId',
        ])
        .addSelect(['company.id', 'company.name'])
        .getOne();
      if (IARFound) {
        const queryBuilder = this.createQueryBuilder('internalAuditReport')
          .leftJoin('internalAuditReport.reportFindingForm', 'reportFindingForm')
          .leftJoin('internalAuditReport.company', 'company')
          .innerJoin('reportFindingForm.planningRequest', 'planningRequest')
          .leftJoin('internalAuditReport.iarPlanningRequest', 'iarPlanningRequest')
          .leftJoinAndSelect('internalAuditReport.iarAuditTypes', 'iarAuditTypes')
          .leftJoin('internalAuditReport.iarUsers', 'iarUsers')
          .innerJoin('planningRequest.auditTimeTable', 'auditTimeTable')
          .where(
            `(internalAuditReport.vesselId = :vesselId AND internalAuditReport.companyId = :companyId AND internalAuditReport.createdAt < :createdAt)`,
            {
              vesselId: IARFound.vesselId,
              companyId: token.companyId,
              createdAt: IARFound.createdAt,
            },
          )
          .orWhere(
            `(internalAuditReport.departmentId = :departmentId AND internalAuditReport.companyId = :companyId AND internalAuditReport.createdAt < :createdAt)`,
            {
              departmentId: IARFound.departmentId,
              companyId: token.companyId,
              createdAt: IARFound.createdAt,
            },
          )
          .select()
          .addSelect([
            'planningRequest.auditNo AS "prAuditNo"',
            'auditTimeTable.actualFrom AS "actualFrom"',
            'auditTimeTable.actualTo AS "actualTo"',
          ])
          .groupBy('internalAuditReport.id')
          .addGroupBy('planningRequest.auditNo')
          .addGroupBy('iarAuditTypes.id')
          .addGroupBy('auditTimeTable.actualFrom')
          .addGroupBy('auditTimeTable.actualTo');

        if (query.content) {
          queryBuilder.andWhere('(internalAuditReport.refId ILIKE :content)', {
            content: `%${query.content}%`,
          });
        }
        return queryBuilder.getRawMany();
      } else {
        throw new BaseError({ status: 404, message: 'internalAuditReport.NOT_FOUND' });
      }
    } catch (ex) {
      LoggerCommon.error(
        '[InternalAuditReportRepository] listPreviousIAR error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  private async _checkCanUpdateFindingItemAndAssignPIC(iarId: string, user: TokenPayloadModel) {
    const qb = this.createQueryBuilder('iar')
      // TODO: Uncomment and delete new if correct
      // .where('iar.id = :iarId AND iar.status IN (:...statuses) AND iar.companyId = :companyId', {
      //   iarId,
      //   statuses: [InternalAuditReportStatus.DRAFT, InternalAuditReportStatus.REASSIGNED],
      //   companyId: user.companyId,
      // })
      .where('iar.id = :iarId AND iar.status != :iarStatus AND iar.companyId = :companyId', {
        iarId,
        iarStatus: InternalAuditReportStatus.CLOSEOUT,
        companyId: user.companyId,
      })
      .select(['iar.id']);

    const record = await this.getOneQB(qb);

    if (record) {
      return record;
    }
    throw new BaseError({ status: 400, message: 'internalAuditReport.CANNOT_UPDATE_FINDING_ITEM' });
  }

  async updateFindingItemAndAssignPIC(
    iarId: string,
    findingItemId: string,
    body: UpdateFindingItemAndAssignPICDto,
    user: TokenPayloadModel,
  ) {
    await this._checkCanUpdateFindingItemAndAssignPIC(iarId, user);

    // Get finding item for checking PIC and workflow status: [IMPROVE]: Check internalAuditReportId also
    const findingItemRecord = await this.manager
      .createQueryBuilder(ReportFindingItem, 'findingItem')
      .where('findingItem.id = :findingItemId', {
        findingItemId,
      })
      .select()
      .getOne();

    // Note: Delete these below codes
    // Delete param picId if already has picId
    // if (findingItemRecord.picId) {
    //   delete body.picId;
    // }

    // Delete workflowStatus if current findingStatus is opened
    if (findingItemRecord?.findingStatus === FindingStatus.OPEN) {
      delete body.workflowStatus;
    }

    // Update finding item: [IMPROVE]: Check internalAuditReportId also
    const result = await this.manager.update(
      ReportFindingItem,
      {
        id: findingItemId,
        companyId: user.companyId,
      },
      {
        ...body,
        planedCompletionDate: body.planedCompletionDate
          ? new Date(body.planedCompletionDate)
          : null,
        actualCompletionDate: body.actualCompletionDate
          ? new Date(body.actualCompletionDate)
          : null,
      },
    );

    await this._saveChangeHistory(findingItemRecord, body, user);

    return result;
  }

  private async _checkCanUpdateFindingItemForPICUser(
    iarId: string,
    findingItemId: string,
    user: TokenPayloadModel,
  ) {
    const qb = this.manager
      .createQueryBuilder(ReportFindingItem, 'findingItem')
      .innerJoin('findingItem.internalAuditReport', 'iar')
      .where(
        `findingItem.id = :findingItemId AND
          findingItem.internalAuditReportId = :iarId AND
          findingItem.findingStatus = :findingStatus AND
          findingItem.picId = :picId AND
          iar.status != :iarStatus AND
          iar.companyId = :companyId`,
        {
          findingItemId,
          iarId,
          findingStatus: FindingStatus.OPEN,
          picId: user.id, // Check PIC
          iarStatus: InternalAuditReportStatus.CLOSEOUT,
          companyId: user.companyId,
        },
      )
      .select(['findingItem.id']); // use this query when list previous NC and Ob of IAR

    // const qb = this.manager // use this query when list previous NC and Ob of ROF
    //   .createQueryBuilder(ReportFindingItem, 'findingItem')
    //   .where(
    //     `findingItem.id = :findingItemId AND
    //       findingItem.findingStatus = :findingStatus AND
    //       findingItem.picId = :picId`,
    //     {
    //       findingItemId,
    //       findingStatus: FindingStatus.OPEN,
    //       picId: user.id,
    //     },
    //   )
    //   .select(['findingItem.id']);

    const record = await qb.getOne();

    if (record) {
      return record;
    }
    throw new BaseError({ status: 400, message: 'internalAuditReport.CANNOT_UPDATE_FINDING_ITEM' });
  }

  async updateFindingItemForPICUser(
    iarId: string,
    findingItemId: string,
    body: UpdateFindingItemForPICDto,
    user: TokenPayloadModel,
  ) {
    await this._checkCanUpdateFindingItemForPICUser(iarId, findingItemId, user);

    const findingItemRecord = await this.manager.findOne(ReportFindingItem, {
      where: {
        id: findingItemId,
      },
    });

    // Update finding item: [IMPROVE]: Check internalAuditReportId also
    const result = await this.manager.update(
      ReportFindingItem,
      {
        id: findingItemId,
      },
      {
        ...body,
        planedCompletionDate: body.planedCompletionDate
          ? new Date(body.planedCompletionDate)
          : null,
        actualCompletionDate: body.actualCompletionDate
          ? new Date(body.actualCompletionDate)
          : null,
        isOpen: FindingStatus.CLOSE !== body.findingStatus,
      },
    );

    await this._saveChangeHistory(findingItemRecord, body, user);

    return result;
  }

  private async _saveChangeHistory(
    findingItemRecord: ReportFindingItem| SAFindingItem,
    body,
    user: TokenPayloadModel,
  ) {
    const newBody = pick(body, [
      'departmentName',
      'mainCategoryName',
      'secondCategoryName',
      'thirdCategoryName',
      'remark',
      'preventiveAction',
      'picRemark',
      'planedCompletionDate',
      'immediateAction',
      'correctiveAction',
      'actualCompletionDate',
      'picUsername',
    ]);

    // save field change history
    const preparedValueChanges = [];
    const reportFindingItemId = findingItemRecord.id;
    const valueChanges = commonCheckValueChange(findingItemRecord, newBody, false);
    const userHistory = await this.manager
      .getCustomRepository(UserRepository)
      ._getUserInfoForHistory(user.id);

    if (valueChanges) {
      for (const value of valueChanges) {
        preparedValueChanges.push({
          module: ModuleEnum.FINDING_ITEM,
          action: ActionValueChangeEnum.UPDATE,
          createdUser: userHistory,
          companyId: user.companyId,
          recordId: reportFindingItemId,
          key: value.key,
          fieldLabel: value.fieldLabel,
          oldValue: value.oldValue,
          newValue: value.newValue,
        });
      }
    }
    await this.manager.save(ValueChangeHistory, preparedValueChanges);
  }

  private async _checkCanUpdateFindingItemForActor(
    iarId: string,
    findingItemId: string,
    user: TokenPayloadModel,
    isSA: boolean,
  ) {
    let qb;
    if (isSA) {
      qb = this.manager
        .createQueryBuilder(SAFindingItem, 'saFindingItem')
        .innerJoin('saFindingItem.internalAuditReport', 'iar')
        .where(
          `saFindingItem.id = :findingItemId AND
          saFindingItem.internalAuditReportId = :iarId AND
          saFindingItem.workflowStatus = :workflowStatus AND
          iar.companyId = :companyId`,
          {
            findingItemId,
            iarId,
            workflowStatus: FindingItemWorkflowStatus.CLOSE_OUT,
            // findingStatus: FindingStatus.OPEN,
            // iarStatus: InternalAuditReportStatus.CLOSEOUT,
            companyId: user.companyId,
          },
        )
        .select(['saFindingItem.id']); // use this query when list previous NC and Ob of IAR
    } else {
      qb = this.manager
        .createQueryBuilder(ReportFindingItem, 'findingItem')
        .innerJoin('findingItem.internalAuditReport', 'iar')
        .where(
          `findingItem.id = :findingItemId AND
          findingItem.internalAuditReportId = :iarId AND
          findingItem.workflowStatus = :workflowStatus AND
          iar.companyId = :companyId`,
          {
            findingItemId,
            iarId,
            workflowStatus: FindingItemWorkflowStatus.CLOSE_OUT,
            // findingStatus: FindingStatus.OPEN,
            // iarStatus: InternalAuditReportStatus.CLOSEOUT,
            companyId: user.companyId,
          },
        )
        .select(['findingItem.id']); // use this query when list previous NC and Ob of IAR
    }
    const record = await qb.getOne();

    if (record) {
      throw new BaseError({
        status: 400,
        message: 'internalAuditReport.CANNOT_UPDATE_FINDING_ITEM',
      });
    }
    return 1;
  }

  async updateFindingItemForActor(
    iarId: string,
    findingItemId: string,
    body: UpdateFindingItemForPICDto,
    user: TokenPayloadModel,
  ) {
    let result;
    let currentFinding: SAFindingItem | ReportFindingItem;
    const isSA = await this.createQueryBuilder('internalAuditReport')
      .leftJoin('internalAuditReport.planningRequest', 'planningRequest')
      .where('internalAuditReport.id = :iarId', {
        iarId,
      })
      .select(['internalAuditReport.id', 'planningRequest.isSA'])
      .getOne();

    await this._checkCanUpdateFindingItemForActor(
      iarId,
      findingItemId,
      user,
      isSA?.planningRequest?.isSA,
    );

    if (isSA?.planningRequest?.isSA) {
      currentFinding = await this.manager.findOne(SAFindingItem, {
        where: {
          id: findingItemId,
        },
      });

      // Update finding item: [IMPROVE]: Check internalAuditReportId also
      result = await this.manager.update(
        SAFindingItem,
        {
          id: findingItemId,
        },
        {
          ...body,
          planedCompletionDate: body.planedCompletionDate
            ? new Date(body.planedCompletionDate)
            : null,
          actualCompletionDate: body.actualCompletionDate
            ? new Date(body.actualCompletionDate)
            : null,
          isOpen: body.findingStatus === FindingStatus.CLOSE ? false : true,
        },
      );
    } else {
      currentFinding = await this.manager.findOne(ReportFindingItem, {
        where: {
          id: findingItemId,
        },
      });

      // Update finding item: [IMPROVE]: Check internalAuditReportId also
      result = await this.manager.update(
        ReportFindingItem,
        {
          id: findingItemId,
        },
        {
          ...body,
          planedCompletionDate: body.planedCompletionDate
            ? new Date(body.planedCompletionDate)
            : null,
          actualCompletionDate: body.actualCompletionDate
            ? new Date(body.actualCompletionDate)
            : null,
          isOpen: body.findingStatus === FindingStatus.CLOSE ? false : true,
        },
      );
    }

    await this._saveChangeHistory(currentFinding, body, user);

    return result;
  }

  private async _checkCanReviewFindingItem(
    iarId: string,
    findingItemId: string,
    user: TokenPayloadModel,
    isSA?: boolean,
  ) {
    let qb;
    if (isSA) {
      // Self-assessment case
      qb = this.manager
        .createQueryBuilder(SAFindingItem, 'saFindingItem')
        .innerJoin('saFindingItem.internalAuditReport', 'iar')
        .where(
          `saFindingItem.id = :findingItemId AND
          saFindingItem.internalAuditReportId = :iarId AND
          saFindingItem.findingStatus = :findingStatus AND
          saFindingItem.workflowStatus != :workflowStatus AND
          iar.status != :iarStatus AND
          iar.companyId = :companyId`,
          {
            iarId,
            findingItemId,
            findingStatus: FindingStatus.CLOSE,
            workflowStatus: FindingItemWorkflowStatus.CLOSE_OUT,
            iarStatus: InternalAuditReportStatus.CLOSEOUT,
            companyId: user.companyId,
          },
        )
        .select(['saFindingItem.id']);
    } else {
      // Normal case
      qb = this.manager
        .createQueryBuilder(ReportFindingItem, 'findingItem')
        .innerJoin('findingItem.internalAuditReport', 'iar')
        .where(
          `findingItem.id = :findingItemId AND
          findingItem.internalAuditReportId = :iarId AND
          findingItem.findingStatus = :findingStatus AND
          findingItem.workflowStatus != :workflowStatus AND
          iar.status != :iarStatus AND
          iar.companyId = :companyId`,
          {
            iarId,
            findingItemId,
            findingStatus: FindingStatus.CLOSE,
            workflowStatus: FindingItemWorkflowStatus.CLOSE_OUT,
            iarStatus: InternalAuditReportStatus.CLOSEOUT,
            companyId: user.companyId,
          },
        )
        .select(['findingItem.id']);
    }

    const record = await qb.getOne();

    if (record) {
      return record;
    }
    throw new BaseError({ status: 400, message: 'internalAuditReport.CANNOT_REVIEW_FINDING_ITEM' });
  }

  async reviewFindingItem(
    iarId: string,
    findingItemId: string,
    body: ReviewFindingItemDto,
    user: TokenPayloadModel,
  ) {
    // Check if this is a self-assessment case
    const isSA = await this.createQueryBuilder('internalAuditReport')
      .leftJoinAndSelect('internalAuditReport.planningRequest', 'planningRequest')
      .where('internalAuditReport.id = :iarId', {
        iarId,
      })
      .getOne();

    await this._checkCanReviewFindingItem(iarId, findingItemId, user, isSA?.planningRequest?.isSA);

    const updateBody = {
      workflowStatus: body.workflowStatus,
      findingStatus: FindingStatus.OPEN,
    };

    if (body.workflowStatus === FindingItemWorkflowStatus.CLOSE_OUT) {
      delete updateBody.findingStatus;
    }

    // Update finding item: [IMPROVE]: Check internalAuditReportId also
    await this.manager.transaction(async (managerTrans) => {
      if (isSA?.planningRequest?.isSA) {
        // Self-assessment case
        await Promise.all([
          managerTrans.update(
            SAFindingItem,
            {
              id: findingItemId,
            },
            updateBody,
          ),
        ]);
      } else {
        // Normal case
        await Promise.all([
          managerTrans.update(
            ReportFindingItem,
            {
              id: findingItemId,
            },
            updateBody,
          ),
          managerTrans.insert(FindingItemHistory, {
            reportFindingItemId: findingItemId,
            workflowStatus: body.workflowStatus,
            createdUserId: user.id,
          }),
        ]);
      }
    });
  }

  async reviewIAR(
    IARid: string,
    params: ReviewIARDTO,
    preparedIARReportHeaders: IARReportHeader[],
    preparedIARReportHeaderDescriptions: IARReportHeaderDescription[],
    token: TokenPayloadModel,
    workflowPermissions: string[],
    query: ReviewIARQueryDTO,
  ) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      if (query.isReviewed && query.isReviewed.toString() === 'true') {
        const IARFound = await this.createQueryBuilder('internalAuditReport')
          .where(
            'internalAuditReport.id = :id AND internalAuditReport.companyId = :companyId AND internalAuditReport.status IN (:...statuses)',
            {
              id: IARid,
              companyId: token.companyId,
              statuses: [
                InternalAuditReportStatus.SUBMITTED,
                InternalAuditReportStatus.REVIEWED_1,
                InternalAuditReportStatus.REVIEWED_2,
                InternalAuditReportStatus.REVIEWED_3,
                InternalAuditReportStatus.REVIEWED_4,
                InternalAuditReportStatus.REASSIGNED,
              ],
            },
          )
          .leftJoin('internalAuditReport.reportFindingForm', 'reportFindingForm')
          .leftJoinAndSelect(
            'internalAuditReport.internalAuditReportComment',
            'internalAuditReportComment',
          )
          .leftJoinAndSelect(
            'internalAuditReport.internalAuditReportOfficeComments',
            'internalAuditReportOfficeComments',
          )
          .leftJoinAndSelect('internalAuditReport.IARReportHeaders', 'IARReportHeaders')
          .leftJoinAndSelect(
            'IARReportHeaders.IARReportHeaderDescriptions',
            'IARReportHeaderDescriptions',
          )
          .leftJoinAndSelect(
            'internalAuditReport.internalAuditReportHistories',
            'internalAuditReportHistories',
          )
          .orderBy('internalAuditReportHistories.createdAt', 'DESC')
          .getOne();

        if (!IARFound) {
          throw new BaseError({ message: 'internalAuditReport.NOT_FOUND' });
        }

        if (!IARFound.reportFindingFormId) {
          throw new BaseError({
            message: 'internalAuditReport.FILL_CHKLIST_FINDING_ITEMS_BEFORE_REVIEWED',
          });
        }

        let status: string, previousStatus: string, globalStatus: string;
        if (IARFound.status === InternalAuditReportStatus.SUBMITTED) {
          if (!workflowPermissions.includes(WorkflowPermission.REVIEWER1)) {
            throw new ForbiddenError({});
          }
          status = InternalAuditReportStatus.REVIEWED_1;
          previousStatus = InternalAuditReportStatus.SUBMITTED;
          globalStatus = GlobalStatusEnum.UNDER_2ND_APPROVAL;
        } else if (IARFound.status.startsWith('reviewed_')) {
          const lastReviewedStep = Number(IARFound.status.substring(IARFound.status.length - 1));
          const currentReviewedStep = lastReviewedStep + 1;
          if (currentReviewedStep > 5) {
            throw new BaseError({ message: 'internalAuditReport.CANNOT_REVIEWED_5' });
          }

          // get max workflow role of user exam: review_5
          const maxRoleOfCurrentUser = workflowPermissions.reduce((currentMaxRole, role) => {
            if (role.startsWith('reviewer') && role > currentMaxRole) {
              return role;
            } else {
              return currentMaxRole;
            }
          }, WorkflowPermission.REVIEWER1);

          // get last char Number to compared 2 role, if less than current iar role then throw err
          const lastChar = maxRoleOfCurrentUser.slice(-1);

          if (lastChar <= IARFound.status.slice(-1)) {
            throw new BaseError({
              status: 404,
              message: 'internal_audit_report.PERMISSION_REQUIRED',
            });
          }

          // set status for iar and format type of enum string. exp: reviewer1 -> review_1
          let selectedStep: number;
          // if (workflowPermissions.includes(`reviewer${currentReviewedStep}`)) {
          //   selectedStep = currentReviewedStep;
          // } else {
          //   selectedStep = Number(lastChar);
          // }
          for (let i = currentReviewedStep; i <= Number(lastChar); i++) {
            if (workflowPermissions.includes(`reviewer${i}`)) {
              selectedStep = i;
              break;
            }
          }
          previousStatus = IARFound.status;

          status = [
            InternalAuditReportStatus.REVIEWED_2,
            InternalAuditReportStatus.REVIEWED_3,
            InternalAuditReportStatus.REVIEWED_4,
            InternalAuditReportStatus.REVIEWED_5,
          ][selectedStep - 2];

          globalStatus = [
            GlobalStatusEnum.UNDER_3RD_APPROVAL,
            GlobalStatusEnum.UNDER_4TH_APPROVAL,
            GlobalStatusEnum.UNDER_5TH_APPROVAL,
            GlobalStatusEnum.UNDER_6TH_APPROVAL,
          ][selectedStep - 2];
        }
        if (IARFound.status === InternalAuditReportStatus.REASSIGNED) {
          const statusMap = this._getLastReviewAndCountReassign(
            IARFound.internalAuditReportHistories,
          );

          status = [
            InternalAuditReportStatus.REVIEWED_1,
            InternalAuditReportStatus.REVIEWED_2,
            InternalAuditReportStatus.REVIEWED_3,
            InternalAuditReportStatus.REVIEWED_4,
            InternalAuditReportStatus.REVIEWED_5,
          ][statusMap.maxReview - statusMap.countReassign];

          globalStatus = [
            GlobalStatusEnum.UNDER_2ND_APPROVAL,
            GlobalStatusEnum.UNDER_3RD_APPROVAL,
            GlobalStatusEnum.UNDER_4TH_APPROVAL,
            GlobalStatusEnum.UNDER_5TH_APPROVAL,
            GlobalStatusEnum.UNDER_6TH_APPROVAL,
          ][statusMap.maxReview - statusMap.countReassign];
        }

        //#region Prepare IAR Header description
        const {
          listHeaderDescriptionsCreate,
          listHeaderDescriptionsUpdate,
          listHeaderDescriptionDeleteIds,
        } = this._handleIARReportHeaderDescription(IARFound, preparedIARReportHeaderDescriptions);
        //#endregion

        //#region Prepare update ROF items
        const prepareROFItems = [];
        if (params.reportFindingItems && params.reportFindingItems.length > 0) {
          params.reportFindingItems.forEach((item: ReportFindingItem3Dto) => {
            prepareROFItems.push({
              ...item,
              planedCompletionDate: item.planedCompletionDate
                ? new Date(item.planedCompletionDate)
                : null,
              actualCompletionDate: item.actualCompletionDate
                ? new Date(item.actualCompletionDate)
                : null,
            });
          });
        }
        //#endregion Prepare update ROF items

        // TODO check user role

        return this.connection.transaction(async (manager) => {
          // Update IAR status
          await manager.update(
            InternalAuditReport,
            {
              id: IARid,
            },
            {
              status,
              previousStatus,
              updatedUserId: token.id,
              workflowRemarks: params.workflowRemarks ? params.workflowRemarks : null,
            },
          );

          // trigger update status report finding form
          await manager
            .getCustomRepository(ReportFindingFormRepository)
            ._triggerFindingManagementStatus(
              IARFound.reportFindingFormId,
              ReportFindingFormStatus.REVIEWED,
            );

          // Get user info
          const createdUser = await this.manager
            .getCustomRepository(UserRepository)
            ._getUserInfoForHistory(token.id);

          await Promise.all([
            // Create IAR history
            manager.save(InternalAuditReportHistory, {
              id: Utils.strings.generateUUID(),
              internalAuditReportId: IARid,
              remark: params.remark ? params.remark : null,
              status: status,
              createdUser,
            }),
            // update IAR report header
            manager.save(IARReportHeader, preparedIARReportHeaders),
            // create IAR header description
            manager.save(IARReportHeaderDescription, listHeaderDescriptionsCreate),
            // update IAR header description
            manager.save(IARReportHeaderDescription, listHeaderDescriptionsUpdate),
            // delete old IAR header description
            manager.delete(IARReportHeaderDescription, {
              id: In(Array.from(listHeaderDescriptionDeleteIds)),
            }),
            // Update OBS, NC
            manager.save(ReportFindingItem, prepareROFItems),
            // Update global status
            manager.update(
              PlanningRequest,
              {
                id: IARFound.planningRequestId,
              },
              {
                globalStatus,
              },
            ),
          ]);

          // Handle add Audit log
          await manager.getCustomRepository(AuditLogRepository).createAuditLog(
            {
              module: AuditModuleEnum.INSPECTION_REPORT,
              planningId: IARFound.planningRequestId,
            },
            null,
            [AuditActivityEnum.REVIEWED],
            createdUser,
          );

          if (params.userAssignment) {
            // Update user assignment
            await manager
              .getCustomRepository(UserAssignmentRepository)
              .updateUserAssignment(
                manager,
                ModuleType.INTERNAL_AUDIT_REPORT,
                IARFound.id,
                params.userAssignment.usersPermissions,
              );
          }

          //#region Handle push noti
          // list User Assignment
          const listUserAssignment = await manager
            .getCustomRepository(UserAssignmentRepository)
            .listByModule(ModuleType.INTERNAL_AUDIT_REPORT, IARFound.id);
          const step1 = listUserAssignment[WorkflowPermission.REVIEWER1].length
            ? 'reviewer1'
            : null;
          const step2 = listUserAssignment[WorkflowPermission.REVIEWER2].length
            ? 'reviewer2'
            : null;
          const step3 = listUserAssignment[WorkflowPermission.REVIEWER3].length
            ? 'reviewer3'
            : null;
          const step4 = listUserAssignment[WorkflowPermission.REVIEWER4].length
            ? 'reviewer4'
            : null;
          const step5 = listUserAssignment[WorkflowPermission.REVIEWER5].length
            ? 'reviewer5'
            : null;
          const stepApprove = listUserAssignment[WorkflowPermission.APPROVER].length
            ? 'approver'
            : null;
          let listReceiverNoti = [];
          let nextStep;

          switch (IARFound.status) {
            case InternalAuditReportStatus.SUBMITTED:
              nextStep = step2 || step3 || step4 || step5 || stepApprove;

              if (nextStep) {
                listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
              }
              break;
            case InternalAuditReportStatus.REVIEWED_1:
              if (status === InternalAuditReportStatus.REVIEWED_2) {
                nextStep = step3 || step4 || step5 || stepApprove;
              }
              if (status === InternalAuditReportStatus.REVIEWED_3) {
                nextStep = step4 || step5 || stepApprove;
              }

              if (status === InternalAuditReportStatus.REVIEWED_4) {
                nextStep = step5 || stepApprove;
              }

              if (status === InternalAuditReportStatus.REVIEWED_5) {
                nextStep = stepApprove;
              }
              // nextStep = step3 || step4 || step5 || stepApprove;
              if (nextStep) {
                listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
              }
              break;
            case InternalAuditReportStatus.REVIEWED_2:
              if (status === InternalAuditReportStatus.REVIEWED_3) {
                nextStep = step4 || step5 || stepApprove;
              }

              if (status === InternalAuditReportStatus.REVIEWED_4) {
                nextStep = step5 || stepApprove;
              }

              if (status === InternalAuditReportStatus.REVIEWED_5) {
                nextStep = stepApprove;
              }
              if (nextStep) {
                listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
              }
              break;
            case InternalAuditReportStatus.REVIEWED_3:
              if (status === InternalAuditReportStatus.REVIEWED_4) {
                nextStep = step5 || stepApprove;
              }

              if (status === InternalAuditReportStatus.REVIEWED_5) {
                nextStep = stepApprove;
              }
              if (nextStep) {
                listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
              }
              break;
            case InternalAuditReportStatus.REVIEWED_4:
              nextStep = stepApprove;
              if (nextStep) {
                listReceiverNoti = listReceiverNoti.concat(listUserAssignment[nextStep]);
              }
              break;
            case InternalAuditReportStatus.REVIEWED_5:
              listReceiverNoti = listReceiverNoti.concat(
                listUserAssignment[WorkflowPermission.APPROVER],
              );
              break;
          }
          const performer = await manager
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle', 'email']);
          dataNoti.push({
            receivers: listReceiverNoti,
            module: ModuleType.INTERNAL_AUDIT_REPORT,
            recordId: IARFound.id,
            recordRef: IARFound.refId,
            type: PushTypeEnum.CHANGE_RECORD_STATUS,
            currentStatus: status,
            previousStatus,
            performer: performer,
            executedAt: new Date(),
          });

          for (const receiver of listReceiverNoti) {
            dataSendMail.push({
              receiver: receiver as IUserEmail,
              type: EmailTypeEnum.CHANGE_RECORD_STATUS,
              templateKey: MailTemplate.CHANGE_RECORD_STATUS,
              subject: '[Notification] Change status in a record',
              data: {
                username: receiver.username,
                baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                recordRef: IARFound.refId,
                recordId: IARFound.id,
                path: ModulePathEnum.INTERNAL_AUDIT_REPORT,
                currentStatus: status,
                previousStatus,
                performer: performer,
                executedAt: new Date(),
              },
            });
          }

          return { dataNoti, dataSendMail };
        });
      } else if (query.isReassigned && query.isReassigned.toString() === 'true') {
        return await this.reassignIAR(
          IARid,
          { remark: params.remark ? params.remark : null },
          token,
        );
      }
      return { dataNoti, dataSendMail };
    } catch (ex) {
      LoggerCommon.error('[InternalAuditReportRepository] reviewIAR error ', ex.message || ex);
      throw ex;
    }
  }

  async reassignIAR(id: string, params: ReassignIARDTO, token: TokenPayloadModel) {
    const dataNoti: INotificationEventModel[] = [];
    const dataSendMail: IEmailEventModel[] = [];
    try {
      return await this.connection.transaction(async (manager) => {
        const IARFound = await this.createQueryBuilder('internalAuditReport')
          .where(
            'internalAuditReport.id = :id AND internalAuditReport.companyId = :companyId AND internalAuditReport.status IN (:...statuses)',
            {
              id,
              companyId: token.companyId,
              statuses: [
                InternalAuditReportStatus.SUBMITTED,
                InternalAuditReportStatus.REVIEWED_1,
                InternalAuditReportStatus.REVIEWED_2,
                InternalAuditReportStatus.REVIEWED_3,
                InternalAuditReportStatus.REVIEWED_4,
                InternalAuditReportStatus.REVIEWED_5,
                InternalAuditReportStatus.REASSIGNED,
              ],
            },
          )
          .leftJoin('internalAuditReport.reportFindingForm', 'reportFindingForm')
          .leftJoinAndSelect(
            'internalAuditReport.internalAuditReportComment',
            'internalAuditReportComment',
          )
          .leftJoinAndSelect(
            'internalAuditReport.internalAuditReportOfficeComments',
            'internalAuditReportOfficeComments',
          )
          .leftJoinAndSelect('internalAuditReport.IARReportHeaders', 'IARReportHeaders')
          .leftJoinAndSelect(
            'IARReportHeaders.IARReportHeaderDescriptions',
            'IARReportHeaderDescriptions',
          )
          .leftJoinAndSelect('internalAuditReport.planningRequest', 'planningRequest')
          .leftJoinAndSelect('planningRequest.auditors', 'auditors')
          .getOne();
        if (!IARFound) {
          throw new BaseError({ message: 'internalAuditReport.NOT_FOUND' });
        }

        // Update IAR
        await manager.update(
          InternalAuditReport,
          { id },
          {
            status: InternalAuditReportStatus.REASSIGNED,
            previousStatus: IARFound.status,
          },
        );
        // trigger update status report finding form
        await manager
          .getCustomRepository(ReportFindingFormRepository)
          ._triggerFindingManagementStatus(
            IARFound.reportFindingFormId,
            ReportFindingFormStatus.IN_PROGRESS,
          );

        await manager.update(
          PlanningRequest,
          { id: IARFound.planningRequestId },
          {
            status: PlanningRequestStatus.IN_PROGRESS,
          },
        );

        // Get user info
        const createdUser = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token.id);

        // Create History
        await manager.save(InternalAuditReportHistory, {
          id: Utils.strings.generateUUID(),
          internalAuditReportId: id,
          remark: params.remark,
          status: InternalAuditReportStatus.REASSIGNED,
          createdUser,
        });
        // NO need to check the below condition
        // Update global status
        // if (
        //   this.manager
        //     .getCustomRepository(CAPRepository)
        //     ._checkNeedUpdateGlobalStatus(
        //       IARFound.planningRequest.globalStatus,
        //       GlobalStatusEnum.DISAPPROVED_REPORT,
        //     )
        // )
        // {
        await manager.update(
          PlanningRequest,
          {
            id: IARFound.planningRequestId,
          },
          {
            globalStatus: GlobalStatusEnum.DISAPPROVED_REPORT,
          },
        );
        // }
        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_REPORT,
            planningId: IARFound.planningRequestId,
          },
          null,
          [AuditActivityEnum.REASSIGNED],
          createdUser,
        );

        // list User Assignment

        const listReceiverNoti = [];
        const auditors = IARFound.planningRequest.auditors;
        for (const { id, username, jobTitle, email } of auditors) {
          listReceiverNoti.push({ id, username, jobTitle, email });
        }
        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle', 'email']);
        dataNoti.push({
          receivers: listReceiverNoti,
          module: ModuleType.INTERNAL_AUDIT_REPORT,
          recordId: IARFound.id,
          recordRef: IARFound.refId,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: InternalAuditReportStatus.REASSIGNED,
          previousStatus: IARFound.status,
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNoti) {
          dataSendMail.push({
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: IARFound.refId,
              recordId: IARFound.id,
              path: ModulePathEnum.INTERNAL_AUDIT_REPORT,
              currentStatus: InternalAuditReportStatus.REASSIGNED,
              previousStatus: IARFound.status,
              performer: performer,
              executedAt: new Date(),
            },
          });
        }

        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      LoggerCommon.error('[InternalAuditReportRepository] reassignIAR error ', ex.message || ex);
      throw ex;
    }
  }

  async approveIAR(
    id: string,
    params: ApproveIARDTO,
    preparedIARReportHeaders: IARReportHeader[],
    preparedIARReportHeaderDescriptions: IARReportHeaderDescription[],
    token: TokenPayloadModel,
    query: ApproveIARQueryDTO,
  ) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      if (query.isApproved && query.isApproved.toString() === 'true') {
        const IARFound = await this.createQueryBuilder('internalAuditReport')
          .where('internalAuditReport.id = :id AND internalAuditReport.companyId = :companyId', {
            id,
            companyId: token.companyId,
          })
          .leftJoin('internalAuditReport.reportFindingForm', 'reportFindingForm')
          .leftJoinAndSelect(
            'internalAuditReport.internalAuditReportComment',
            'internalAuditReportComment',
          )
          .leftJoinAndSelect(
            'internalAuditReport.internalAuditReportOfficeComments',
            'internalAuditReportOfficeComments',
          )
          .leftJoinAndSelect('internalAuditReport.IARReportHeaders', 'IARReportHeaders')
          .leftJoinAndSelect('internalAuditReport.planningRequest', 'planningRequest')
          .leftJoinAndSelect(
            'IARReportHeaders.IARReportHeaderDescriptions',
            'IARReportHeaderDescriptions',
          )
          .getOne();
        if (!IARFound) {
          throw new BaseError({ message: 'internalAuditReport.NOT_FOUND' });
        }

        if (!IARFound.reportFindingFormId) {
          throw new BaseError({
            message: 'internalAuditReport.FILL_CHKLIST_FINDING_ITEMS_BEFORE_REVIEWED',
          });
        }

        //#region Prepare IAR Header description
        const {
          listHeaderDescriptionsCreate,
          listHeaderDescriptionsUpdate,
          listHeaderDescriptionDeleteIds,
        } = this._handleIARReportHeaderDescription(IARFound, preparedIARReportHeaderDescriptions);
        //#endregion

        //#region Prepare update ROF items
        const prepareROFItems = [];
        if (params.reportFindingItems && params.reportFindingItems.length > 0) {
          params.reportFindingItems.forEach((item: ReportFindingItem3Dto) => {
            prepareROFItems.push({
              ...item,
              planedCompletionDate: item.planedCompletionDate
                ? new Date(item.planedCompletionDate)
                : null,
              actualCompletionDate: item.actualCompletionDate
                ? new Date(item.actualCompletionDate)
                : null,
            });
          });
        }
        //#endregion Prepare update ROF items

        return this.connection.transaction(async (manager) => {
          // Update IAR status
          await manager.update(
            InternalAuditReport,
            {
              id,
            },
            {
              status: InternalAuditReportStatus.APPROVED,
              previousStatus: InternalAuditReportStatus.REVIEWED_5,
              updatedUserId: token.id,
              workflowRemarks: params.workflowRemarks ? params.workflowRemarks : null,
            },
          );

          // trigger update status report finding form
          await manager
            .getCustomRepository(ReportFindingFormRepository)
            ._triggerFindingManagementStatus(
              IARFound.reportFindingFormId,
              ReportFindingFormStatus.APPROVED,
            );

          // Get user info
          const createdUser = await this.manager
            .getCustomRepository(UserRepository)
            ._getUserInfoForHistory(token.id);

          await Promise.all([
            // Create history
            manager.insert(InternalAuditReportHistory, {
              internalAuditReportId: id,
              remark: params.remark ? params.remark : null,
              status: InternalAuditReportStatus.APPROVED,
              createdUser,
            }),
            //update IAR report header
            manager.save(IARReportHeader, preparedIARReportHeaders),
            // create IAR header description
            manager.save(IARReportHeaderDescription, listHeaderDescriptionsCreate),
            // update IAR header description
            manager.save(IARReportHeaderDescription, listHeaderDescriptionsUpdate),
            // delete old IAR header description
            manager.delete(IARReportHeaderDescription, {
              id: In(Array.from(listHeaderDescriptionDeleteIds)),
            }),
            // Update OBS, NC
            manager.save(ReportFindingItem, prepareROFItems),
          ]);

          //#region Update global status all case
          const foundCar = await manager.getCustomRepository(CARRepository).findOne({
            where: {
              planningRequestId: IARFound.planningRequestId,
              isDup: true,
            },
          });

          const verifiedCar = await manager
            .getCustomRepository(CARRepository)
            .createQueryBuilder('car')
            .innerJoinAndSelect('car.cARVerification', 'verification')
            .where('car.planningRequestId = :planningRequestId', {
              planningRequestId: IARFound.planningRequestId,
            })
            .orderBy('verification.updatedAt', 'DESC')
            .getOne();

          if (!foundCar && !verifiedCar) {
            if (
              this.manager
                .getCustomRepository(CAPRepository)
                ._checkNeedUpdateGlobalStatus(
                  IARFound.planningRequest.globalStatus,
                  GlobalStatusEnum.APPROVED_REPORT,
                )
            ) {
              await manager.update(
                PlanningRequest,
                {
                  id: IARFound.planningRequestId,
                },
                {
                  globalStatus: GlobalStatusEnum.APPROVED_REPORT,
                },
              );
            }
          } else if (foundCar && !verifiedCar) {
            if (
              this.manager
                .getCustomRepository(CAPRepository)
                ._checkNeedUpdateGlobalStatus(
                  IARFound.planningRequest.globalStatus,
                  GlobalStatusEnum.SENT_CAR_UNDER_CAP_PREPARATION,
                )
            ) {
              await manager.update(
                PlanningRequest,
                {
                  id: IARFound.planningRequestId,
                },
                {
                  globalStatus: GlobalStatusEnum.SENT_CAR_UNDER_CAP_PREPARATION,
                },
              );
            }
          } else if (verifiedCar) {
            const carVerification = verifiedCar.cARVerification;
            if (
              !carVerification.isNeeded &&
              this.manager
                .getCustomRepository(CAPRepository)
                ._checkNeedUpdateGlobalStatus(
                  IARFound.planningRequest.globalStatus,
                  GlobalStatusEnum.APPROVED_CAP_NO_VERIFICATION,
                )
            ) {
              await manager.update(
                PlanningRequest,
                {
                  id: IARFound.planningRequestId,
                },
                {
                  globalStatus: GlobalStatusEnum.APPROVED_CAP_NO_VERIFICATION,
                },
              );
            } else {
              if (
                (carVerification.status === CarVerificationStatusEnum.PENDING ||
                  carVerification.status === CarVerificationStatusEnum.HOLDING) &&
                this.manager
                  .getCustomRepository(CAPRepository)
                  ._checkNeedUpdateGlobalStatus(
                    IARFound.planningRequest.globalStatus,
                    GlobalStatusEnum.WAITING_VERIFICATION,
                  )
              ) {
                await manager.update(
                  PlanningRequest,
                  {
                    id: IARFound.planningRequestId,
                  },
                  {
                    globalStatus: GlobalStatusEnum.WAITING_VERIFICATION,
                  },
                );
              } else if (
                (carVerification.status === CarVerificationStatusEnum.VERIFIED_AND_CLOSE ||
                  carVerification.status === CarVerificationStatusEnum.OVERRIDING_CLOSURE) &&
                this.manager
                  .getCustomRepository(CAPRepository)
                  ._checkNeedUpdateGlobalStatus(
                    IARFound.planningRequest.globalStatus,
                    GlobalStatusEnum.APPROVED_VERIFICATION,
                  )
              ) {
                await manager.update(
                  PlanningRequest,
                  {
                    id: IARFound.planningRequestId,
                  },
                  {
                    globalStatus: GlobalStatusEnum.APPROVED_VERIFICATION,
                  },
                );
              }
            }
          }
          //#endregion

          // Handle add Audit log
          await manager.getCustomRepository(AuditLogRepository).createAuditLog(
            {
              module: AuditModuleEnum.INSPECTION_REPORT,
              planningId: IARFound.planningRequestId,
            },
            null,
            [AuditActivityEnum.APPROVED],
            createdUser,
          );
          if (params.userAssignment) {
            // Update user assignment
            await manager
              .getCustomRepository(UserAssignmentRepository)
              .updateUserAssignment(
                manager,
                ModuleType.INTERNAL_AUDIT_REPORT,
                IARFound.id,
                params.userAssignment.usersPermissions,
              );
          }
          //#region Handle push noti
          // list User Assignment
          const listUserAssignment = await manager
            .getCustomRepository(UserAssignmentRepository)
            .listByModule(ModuleType.INTERNAL_AUDIT_REPORT, IARFound.id);

          let listReceiverNoti = [];
          listReceiverNoti = listReceiverNoti.concat(
            listUserAssignment[WorkflowPermission.APPROVER],
          );
          const performer = await manager
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle', 'email']);
          dataNoti.push({
            receivers: listReceiverNoti,
            module: ModuleType.INTERNAL_AUDIT_REPORT,
            recordId: IARFound.id,
            recordRef: IARFound.refId,
            type: PushTypeEnum.CHANGE_RECORD_STATUS,
            currentStatus: InternalAuditReportStatus.APPROVED,
            previousStatus: IARFound.status,
            performer: performer,
            executedAt: new Date(),
          });

          for (const receiver of listReceiverNoti) {
            dataSendMail.push({
              receiver: receiver as IUserEmail,
              type: EmailTypeEnum.CHANGE_RECORD_STATUS,
              templateKey: MailTemplate.CHANGE_RECORD_STATUS,
              subject: '[Notification] Change status in a record',
              data: {
                username: receiver.username,
                baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
                recordRef: IARFound.refId,
                recordId: IARFound.id,
                path: ModulePathEnum.INTERNAL_AUDIT_REPORT,
                currentStatus: InternalAuditReportStatus.APPROVED,
                previousStatus: IARFound.status,
                performer: performer,
                executedAt: new Date(),
              },
            });
          }

          await manager
            .getCustomRepository(ReportFindingFormRepository)
            ._triggerCreateFollowUp(
              manager,
              token,
              IARFound.reportFindingFormId,
              params.timezone,
              createdUser,
            );

          return { dataNoti, dataSendMail };
        });
      } else if (query.isReassigned && query.isReassigned.toString() === 'true') {
        return await this.reassignIAR(id, { remark: params.remark ? params.remark : null }, token);
      }
      return { dataNoti, dataSendMail };
    } catch (ex) {
      LoggerCommon.error('[InternalAuditReportRepository] approveIAR error ', ex.message || ex);
      throw ex;
    }
  }

  async closeoutIAR(
    id: string,
    params: CloseoutIARDTO,
    preparedIARReportHeaders: IARReportHeader[],
    preparedIARReportHeaderDescriptions: IARReportHeaderDescription[],
    token: TokenPayloadModel,
  ) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      const IARFound = await this.createQueryBuilder('internalAuditReport')
        .where(
          'internalAuditReport.id = :id AND internalAuditReport.companyId = :companyId AND internalAuditReport.status = :status',
          {
            id,
            companyId: token.companyId,
            status: InternalAuditReportStatus.APPROVED,
          },
        )
        .leftJoinAndSelect(
          'internalAuditReport.internalAuditReportComment',
          'internalAuditReportComment',
        )
        .leftJoinAndSelect(
          'internalAuditReport.internalAuditReportOfficeComments',
          'internalAuditReportOfficeComments',
        )
        .leftJoinAndSelect('internalAuditReport.IARReportHeaders', 'IARReportHeaders')
        .leftJoinAndSelect(
          'IARReportHeaders.IARReportHeaderDescriptions',
          'IARReportHeaderDescriptions',
        )
        .leftJoinAndSelect('internalAuditReport.planningRequest', 'planningRequest')
        .leftJoinAndSelect('planningRequest.auditors', 'auditors')
        .getOne();
      if (!IARFound) {
        throw new BaseError({ message: 'internalAuditReport.NOT_FOUND' });
      }

      // Check if All ROF Item of IAR is closeout
      const listNCItemOfROF = await this.connection
        .getCustomRepository(ReportFindingItemRepository)
        .listFindingItemOfForm(
          IARFound.reportFindingFormId,
          {
            natureFinding: AppConst.NATURE_FINDING_DEFAULT.NON_CONFORMITY,
            internalAuditReportId: id,
          },
          token,
        );

      let numCloseOutItemOfIAR = 0;
      for (const item of listNCItemOfROF.data) {
        if (item.workflowStatus === FindingItemWorkflowStatus.CLOSE_OUT) {
          numCloseOutItemOfIAR += 1;
        }
      }
      if (numCloseOutItemOfIAR !== listNCItemOfROF.totalItem) {
        throw new BaseError({
          message: 'internalAuditReport.ALL_NC_FINDINGS_MUST_BE_CLOSED_OUT',
        });
      }

      //#region Prepare IAR Header description
      const {
        listHeaderDescriptionsCreate,
        listHeaderDescriptionsUpdate,
        listHeaderDescriptionDeleteIds,
      } = this._handleIARReportHeaderDescription(IARFound, preparedIARReportHeaderDescriptions);
      //#endregion

      return this.connection.transaction(async (manager) => {
        // Update IAR status
        await manager.update(
          InternalAuditReport,
          {
            id,
          },
          {
            status: InternalAuditReportStatus.CLOSEOUT,
            previousStatus: InternalAuditReportStatus.APPROVED,
            updatedUserId: token.id,
          },
        );

        // Get user info
        const createdUser = await this.manager
          .getCustomRepository(UserRepository)
          ._getUserInfoForHistory(token.id);

        await Promise.all([
          // Create history
          manager.insert(InternalAuditReportHistory, {
            internalAuditReportId: id,
            remark: params.remark ? params.remark : null,
            status: InternalAuditReportStatus.CLOSEOUT,
            createdUser,
          }),
          // update IAR report header
          manager.save(IARReportHeader, preparedIARReportHeaders),
          // create IAR header description
          manager.save(IARReportHeaderDescription, listHeaderDescriptionsCreate),
          // update IAR header description
          manager.save(IARReportHeaderDescription, listHeaderDescriptionsUpdate),
          // delete old IAR header description
          manager.delete(IARReportHeaderDescription, {
            id: In(Array.from(listHeaderDescriptionDeleteIds)),
          }),
        ]);

        // Handle add Audit log
        await manager.getCustomRepository(AuditLogRepository).createAuditLog(
          {
            module: AuditModuleEnum.INSPECTION_REPORT,
            planningId: IARFound.planningRequestId,
          },
          null,
          [AuditActivityEnum.CLOSE_OUT],
          createdUser,
        );

        // trigger planing request status to Completed
        await manager
          .getCustomRepository(PlanningRequestRepository)
          ._triggerCompletedPlanningRequest(IARFound.planningRequestId, createdUser, token.id);

        if (params.userAssignment) {
          // Update user assignment
          await manager
            .getCustomRepository(UserAssignmentRepository)
            .updateUserAssignment(
              manager,
              ModuleType.INTERNAL_AUDIT_REPORT,
              IARFound.id,
              params.userAssignment.usersPermissions,
            );
        }

        // list User Assignment
        const listUserAssignment = await manager
          .getCustomRepository(UserAssignmentRepository)
          .listByModule(ModuleType.INTERNAL_AUDIT_REPORT, IARFound.id);

        let listReceiverNoti: IUser[] = [];
        listReceiverNoti = listReceiverNoti.concat(
          listUserAssignment[WorkflowPermission.REVIEWER1],
        );
        listReceiverNoti = listReceiverNoti.concat(
          listUserAssignment[WorkflowPermission.REVIEWER2],
        );
        listReceiverNoti = listReceiverNoti.concat(
          listUserAssignment[WorkflowPermission.REVIEWER3],
        );
        listReceiverNoti = listReceiverNoti.concat(
          listUserAssignment[WorkflowPermission.REVIEWER4],
        );
        listReceiverNoti = listReceiverNoti.concat(
          listUserAssignment[WorkflowPermission.REVIEWER5],
        );
        listReceiverNoti = listReceiverNoti.concat(listUserAssignment[WorkflowPermission.APPROVER]);

        const auditors = IARFound.planningRequest.auditors;
        for (const { id, username, email, jobTitle } of auditors) {
          listReceiverNoti.push({ id, username, email, jobTitle });
        }

        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle', 'email']);
        dataNoti.push({
          receivers: listReceiverNoti,
          module: ModuleType.INTERNAL_AUDIT_REPORT,
          recordId: IARFound.id,
          recordRef: IARFound.refId,
          type: PushTypeEnum.CHANGE_RECORD_STATUS,
          currentStatus: InternalAuditReportStatus.CLOSEOUT,
          previousStatus: IARFound.status,
          performer: performer,
          executedAt: new Date(),
        });

        for (const receiver of listReceiverNoti) {
          dataSendMail.push({
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: IARFound.refId,
              recordId: IARFound.id,
              path: ModulePathEnum.INTERNAL_AUDIT_REPORT,
              currentStatus: InternalAuditReportStatus.CLOSEOUT,
              previousStatus: IARFound.status,
              performer: performer,
              executedAt: new Date(),
            },
          });
        }

        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      LoggerCommon.error('[InternalAuditReportRepository] closeoutIAR error ', ex.message || ex);
      throw ex;
    }
  }

  //#endregion IAR

  //#region Common function
  private _handleIARReportHeaderDescription(
    IARFound: InternalAuditReport,
    newHeaderDescriptions: IARReportHeaderDescription[],
  ) {
    const listIARReportHeaders = IARFound.IARReportHeaders;
    // All current description by IAR
    const currentHeaderDescriptions: IARReportHeaderDescription[] = [];
    if (listIARReportHeaders && listIARReportHeaders.length > 0) {
      listIARReportHeaders.forEach((header: IARReportHeader) => {
        if (header.IARReportHeaderDescriptions && header.IARReportHeaderDescriptions.length > 0) {
          header.IARReportHeaderDescriptions.forEach((description: IARReportHeaderDescription) => {
            currentHeaderDescriptions.push(description);
          });
        }
      });
    }
    const listCurrentHeaderDescriptionIds: string[] = [];
    const listNewHeaderDescriptionIds: string[] = [];
    if (currentHeaderDescriptions && currentHeaderDescriptions.length > 0) {
      currentHeaderDescriptions.forEach((item: IARReportHeaderDescription) => {
        listCurrentHeaderDescriptionIds.push(item.id);
      });
    }
    if (newHeaderDescriptions && newHeaderDescriptions.length > 0) {
      newHeaderDescriptions.forEach((item: UpdateIARReportHeaderDTO) => {
        listNewHeaderDescriptionIds.push(item.id);
      });
    }

    const listHeaderDescriptionUpdateIds = MySet.intersect(
      new Set(listCurrentHeaderDescriptionIds),
      new Set(listNewHeaderDescriptionIds),
    );

    const listHeaderDescriptionCreateIds = MySet.difference(
      new Set(listNewHeaderDescriptionIds),
      new Set(listCurrentHeaderDescriptionIds),
    );

    const listHeaderDescriptionDeleteIds = MySet.difference(
      new Set(listCurrentHeaderDescriptionIds),
      new Set(listNewHeaderDescriptionIds),
    );

    const listHeaderDescriptionsCreate = newHeaderDescriptions.filter((item) =>
      listHeaderDescriptionCreateIds.has(item.id),
    );
    const listHeaderDescriptionsUpdate = newHeaderDescriptions.filter((item) =>
      listHeaderDescriptionUpdateIds.has(item.id),
    );

    return {
      listHeaderDescriptionsCreate,
      listHeaderDescriptionsUpdate,
      listHeaderDescriptionDeleteIds,
    };
  }

  async updateVerificationStatus(managerTrans: EntityManager, iarId: string) {
    const listROFItemByIAR = await managerTrans.find(ReportFindingItem, {
      where: { internalAuditReportId: iarId, deleted: false },
    });

    let numVerifiedAndClosedROFItem = 0;
    for (let i = 0; i < listROFItemByIAR.length; i++) {
      if (listROFItemByIAR[i].isVerify === true && listROFItemByIAR[i].isOpen === false) {
        numVerifiedAndClosedROFItem += 1;
      }
    }
    // check verification status
    let verificationStatus = IARVerificationStatus.YET_TO_VERIFIED;
    if (numVerifiedAndClosedROFItem === 0) {
      verificationStatus = IARVerificationStatus.YET_TO_VERIFIED;
    } else if (
      numVerifiedAndClosedROFItem > 0 &&
      numVerifiedAndClosedROFItem < listROFItemByIAR.length
    ) {
      verificationStatus = IARVerificationStatus.PARTIALLY_VERIFIED;
    } else if (numVerifiedAndClosedROFItem === listROFItemByIAR.length) {
      verificationStatus = IARVerificationStatus.ALL_VERIFIED;
    }

    // Update IAR verification status
    await managerTrans.update(
      InternalAuditReport,
      { id: iarId },
      { verificationStatus, verificationDate: new Date() },
    );
    return 1;
  }

  async checkUserCanUpdateRecord(userId: string, internalAuditReportId: string) {
    const userAssignments = await this.connection
      .getCustomRepository(UserAssignmentRepository)
      .find({
        where: {
          internalAuditReportId,
          userId,
        },
      });

    return userAssignments.length > 0;
  }

  async _migrateInternalAuditReport() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_AUDIT_REPORT,
      })
      .getOne();

    const metaConfigAdminCompany = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_AUDIT_REPORT_USER_ADMIN_COMPANY,
      })
      .getOne();
    // RUNNING ONLY ONE
    if (!metaConfig || !metaConfigAdminCompany) {
      return await this.connection.transaction(async (manager) => {
        const queryRaw = `SELECT pr.id, pr."vesselId" , pr."departmentId", pr."companyId", pr."createdUserId", pr."timezone"
        FROM planning_request pr 
        LEFT JOIN internal_audit_report iar on pr.id = iar."planningRequestId"
        WHERE pr.deleted = false  AND pr.status IN ('In-progress', 'approved') AND iar.id IS NULL;`;
        const planningRequests = await this.query(queryRaw);

        for (const planningRequest of planningRequests) {
          const {
            id,
            vesselId,
            departmentId,
            createdUserId,
            timezone,
            companyId,
          } = planningRequest;

          const createdUser = await this.manager
            .getCustomRepository(UserRepository)
            ._getUserInfoForHistory(createdUserId);
          let user;
          if (!createdUser) {
            user = await this.manager
              .getCustomRepository(UserRepository)
              ._getUserCompanyAdminInfoForHistory(companyId);

            const rawQuery = `UPDATE planning_request SET "createdUserId" = '${user.id}' WHERE "id" = '${planningRequest.id}'`;
            await this.query(rawQuery);
          }

          let selectedReportTemplates;

          if (vesselId) {
            selectedReportTemplates = await manager
              .getRepository(ReportTemplate)
              .createQueryBuilder('reportTemplate')
              .leftJoin('reportTemplate.vesselTypes', 'vesselTypes')
              .leftJoin('vesselTypes.vessels', 'vessels')
              .leftJoin('reportTemplate.auditTypes', 'auditTypes')
              .leftJoin('auditTypes.planningRequests', 'planningRequests')
              .leftJoinAndSelect(
                'reportTemplate.reportHeaders',
                'reportHeaders',
                'reportHeaders.deleted = false',
              )
              .where(
                `(reportTemplate.auditEntity = :auditEntity AND
              reportTemplate.deleted = false AND
              reportTemplate.status = 'active' AND
              vessels.id = :vesselId AND
              planningRequests.id = :planningRequestId)`,
                {
                  auditEntity: AuditEntity.VESSEL,
                  vesselId: vesselId,
                  planningRequestId: id,
                },
              )
              .select()
              .addSelect(['auditTypes.id', 'auditTypes.name'])
              .orderBy('reportTemplate.createdAt', 'DESC')
              .addOrderBy('reportHeaders.serialNumber', 'ASC')
              .getCount();
          } else {
            selectedReportTemplates = await manager
              .getRepository(ReportTemplate)
              .createQueryBuilder('reportTemplate')
              .leftJoin('reportTemplate.auditTypes', 'auditTypes')
              .leftJoin('auditTypes.planningRequests', 'planningRequests')
              .leftJoinAndSelect(
                'reportTemplate.reportHeaders',
                'reportHeaders',
                'reportHeaders.deleted = false',
              )
              .where(
                `reportTemplate.auditEntity = :auditEntity AND 
                reportTemplate.deleted = false AND
                reportTemplate.status = 'active' AND
                planningRequests.id = :planningRequestId`,
                {
                  auditEntity: AuditEntity.OFFICE,
                  planningRequestId: id,
                },
              )
              .select()
              .addSelect(['auditTypes.id', 'auditTypes.name'])
              .orderBy('reportTemplate.createdAt', 'DESC')
              .addOrderBy('reportHeaders.serialNumber', 'ASC')
              .getCount();
          }

          if (selectedReportTemplates && (createdUser || user)) {
            await manager
              .getCustomRepository(InternalAuditReportRepository)
              ._triggerCreateInternalAuditReport(
                manager,
                {
                  planningRequestId: id,
                  vesselId: vesselId,
                  departmentId: departmentId,
                  timezone,
                  companyId: companyId,
                },
                null,
                createdUser || user,
              );
          }
        }

        await manager.save(MetaConfig, {
          key: CatalogConst.MIGRATE_AUDIT_REPORT,
          lastTimeSync: '2023-07-25T04:20:00.000z',
        });
        await manager.save(MetaConfig, {
          key: CatalogConst.MIGRATE_AUDIT_REPORT_USER_ADMIN_COMPANY,
          lastTimeSync: '2023-07-25T04:20:00.000z',
        });
        console.log('Migrate done!');
      });
    }
    return 1;
  }

  _getLastReviewAndCountReassign(IARHistory: InternalAuditReportHistory[]) {
    if (IARHistory[0].status !== InternalAuditReportStatus.REASSIGNED) {
      return {};
    }
    let countReassign = 1;
    let maxReview = 1;
    for (let i = 1; i < IARHistory.length; i++) {
      if (IARHistory[i].status === InternalAuditReportStatus.REASSIGNED) {
        countReassign += 1;
      } else {
        maxReview =
          IARHistory[i].status === InternalAuditReportStatus.SUBMITTED
            ? 0
            : Number(IARHistory[i].status.substring(IARHistory[i].status.length - 1));
        break;
      }
    }
    return { countReassign, maxReview };
  }
}
