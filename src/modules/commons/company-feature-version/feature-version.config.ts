export const FeatureVersionConfig = {
  AUDIT_CHECKLIST: 'audit-checklist',
  REPORT_TEMPLATE_INTERNAL_AUDIT_VERSION: 'report-template-internal-audit-version',
  REPORT_TEMPLATE_INTERNAL_AUDIT_SERIAL_NUMBER: 'report-template-internal-audit-serial-number',
  VIQ_REF_NO: 'viq-ref-no',
  PLANNING_REQUEST_COUNTER: 'planning-request-counter',
  AUDIT_INSPECTION_WORKSPACE_REF_NO: 'audit-workspace-ref-no',
  AUDIT_TIME_TABLE_REFNO: 'audit-time-table-ref-no',
  AUDIT_TIME_TABLE_SNO: 'audit-time-table-s-no',
  REPORT_FINDING_FORM_REFNO: 'report-finding-form-refNo',
  REPORT_FINDING_FORM_SNO: 'report-finding-form-sNo',
  SELF_ASSESSMENT_SNO: 'self-assessment-s-no',
  INCIDENT_INVESTIGATION_COUNTER: 'incident-investigation-counter',
  VESSEL_SCREENING_REQUEST_NO: 'vessel-screening-request-no',
  SELF_DECLARATION_REF_ID: 'self-declaration-refId',
  FILL_AUDIT_CHECKLIST: 'fill-audit-checklist',
  PORT_STATE_CONTROL_REF_ID: 'port-state-control-refId',
  PILOT_TERMINAL_FEEDBACK_REF_ID: 'pilot-terminal-feedback-refId',
  VESSEL_COMPANY_FEEDBACK_REF_ID: 'vessel-company-feedback-refId',
  EXTERNAL_INSPECTIONS_REF_ID: 'external-inspections-refId',
  INTERNAL_AUDIT_REPORT_REF_ID: 'internal-audit-report-ref-id',
  INTERNAL_INSPECTIONS_REF_ID: 'internal-inspections-ref-id',
  PLAN_AND_DRAWING_REF_ID: 'plan-and-drawing-refId',
  AUDIT_WORKSPACE_SERIAL_CODE: 'audit-workspace-serial-code',
  FILL_AUDIT_CHECKLIST_INSTANCE: 'fill-audit-checklist-instance',
  CHECKLIST_PREFIX: 'checklist-prefix',
  FOLLOW_UP: 'follow-up',
  DISPENSATIONS: 'Dispensations',
  OTHER_TECHNICAL_RECORD: 'other-technical-record',
  DRY_DOCKING: 'dry-docking',
  OTHER_SMS_RECORD: 'other-sms-record',
  MAINTENANCE_PERFORMANCE_REF_ID: 'maintenance-performance-ref-id',
  SURVEY_CLASS_REF_ID: 'survey-class-ref-id',
  INJURIES_REF_ID: 'injuries-ref-id',
  CAUSE_ANALYSIS_REF_NO: 'cause-analysis-ref-no',
  // auto increment session id for mobile upload and download
  SESSION_ID: 'session-id',
};
