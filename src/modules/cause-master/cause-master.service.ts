import { Injectable } from '@nestjs/common';
import { omit } from 'lodash';
import { CauseMasterRepository } from './repositories/cause-master.repository';
import {
  ListCauseMasterDto,
  CreateCauseMasterDto,
  UpdateCauseMasterDto,
  CauseHierarchyDto,
} from './dto';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import { CauseMaster } from './entities/cause-master.entity';
import { CauseTypeEnum } from './enums/cause-master.enum';

@Injectable()
export class CauseMasterService {
  constructor(private readonly causeMasterRepository: CauseMasterRepository) {}

  async listCauseMaster(query: ListCauseMasterDto, token: TokenPayloadModel) {
    return this.causeMasterRepository.listCauseMaster(query, token);
  }

  async getDetailCauseMaster(user: TokenPayloadModel, causeMasterId: string) {
    return this.causeMasterRepository.getDetailCauseMaster(user.companyId, causeMasterId);
  }

  async getCauseHierarchy(user: TokenPayloadModel, causeMasterId: string): Promise<CauseHierarchyDto> {
    return this.causeMasterRepository.getCauseHierarchy(user.companyId, causeMasterId);
  }

  async createCauseMaster(user: TokenPayloadModel, body: CreateCauseMasterDto) {
    const preparedCauseMaster: CauseMaster = {
      ...body,
      companyId: user.companyId,
      createdUserId: user.id,
    } as CauseMaster;

    return this.causeMasterRepository.createCauseMaster(user, preparedCauseMaster);
  }

  async updateCauseMaster(
    user: TokenPayloadModel,
    causeMasterId: string,
    body: UpdateCauseMasterDto,
  ) {
    const updateData = {
      ...body,
      updatedUserId: user.id,
    };

    return this.causeMasterRepository.updateCauseMaster(user, causeMasterId, updateData);
  }

  async deleteCauseMaster(user: TokenPayloadModel, causeMasterId: string) {
    return this.causeMasterRepository.deleteCauseMaster(user, causeMasterId);
  }

  async activateCauseMaster(user: TokenPayloadModel, causeMasterId: string) {
    return this.causeMasterRepository.activateCauseMaster(user, causeMasterId);
  }

  async getActiveCausesByType(user: TokenPayloadModel, causeType: CauseTypeEnum) {
    return this.causeMasterRepository.getActiveCausesByType(user.companyId, causeType);
  }
}
