import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsEnum,
  IsOptional,
  IsUUID,
  MaxLength,
  IsInt,
  Min,
} from 'class-validator';
import { StatusCommon } from '../../../commons/enums';
import { CauseTypeEnum, PotentialRiskEnum } from '../enums/cause-master.enum';

export class UpdateCauseMasterDto {
  @ApiProperty({ enum: CauseTypeEnum, required: false })
  @IsOptional()
  @IsEnum(CauseTypeEnum)
  causeType?: CauseTypeEnum;

  @ApiProperty({ type: 'string', maxLength: 100, required: false })
  @IsOptional()
  @Transform(({ value }) => value?.trim())
  @MaxLength(100)
  mainCategory?: string;

  @ApiProperty({ type: 'number', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  referenceNo?: number;

  @ApiProperty({ enum: PotentialRiskEnum, required: false })
  @IsOptional()
  @IsEnum(PotentialRiskEnum)
  potentialRisk?: PotentialRiskEnum;

  @ApiProperty({ enum: StatusCommon, required: false })
  @IsOptional()
  @IsEnum(StatusCommon)
  status?: StatusCommon;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  parentId?: string;
}
