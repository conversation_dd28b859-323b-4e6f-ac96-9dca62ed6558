import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import { ListQueryDto } from '../../../commons/dtos';
import { CauseTypeEnum } from '../enums/cause-master.enum';

export class ListCauseMasterDto extends ListQueryDto {
  @ApiProperty({ enum: CauseTypeEnum, required: false })
  @IsOptional()
  @IsEnum(CauseTypeEnum)
  causeType?: CauseTypeEnum;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  parentId?: string;
}
