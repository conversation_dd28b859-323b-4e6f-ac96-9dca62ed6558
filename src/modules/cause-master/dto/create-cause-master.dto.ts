import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  MaxLength,
  IsInt,
  Min,
} from 'class-validator';
import { CauseTypeEnum, PotentialRiskEnum } from '../enums/cause-master.enum';

export class CreateCauseMasterDto {
  @ApiProperty({ enum: CauseTypeEnum, example: CauseTypeEnum.BASIC })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @IsEnum(CauseTypeEnum)
  causeType: CauseTypeEnum;

  @ApiProperty({ type: 'string', example: 'Human Error', maxLength: 100 })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @Transform(({ value }) => value?.trim())
  @MaxLength(100)
  mainCategory: string;

  @ApiProperty({ type: 'number', example: 1, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  referenceNo?: number;

  @ApiProperty({ enum: PotentialRiskEnum, example: PotentialRiskEnum.HIGH })
  @IsNotEmpty({ message: 'common.REQUIRED_FIELD' })
  @IsEnum(PotentialRiskEnum)
  potentialRisk: PotentialRiskEnum;

  @ApiProperty({ type: 'string', example: 'Description of the cause', required: false })
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  parentId?: string;
}
