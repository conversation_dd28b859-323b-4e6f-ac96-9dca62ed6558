import { ApiProperty } from '@nestjs/swagger';
import { CauseTypeEnum, PotentialRiskEnum } from '../enums/cause-master.enum';
import { StatusCommon } from '../../../commons/enums';

export class CauseHierarchyDto {
  @ApiProperty({ type: 'string' })
  id: string;

  @ApiProperty({ enum: CauseTypeEnum })
  causeType: CauseTypeEnum;

  @ApiProperty({ type: 'string' })
  mainCategory: string;

  @ApiProperty({ type: 'number', nullable: true })
  referenceNo: number;

  @ApiProperty({ enum: PotentialRiskEnum })
  potentialRisk: PotentialRiskEnum;

  @ApiProperty({ enum: StatusCommon })
  status: StatusCommon;

  @ApiProperty({ type: 'number' })
  level: number;

  @ApiProperty({ type: 'string', nullable: true })
  description: string;

  @ApiProperty({ type: 'string', nullable: true })
  parentId: string;

  @ApiProperty({ type: [CauseHierarchyDto], required: false })
  children?: CauseHierarchyDto[];
}
