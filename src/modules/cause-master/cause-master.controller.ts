import {
  Get,
  Controller,
  Query,
  HttpStatus,
  UseGuards,
  Post,
  Body,
  Put,
  Param,
  ParseUUIDPipe,
  Delete,
} from '@nestjs/common';
import {
  Roles,
  RoleScope,
  AuthGuard,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
  RequiredPermissions,
} from 'svm-nest-lib-v3';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';

import {
  ListCauseMasterDto,
  CreateCauseMasterDto,
  UpdateCauseMasterDto,
  CauseHierarchyDto,
} from './dto';
import { CauseMasterService } from './cause-master.service';
import { I18n, I18nContext } from 'nestjs-i18n';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';
import { CauseTypeEnum } from './enums/cause-master.enum';

@ApiTags('Cause Master')
@Controller('cause-master')
@UseGuards(AuthGuard, RolesGuard)
@ApiBearerAuth()
export class CauseMasterController {
  constructor(private readonly causeMasterService: CauseMasterService) {}

  @ApiQuery({ name: 'page', type: 'number', required: false })
  @ApiQuery({ name: 'pageSize', type: 'number', required: false })
  @ApiQuery({ name: 'content', type: 'string', required: false })
  @ApiQuery({ name: 'status', enum: ['active', 'inactive'], required: false })
  @ApiQuery({ name: 'causeType', enum: CauseTypeEnum, required: false })
  @ApiQuery({ name: 'parentId', type: 'string', required: false })
  @ApiQuery({ name: 'sort', type: 'string', required: false })
  @ApiResponse({ description: 'Get list cause master success', status: HttpStatus.OK })
  @ApiOperation({ summary: 'Get list cause master' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CAUSE_MASTER,
    action: ActionEnum.VIEW,
  })
  @Get('')
  async listCauseMaster(
    @TokenDecorator() user: TokenPayloadModel,
    @Query() query: ListCauseMasterDto,
  ) {
    return this.causeMasterService.listCauseMaster(query, user);
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({ description: 'Get cause master detail success', status: HttpStatus.OK })
  @ApiOperation({ summary: 'Get cause master detail' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CAUSE_MASTER,
    action: ActionEnum.VIEW,
  })
  @Get('/:id')
  async getDetailCauseMaster(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) causeMasterId: string,
  ) {
    return this.causeMasterService.getDetailCauseMaster(user, causeMasterId);
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Get cause master hierarchy success',
    status: HttpStatus.OK,
    type: CauseHierarchyDto,
  })
  @ApiOperation({ summary: 'Get cause master hierarchy tree view' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CAUSE_MASTER,
    action: ActionEnum.VIEW,
  })
  @Get('/:id/hierarchy')
  async getCauseHierarchy(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) causeMasterId: string,
  ) {
    return this.causeMasterService.getCauseHierarchy(user, causeMasterId);
  }

  @ApiResponse({ description: 'Create cause master success', status: HttpStatus.CREATED })
  @ApiOperation({ summary: 'Create cause master' })
  @ApiBody({ type: CreateCauseMasterDto, description: 'Create cause master object body' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CAUSE_MASTER,
    action: ActionEnum.CREATE,
  })
  @Post('')
  async createCauseMaster(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: CreateCauseMasterDto,
    @I18n() i18n: I18nContext,
  ) {
    const result = await this.causeMasterService.createCauseMaster(user, body);
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
      data: result,
    };
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiBody({ type: UpdateCauseMasterDto, description: 'Update cause master object body' })
  @ApiResponse({
    description: 'Update cause master success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Update cause master' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CAUSE_MASTER,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id')
  async updateCauseMaster(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) causeMasterId: string,
    @Body() body: UpdateCauseMasterDto,
    @I18n() i18n: I18nContext,
  ) {
    const result = await this.causeMasterService.updateCauseMaster(user, causeMasterId, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
      data: result,
    };
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Delete cause master success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Delete cause master' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CAUSE_MASTER,
    action: ActionEnum.DELETE,
  })
  @Delete('/:id')
  async deleteCauseMaster(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) causeMasterId: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.causeMasterService.deleteCauseMaster(user, causeMasterId);
    return {
      message: await i18n.t('common.DELETE_SUCCESS'),
    };
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Activate cause master success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Activate cause master and deactivate others of same type' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CAUSE_MASTER,
    action: ActionEnum.UPDATE,
  })
  @Put('/:id/activate')
  async activateCauseMaster(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) causeMasterId: string,
    @I18n() i18n: I18nContext,
  ) {
    const result = await this.causeMasterService.activateCauseMaster(user, causeMasterId);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
      data: result,
    };
  }

  @ApiQuery({ name: 'causeType', enum: CauseTypeEnum, required: true })
  @ApiResponse({
    description: 'Get active causes by type success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get active causes by type for incident module integration' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.CONFIGURATION_QA + '::' + SubFeatureEnum.CAUSE_MASTER,
    action: ActionEnum.VIEW,
  })
  @Get('/active/:causeType')
  async getActiveCausesByType(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('causeType') causeType: CauseTypeEnum,
  ) {
    return this.causeMasterService.getActiveCausesByType(user, causeType);
  }
}
