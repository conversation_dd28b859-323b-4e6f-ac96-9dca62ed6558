import { EntityRepository, Connection } from 'typeorm';
import { <PERSON><PERSON><PERSON>r, <PERSON>gger<PERSON>om<PERSON>, TokenPayloadModel, TypeORMRepository } from 'svm-nest-lib-v3';
import { CauseMaster } from '../entities/cause-master.entity';
import { ListCauseMasterDto, CauseHierarchyDto } from '../dto';
import { DBErrorCode } from '../../../commons/consts/db.const';
import { generateMultipleUniqueFieldsError } from '../../../commons/businesses';
import { StatusCommon } from '../../../commons/enums';
import { CauseTypeEnum } from '../enums/cause-master.enum';

@EntityRepository(CauseMaster)
export class CauseMasterRepository extends TypeORMRepository<CauseMaster> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async listCauseMaster(query: ListCauseMasterDto, token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('causeMaster')
      .leftJoin('causeMaster.createdUser', 'createdUser')
      .leftJoin('causeMaster.updatedUser', 'updatedUser')
      .leftJoin('causeMaster.company', 'company')
      .leftJoin('causeMaster.parent', 'parent')
      .where('(causeMaster.companyId = :companyId OR company.parentId = :companyId)', {
        companyId: token.companyId,
      })
      .select()
      .addSelect([
        'company.id',
        'company.name',
        'createdUser.username',
        'updatedUser.username',
        'parent.id',
        'parent.mainCategory',
      ]);

    if (query.companyId) {
      queryBuilder.andWhere('causeMaster.companyId = :companyId', {
        companyId: query.companyId,
      });
    }

    if (query.causeType) {
      queryBuilder.andWhere('causeMaster.causeType = :causeType', {
        causeType: query.causeType,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('causeMaster.status = :status', {
        status: query.status,
      });
    }

    if (query.parentId) {
      queryBuilder.andWhere('causeMaster.parentId = :parentId', {
        parentId: query.parentId,
      });
    }

    if (query.content) {
      queryBuilder.andWhere(
        '(causeMaster.mainCategory ILIKE :content OR causeMaster.description ILIKE :content)',
        {
          content: `%${query.content}%`,
        },
      );
    }

    return this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort: query.sort || 'causeMaster.referenceNo:1,causeMaster.createdAt:-1',
        advanceConditions: {
          createdAtFrom: query.createdAtFrom,
          createdAtTo: query.createdAtTo,
        },
      },
    );
  }

  async getDetailCauseMaster(companyId: string, causeMasterId: string) {
    const causeMaster = await this.createQueryBuilder('causeMaster')
      .leftJoinAndSelect('causeMaster.createdUser', 'createdUser')
      .leftJoinAndSelect('causeMaster.updatedUser', 'updatedUser')
      .leftJoinAndSelect('causeMaster.company', 'company')
      .leftJoinAndSelect('causeMaster.parent', 'parent')
      .leftJoinAndSelect('causeMaster.children', 'children')
      .where('causeMaster.id = :causeMasterId', { causeMasterId })
      .andWhere('(causeMaster.companyId = :companyId OR company.parentId = :companyId)', {
        companyId,
      })
      .getOne();

    if (!causeMaster) {
      throw new BaseError({ status: 404, message: 'causeMaster.NOT_FOUND' });
    }

    return causeMaster;
  }

  async getCauseHierarchy(companyId: string, causeMasterId: string): Promise<CauseHierarchyDto> {
    const causeMaster = await this.getDetailCauseMaster(companyId, causeMasterId);

    const buildHierarchy = async (cause: CauseMaster): Promise<CauseHierarchyDto> => {
      const children = await this.find({
        where: {
          parentId: cause.id,
          deleted: false,
          companyId,
        },
        order: { referenceNo: 'ASC', createdAt: 'ASC' },
      });

      const childrenHierarchy = await Promise.all(children.map((child) => buildHierarchy(child)));

      return {
        id: cause.id,
        causeType: cause.causeType,
        mainCategory: cause.mainCategory,
        referenceNo: cause.referenceNo,
        potentialRisk: cause.potentialRisk,
        status: cause.status,
        level: cause.level,
        description: cause.description,
        parentId: cause.parentId,
        children: childrenHierarchy.length > 0 ? childrenHierarchy : undefined,
      };
    };

    return buildHierarchy(causeMaster);
  }

  async createCauseMaster(user: TokenPayloadModel, entityParam: CauseMaster) {
    try {
      return await this.connection.transaction(async (manager) => {
        let newCauseMasterParams = Object.assign(entityParam, {
          createdUserId: user.id,
          companyId: user.companyId,
        });

        if (entityParam.parentId) {
          const parentCause = await this.getDetailCauseMaster(user.companyId, entityParam.parentId);
          if (parentCause.level >= 3) {
            throw new BaseError({ message: 'causeMaster.CANNOT_CREATE_MORE_THAN_3_LEVELS' });
          }
          newCauseMasterParams = Object.assign(newCauseMasterParams, {
            level: parentCause.level + 1,
            causeType: parentCause.causeType, // Inherit cause type from parent
          });
        }

        const newCauseMaster = await manager.save(CauseMaster, newCauseMasterParams);

        if (entityParam.parentId) {
          await manager.increment(
            CauseMaster,
            { id: entityParam.parentId, deleted: false },
            'numChildren',
            1,
          );
        }

        // Handle active record constraint for specific cause types
        if (
          newCauseMaster.status === StatusCommon.ACTIVE &&
          [
            CauseTypeEnum.BASIC,
            CauseTypeEnum.IMMEDIATE,
            CauseTypeEnum.CONTROL_ACTION_NEEDS,
          ].includes(newCauseMaster.causeType) &&
          !newCauseMaster.parentId
        ) {
          await this.deactivateOtherRecords(
            manager,
            user.companyId,
            newCauseMaster.causeType,
            newCauseMaster.id,
          );
        }

        return newCauseMaster;
      });
    } catch (ex) {
      LoggerCommon.error('[CauseMasterRepository] createCauseMaster error ', ex.message || ex);
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw await generateMultipleUniqueFieldsError(
          this,
          [
            {
              key: 'mainCategory',
              value: entityParam.mainCategory,
              errorMessage: 'causeMaster.MAIN_CATEGORY_EXISTED',
            },
          ],
          { key: 'companyId', value: user.companyId },
        );
      }
      throw ex;
    }
  }

  async updateCauseMaster(
    user: TokenPayloadModel,
    causeMasterId: string,
    entityParam: Partial<CauseMaster>,
  ) {
    try {
      return await this.connection.transaction(async (manager) => {
        const existingCause = await this.getDetailCauseMaster(user.companyId, causeMasterId);

        if (existingCause.numDependents > 0) {
          throw new BaseError({ message: 'causeMaster.CANNOT_UPDATE_HAS_DEPENDENTS' });
        }

        // Handle parent change
        if (entityParam.parentId !== undefined && entityParam.parentId !== existingCause.parentId) {
          if (entityParam.parentId) {
            const newParent = await this.getDetailCauseMaster(user.companyId, entityParam.parentId);
            if (newParent.level >= 3) {
              throw new BaseError({ message: 'causeMaster.CANNOT_CREATE_MORE_THAN_3_LEVELS' });
            }
            entityParam.level = newParent.level + 1;
            entityParam.causeType = newParent.causeType;
          } else {
            entityParam.level = 1;
          }

          // Update numChildren for old and new parents
          if (existingCause.parentId) {
            await manager.decrement(
              CauseMaster,
              { id: existingCause.parentId, deleted: false },
              'numChildren',
              1,
            );
          }
          if (entityParam.parentId) {
            await manager.increment(
              CauseMaster,
              { id: entityParam.parentId, deleted: false },
              'numChildren',
              1,
            );
          }
        }

        const updateData = {
          ...entityParam,
          updatedUserId: user.id,
        };

        await manager.update(
          CauseMaster,
          {
            id: causeMasterId,
            companyId: user.companyId,
            deleted: false,
          },
          updateData,
        );

        // Handle active record constraint
        if (
          updateData.status === StatusCommon.ACTIVE &&
          [
            CauseTypeEnum.BASIC,
            CauseTypeEnum.IMMEDIATE,
            CauseTypeEnum.CONTROL_ACTION_NEEDS,
          ].includes(existingCause.causeType) &&
          !existingCause.parentId
        ) {
          await this.deactivateOtherRecords(
            manager,
            user.companyId,
            existingCause.causeType,
            causeMasterId,
          );
        }

        return await this.getDetailCauseMaster(user.companyId, causeMasterId);
      });
    } catch (ex) {
      LoggerCommon.error('[CauseMasterRepository] updateCauseMaster error ', ex.message || ex);
      if (ex.code === DBErrorCode.UNIQUE_VIOLATION) {
        throw await generateMultipleUniqueFieldsError(
          this,
          [
            {
              key: 'mainCategory',
              value: entityParam.mainCategory,
              errorMessage: 'causeMaster.MAIN_CATEGORY_EXISTED',
            },
          ],
          { key: 'companyId', value: user.companyId },
          causeMasterId,
        );
      }
      throw ex;
    }
  }

  async deleteCauseMaster(user: TokenPayloadModel, causeMasterId: string) {
    return await this.connection.transaction(async (manager) => {
      const causeMaster = await this.getDetailCauseMaster(user.companyId, causeMasterId);

      if (causeMaster.numChildren > 0) {
        throw new BaseError({ message: 'causeMaster.CANNOT_DELETE_HAS_CHILDREN' });
      }

      if (causeMaster.numDependents > 0) {
        throw new BaseError({ message: 'causeMaster.CANNOT_DELETE_HAS_DEPENDENTS' });
      }

      // Soft delete
      await manager.update(
        CauseMaster,
        {
          id: causeMasterId,
          companyId: user.companyId,
        },
        {
          deleted: true,
          updatedUserId: user.id,
        },
      );

      // Update parent's numChildren
      if (causeMaster.parentId) {
        await manager.decrement(
          CauseMaster,
          { id: causeMaster.parentId, deleted: false },
          'numChildren',
          1,
        );
      }

      return { deleted: true };
    });
  }

  async activateCauseMaster(user: TokenPayloadModel, causeMasterId: string) {
    return await this.connection.transaction(async (manager) => {
      const causeMaster = await this.getDetailCauseMaster(user.companyId, causeMasterId);

      // Only allow activation for root level records of specific cause types
      if (
        [CauseTypeEnum.BASIC, CauseTypeEnum.IMMEDIATE, CauseTypeEnum.CONTROL_ACTION_NEEDS].includes(
          causeMaster.causeType,
        ) &&
        !causeMaster.parentId
      ) {
        await this.deactivateOtherRecords(
          manager,
          user.companyId,
          causeMaster.causeType,
          causeMasterId,
        );
      }

      await manager.update(
        CauseMaster,
        {
          id: causeMasterId,
          companyId: user.companyId,
          deleted: false,
        },
        {
          status: StatusCommon.ACTIVE,
          updatedUserId: user.id,
        },
      );

      return await this.getDetailCauseMaster(user.companyId, causeMasterId);
    });
  }

  private async deactivateOtherRecords(
    manager: any,
    companyId: string,
    causeType: CauseTypeEnum,
    excludeId: string,
  ) {
    await manager
      .createQueryBuilder()
      .update(CauseMaster)
      .set({ status: StatusCommon.INACTIVE })
      .where('companyId = :companyId', { companyId })
      .andWhere('causeType = :causeType', { causeType })
      .andWhere('parentId IS NULL')
      .andWhere('deleted = false')
      .andWhere('id != :excludeId', { excludeId })
      .execute();
  }

  async getActiveCausesByType(companyId: string, causeType: CauseTypeEnum) {
    return this.find({
      where: {
        companyId,
        causeType,
        status: StatusCommon.ACTIVE,
        deleted: false,
      },
      order: { referenceNo: 'ASC', createdAt: 'ASC' },
    });
  }
}
