import { Entity, Column, PrimaryGeneratedColumn, Index, ManyToOne, OneToMany } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';

import { StatusCommon } from '../../commons/enums';
import { DBIndexes } from '../../commons/consts/db.const';
import { Company } from '../company/company.entity';
import { User } from '../user/user.entity';
import { CauseTypeEnum, PotentialRiskEnum } from './enums/cause-master.enum';

@Entity()
@Index(DBIndexes.IDX_CAUSE_MASTER_CAUSETYPE_MAINCATEGORY_COMPANYID, ['causeType', 'mainCategory', 'companyId'], {
  unique: true,
  where: 'deleted = false AND parent_id IS NULL',
})
@Index(DBIndexes.IDX_CAUSE_MASTER_PARENTID_REFERENCENO, ['parentId', 'referenceNo'], {
  unique: true,
  where: 'deleted = false AND parent_id IS NOT NULL',
})
export class CauseMaster extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'enum', enum: CauseTypeEnum })
  public causeType: CauseTypeEnum;

  @Column({ type: 'varchar', length: 100 })
  public mainCategory: string;

  @Column({ type: 'integer', nullable: true })
  public referenceNo: number;

  @Column({ type: 'enum', enum: PotentialRiskEnum })
  public potentialRisk: PotentialRiskEnum;

  @Column({ type: 'enum', enum: StatusCommon, default: StatusCommon.ACTIVE })
  public status: StatusCommon;

  @Column({ type: 'uuid', nullable: true })
  public parentId: string;

  @Column({ type: 'smallint', default: 1 })
  public level: number;

  @Column({ type: 'smallint', default: 0 })
  public numChildren: number;

  @Column({ type: 'smallint', default: 0 })
  public numDependents: number;

  @Column({ nullable: true })
  public description: string;

  @Column({ type: 'uuid' })
  public companyId: string;

  @Column({ type: 'uuid', nullable: true })
  public createdUserId: string;

  @Column({ type: 'uuid', nullable: true })
  public updatedUserId?: string;

  // Relationships
  @ManyToOne(() => Company, { onDelete: 'CASCADE' })
  public company: Company;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  public createdUser: User;

  @ManyToOne(() => User, { onDelete: 'NO ACTION' })
  public updatedUser: User;

  @ManyToOne(() => CauseMaster, (causeMaster) => causeMaster.children, { onDelete: 'CASCADE' })
  public parent: CauseMaster;

  @OneToMany(() => CauseMaster, (causeMaster) => causeMaster.parent)
  public children: CauseMaster[];
}
