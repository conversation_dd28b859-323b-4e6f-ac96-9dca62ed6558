import { Test, TestingModule } from '@nestjs/testing';
import { CauseMasterService } from '../cause-master.service';
import { CauseMasterRepository } from '../cause-master.repository';
import { CauseMaster } from '../cause-master.entity';
import { CauseTypeEnum, PotentialRiskEnum } from '../enums/cause-master.enum';
import { StatusCommon } from '../../../commons/enums';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import {
  CreateCauseMasterDto,
  UpdateCauseMasterDto,
  ListCauseMasterDto,
  CauseHierarchyDto,
} from '../dto';

describe('CauseMasterService', () => {
  let service: CauseMasterService;
  let repository: CauseMasterRepository;

  const mockTokenPayload: TokenPayloadModel = {
    id: 'user-id-123',
    companyId: 'company-id-123',
    username: 'testuser',
    email: '<EMAIL>',
    roleScope: 'admin',
  } as TokenPayloadModel;

  const mockCauseMaster: CauseMaster = {
    id: 'cause-id-123',
    causeType: CauseTypeEnum.BASIC,
    mainCategory: 'Human Error',
    referenceNo: 1,
    potentialRisk: PotentialRiskEnum.HIGH,
    status: StatusCommon.ACTIVE,
    parentId: null,
    level: 1,
    numChildren: 0,
    numDependents: 0,
    description: 'Test description',
    companyId: 'company-id-123',
    createdUserId: 'user-id-123',
    updatedUserId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    deleted: false,
  } as CauseMaster;

  const mockCauseHierarchy: CauseHierarchyDto = {
    id: 'cause-id-123',
    causeType: CauseTypeEnum.BASIC,
    mainCategory: 'Human Error',
    referenceNo: 1,
    potentialRisk: PotentialRiskEnum.HIGH,
    status: StatusCommon.ACTIVE,
    level: 1,
    description: 'Test description',
    parentId: null,
    children: [],
  };

  const mockRepository = {
    listCauseMaster: jest.fn(),
    getDetailCauseMaster: jest.fn(),
    getCauseHierarchy: jest.fn(),
    createCauseMaster: jest.fn(),
    updateCauseMaster: jest.fn(),
    deleteCauseMaster: jest.fn(),
    activateCauseMaster: jest.fn(),
    getActiveCausesByType: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CauseMasterService,
        {
          provide: CauseMasterRepository,
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<CauseMasterService>(CauseMasterService);
    repository = module.get<CauseMasterRepository>(CauseMasterRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('listCauseMaster', () => {
    it('should return paginated list of cause masters', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        causeType: CauseTypeEnum.BASIC,
        status: StatusCommon.ACTIVE,
      };

      const expectedResult = {
        data: [mockCauseMaster],
        totalItems: 1,
        totalPages: 1,
        currentPage: 1,
      };

      mockRepository.listCauseMaster.mockResolvedValue(expectedResult);

      const result = await service.listCauseMaster(query, mockTokenPayload);

      expect(repository.listCauseMaster).toHaveBeenCalledWith(query, mockTokenPayload);
      expect(result).toEqual(expectedResult);
    });

    it('should handle empty results', async () => {
      const query: ListCauseMasterDto = { page: 1, pageSize: 10 };
      const expectedResult = {
        data: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };

      mockRepository.listCauseMaster.mockResolvedValue(expectedResult);

      const result = await service.listCauseMaster(query, mockTokenPayload);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('getDetailCauseMaster', () => {
    it('should return cause master details', async () => {
      const causeMasterId = 'cause-id-123';

      mockRepository.getDetailCauseMaster.mockResolvedValue(mockCauseMaster);

      const result = await service.getDetailCauseMaster(mockTokenPayload, causeMasterId);

      expect(repository.getDetailCauseMaster).toHaveBeenCalledWith(
        mockTokenPayload.companyId,
        causeMasterId,
      );
      expect(result).toEqual(mockCauseMaster);
    });
  });

  describe('getCauseHierarchy', () => {
    it('should return cause hierarchy tree', async () => {
      const causeMasterId = 'cause-id-123';

      mockRepository.getCauseHierarchy.mockResolvedValue(mockCauseHierarchy);

      const result = await service.getCauseHierarchy(mockTokenPayload, causeMasterId);

      expect(repository.getCauseHierarchy).toHaveBeenCalledWith(
        mockTokenPayload.companyId,
        causeMasterId,
      );
      expect(result).toEqual(mockCauseHierarchy);
    });
  });

  describe('createCauseMaster', () => {
    it('should create a new cause master', async () => {
      const createDto: CreateCauseMasterDto = {
        causeType: CauseTypeEnum.BASIC,
        mainCategory: 'Human Error',
        referenceNo: 1,
        potentialRisk: PotentialRiskEnum.HIGH,
        description: 'Test description',
      };

      mockRepository.createCauseMaster.mockResolvedValue(mockCauseMaster);

      const result = await service.createCauseMaster(mockTokenPayload, createDto);

      expect(repository.createCauseMaster).toHaveBeenCalledWith(
        mockTokenPayload,
        expect.objectContaining({
          ...createDto,
          companyId: mockTokenPayload.companyId,
          createdUserId: mockTokenPayload.id,
        }),
      );
      expect(result).toEqual(mockCauseMaster);
    });

    it('should create a subcategory with parent', async () => {
      const createDto: CreateCauseMasterDto = {
        causeType: CauseTypeEnum.BASIC,
        mainCategory: 'Subcategory',
        referenceNo: 1,
        potentialRisk: PotentialRiskEnum.MEDIUM,
        parentId: 'parent-id-123',
      };

      const subcategory = { ...mockCauseMaster, parentId: 'parent-id-123', level: 2 };
      mockRepository.createCauseMaster.mockResolvedValue(subcategory);

      const result = await service.createCauseMaster(mockTokenPayload, createDto);

      expect(repository.createCauseMaster).toHaveBeenCalledWith(
        mockTokenPayload,
        expect.objectContaining({
          ...createDto,
          companyId: mockTokenPayload.companyId,
          createdUserId: mockTokenPayload.id,
        }),
      );
      expect(result).toEqual(subcategory);
    });
  });

  describe('updateCauseMaster', () => {
    it('should update an existing cause master', async () => {
      const causeMasterId = 'cause-id-123';
      const updateDto: UpdateCauseMasterDto = {
        mainCategory: 'Updated Category',
        potentialRisk: PotentialRiskEnum.LOW,
        status: StatusCommon.INACTIVE,
      };

      const updatedCauseMaster = { ...mockCauseMaster, ...updateDto };
      mockRepository.updateCauseMaster.mockResolvedValue(updatedCauseMaster);

      const result = await service.updateCauseMaster(mockTokenPayload, causeMasterId, updateDto);

      expect(repository.updateCauseMaster).toHaveBeenCalledWith(
        mockTokenPayload,
        causeMasterId,
        expect.objectContaining({
          ...updateDto,
          updatedUserId: mockTokenPayload.id,
        }),
      );
      expect(result).toEqual(updatedCauseMaster);
    });
  });

  describe('deleteCauseMaster', () => {
    it('should delete a cause master', async () => {
      const causeMasterId = 'cause-id-123';
      const deleteResult = { deleted: true };

      mockRepository.deleteCauseMaster.mockResolvedValue(deleteResult);

      const result = await service.deleteCauseMaster(mockTokenPayload, causeMasterId);

      expect(repository.deleteCauseMaster).toHaveBeenCalledWith(mockTokenPayload, causeMasterId);
      expect(result).toEqual(deleteResult);
    });
  });

  describe('activateCauseMaster', () => {
    it('should activate a cause master', async () => {
      const causeMasterId = 'cause-id-123';
      const activatedCauseMaster = { ...mockCauseMaster, status: StatusCommon.ACTIVE };

      mockRepository.activateCauseMaster.mockResolvedValue(activatedCauseMaster);

      const result = await service.activateCauseMaster(mockTokenPayload, causeMasterId);

      expect(repository.activateCauseMaster).toHaveBeenCalledWith(mockTokenPayload, causeMasterId);
      expect(result).toEqual(activatedCauseMaster);
    });
  });

  describe('getActiveCausesByType', () => {
    it('should return active causes by type', async () => {
      const causeType = CauseTypeEnum.BASIC;
      const activeCauses = [mockCauseMaster];

      mockRepository.getActiveCausesByType.mockResolvedValue(activeCauses);

      const result = await service.getActiveCausesByType(mockTokenPayload, causeType);

      expect(repository.getActiveCausesByType).toHaveBeenCalledWith(
        mockTokenPayload.companyId,
        causeType,
      );
      expect(result).toEqual(activeCauses);
    });

    it('should return empty array when no active causes found', async () => {
      const causeType = CauseTypeEnum.IMMEDIATE;

      mockRepository.getActiveCausesByType.mockResolvedValue([]);

      const result = await service.getActiveCausesByType(mockTokenPayload, causeType);

      expect(result).toEqual([]);
    });
  });
});
