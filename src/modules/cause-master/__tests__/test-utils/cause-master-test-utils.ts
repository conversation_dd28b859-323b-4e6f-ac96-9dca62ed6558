import { Test, TestingModule } from '@nestjs/testing';
import { Connection, SelectQueryBuilder } from 'typeorm';
import { I18nContext } from 'nestjs-i18n';
import { CauseMaster } from '../../cause-master.entity';
import { CauseMasterService } from '../../cause-master.service';
import { CauseMasterRepository } from '../../cause-master.repository';
import { CauseMasterController } from '../../cause-master.controller';

/**
 * Creates a mock TypeORM Connection for testing
 */
export const createMockConnection = () => ({
  transaction: jest.fn(),
  createQueryBuilder: jest.fn(),
  manager: {
    save: jest.fn(),
    update: jest.fn(),
    increment: jest.fn(),
    decrement: jest.fn(),
    createQueryBuilder: jest.fn(),
  },
});

/**
 * Creates a mock QueryBuilder for testing
 */
export const createMockQueryBuilder = (): Partial<SelectQueryBuilder<CauseMaster>> => ({
  leftJoin: jest.fn().mockReturnThis(),
  leftJoinAndSelect: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  orWhere: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  addSelect: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  addOrderBy: jest.fn().mockReturnThis(),
  groupBy: jest.fn().mockReturnThis(),
  having: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  offset: jest.fn().mockReturnThis(),
  getOne: jest.fn(),
  getMany: jest.fn(),
  getManyAndCount: jest.fn(),
  getCount: jest.fn(),
  getRawOne: jest.fn(),
  getRawMany: jest.fn(),
  execute: jest.fn(),
  update: jest.fn().mockReturnThis(),
  set: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  values: jest.fn().mockReturnThis(),
  into: jest.fn().mockReturnThis(),
});

/**
 * Creates a mock I18nContext for testing
 */
export const createMockI18nContext = (): I18nContext => ({
  t: jest.fn().mockImplementation((key: string) => {
    const translations = {
      'common.CREATE_SUCCESS': 'Created successfully',
      'common.UPDATE_SUCCESS': 'Updated successfully',
      'common.DELETE_SUCCESS': 'Deleted successfully',
      'causeMaster.NOT_FOUND': 'Cause master not found',
      'causeMaster.MAIN_CATEGORY_EXISTED': 'Main category already exists',
      'causeMaster.CANNOT_CREATE_MORE_THAN_3_LEVELS': 'Cannot create more than 3 levels',
      'causeMaster.CANNOT_DELETE_HAS_CHILDREN': 'Cannot delete cause master with children',
      'causeMaster.CANNOT_DELETE_HAS_DEPENDENTS': 'Cannot delete cause master with dependents',
      'causeMaster.CANNOT_UPDATE_HAS_DEPENDENTS': 'Cannot update cause master with dependents',
    };
    return Promise.resolve(translations[key] || key);
  }),
  lang: 'en',
  service: {} as any,
}) as I18nContext;

/**
 * Creates a mock CauseMasterRepository for testing
 */
export const createMockRepository = () => ({
  listCauseMaster: jest.fn(),
  getDetailCauseMaster: jest.fn(),
  getCauseHierarchy: jest.fn(),
  createCauseMaster: jest.fn(),
  updateCauseMaster: jest.fn(),
  deleteCauseMaster: jest.fn(),
  activateCauseMaster: jest.fn(),
  getActiveCausesByType: jest.fn(),
  createQueryBuilder: jest.fn(),
  find: jest.fn(),
  findOne: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  list: jest.fn(),
});

/**
 * Creates a mock CauseMasterService for testing
 */
export const createMockService = () => ({
  listCauseMaster: jest.fn(),
  getDetailCauseMaster: jest.fn(),
  getCauseHierarchy: jest.fn(),
  createCauseMaster: jest.fn(),
  updateCauseMaster: jest.fn(),
  deleteCauseMaster: jest.fn(),
  activateCauseMaster: jest.fn(),
  getActiveCausesByType: jest.fn(),
});

/**
 * Creates a testing module for CauseMasterService
 */
export const createServiceTestingModule = async (
  mockRepository = createMockRepository(),
): Promise<TestingModule> => {
  return Test.createTestingModule({
    providers: [
      CauseMasterService,
      {
        provide: CauseMasterRepository,
        useValue: mockRepository,
      },
    ],
  }).compile();
};

/**
 * Creates a testing module for CauseMasterRepository
 */
export const createRepositoryTestingModule = async (
  mockConnection = createMockConnection(),
): Promise<TestingModule> => {
  return Test.createTestingModule({
    providers: [
      CauseMasterRepository,
      {
        provide: Connection,
        useValue: mockConnection,
      },
    ],
  }).compile();
};

/**
 * Creates a testing module for CauseMasterController
 */
export const createControllerTestingModule = async (
  mockService = createMockService(),
): Promise<TestingModule> => {
  return Test.createTestingModule({
    controllers: [CauseMasterController],
    providers: [
      {
        provide: CauseMasterService,
        useValue: mockService,
      },
    ],
  }).compile();
};

/**
 * Utility function to assert pagination results
 */
export const assertPaginationResult = (result: any, expectedData: any[], expectedTotal: number) => {
  expect(result).toHaveProperty('data');
  expect(result).toHaveProperty('totalItems');
  expect(result).toHaveProperty('totalPages');
  expect(result).toHaveProperty('currentPage');
  expect(result.data).toEqual(expectedData);
  expect(result.totalItems).toBe(expectedTotal);
};

/**
 * Utility function to assert hierarchy structure
 */
export const assertHierarchyStructure = (hierarchy: any, expectedId: string, expectedChildrenCount: number) => {
  expect(hierarchy).toHaveProperty('id', expectedId);
  expect(hierarchy).toHaveProperty('causeType');
  expect(hierarchy).toHaveProperty('mainCategory');
  expect(hierarchy).toHaveProperty('level');
  expect(hierarchy).toHaveProperty('parentId');
  
  if (expectedChildrenCount > 0) {
    expect(hierarchy).toHaveProperty('children');
    expect(hierarchy.children).toHaveLength(expectedChildrenCount);
  }
};

/**
 * Utility function to create test data with variations
 */
export const createTestCauseMaster = (overrides: Partial<CauseMaster> = {}): CauseMaster => {
  const base: CauseMaster = {
    id: 'test-id-123',
    causeType: 'Basic' as any,
    mainCategory: 'Test Category',
    referenceNo: 1,
    potentialRisk: 'High' as any,
    status: 'active' as any,
    parentId: null,
    level: 1,
    numChildren: 0,
    numDependents: 0,
    description: 'Test description',
    companyId: 'company-id-123',
    createdUserId: 'user-id-123',
    updatedUserId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    deleted: false,
    ...overrides,
  } as CauseMaster;

  return base;
};

/**
 * Utility function to simulate database transaction
 */
export const mockTransaction = (mockConnection: any, callback: (manager: any) => Promise<any>) => {
  const mockManager = {
    save: jest.fn(),
    update: jest.fn(),
    increment: jest.fn(),
    decrement: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnValue(createMockQueryBuilder()),
  };

  mockConnection.transaction.mockImplementation(async (transactionCallback: any) => {
    return transactionCallback(mockManager);
  });

  return mockManager;
};

/**
 * Utility function to assert error responses
 */
export const assertErrorResponse = (error: any, expectedMessage: string, expectedStatus?: number) => {
  expect(error).toBeDefined();
  expect(error.message).toContain(expectedMessage);
  if (expectedStatus) {
    expect(error.status).toBe(expectedStatus);
  }
};

/**
 * Utility function to assert success responses
 */
export const assertSuccessResponse = (response: any, expectedMessage: string, expectedData?: any) => {
  expect(response).toHaveProperty('message', expectedMessage);
  if (expectedData) {
    expect(response).toHaveProperty('data', expectedData);
  }
};

/**
 * Utility function to create mock validation errors
 */
export const createValidationError = (field: string, message: string) => {
  const error = new Error(`Validation failed`);
  (error as any).field = field;
  (error as any).validationMessage = message;
  return error;
};

/**
 * Utility function to create mock database errors
 */
export const createDatabaseError = (code: string, message: string) => {
  const error = new Error(message);
  (error as any).code = code;
  return error;
};

/**
 * Test data generators for different scenarios
 */
export const TestDataGenerators = {
  /**
   * Generates test data for hierarchy testing
   */
  generateHierarchyData: (levels: number = 3) => {
    const data: CauseMaster[] = [];
    let parentId: string | null = null;

    for (let level = 1; level <= levels; level++) {
      const causeMaster = createTestCauseMaster({
        id: `level-${level}-id`,
        mainCategory: `Level ${level} Category`,
        level,
        parentId,
        numChildren: level < levels ? 1 : 0,
      });
      data.push(causeMaster);
      parentId = causeMaster.id;
    }

    return data;
  },

  /**
   * Generates test data for different cause types
   */
  generateCauseTypeData: () => {
    const causeTypes = ['Basic', 'Immediate', 'Control Action Needs', 'Type of Loss'];
    return causeTypes.map((type, index) => 
      createTestCauseMaster({
        id: `${type.toLowerCase().replace(/\s+/g, '-')}-id`,
        causeType: type as any,
        mainCategory: `${type} Category`,
        referenceNo: index + 1,
      })
    );
  },

  /**
   * Generates test data for active/inactive scenarios
   */
  generateStatusData: () => {
    return [
      createTestCauseMaster({
        id: 'active-cause-id',
        mainCategory: 'Active Cause',
        status: 'active' as any,
      }),
      createTestCauseMaster({
        id: 'inactive-cause-id',
        mainCategory: 'Inactive Cause',
        status: 'inactive' as any,
      }),
    ];
  },
};
