import { Test, TestingModule } from '@nestjs/testing';
import { Connection, QueryBuilder, SelectQueryBuilder } from 'typeorm';
import { BaseError, TokenPayloadModel } from 'svm-nest-lib-v3';
import { CauseMasterRepository } from '../cause-master.repository';
import { CauseMaster } from '../cause-master.entity';
import { CauseTypeEnum, PotentialRiskEnum } from '../enums/cause-master.enum';
import { StatusCommon } from '../../../commons/enums';
import { ListCauseMasterDto, CauseHierarchyDto } from '../dto';
import { DBErrorCode } from '../../../commons/consts/db.const';

describe('CauseMasterRepository', () => {
  let repository: CauseMasterRepository;
  let connection: Connection;
  let queryBuilder: SelectQueryBuilder<CauseMaster>;

  const mockTokenPayload: TokenPayloadModel = {
    id: 'user-id-123',
    companyId: 'company-id-123',
    username: 'testuser',
    email: '<EMAIL>',
    roleScope: 'admin',
  } as TokenPayloadModel;

  const mockCauseMaster: CauseMaster = {
    id: 'cause-id-123',
    causeType: CauseTypeEnum.BASIC,
    mainCategory: 'Human Error',
    referenceNo: 1,
    potentialRisk: PotentialRiskEnum.HIGH,
    status: StatusCommon.ACTIVE,
    parentId: null,
    level: 1,
    numChildren: 0,
    numDependents: 0,
    description: 'Test description',
    companyId: 'company-id-123',
    createdUserId: 'user-id-123',
    updatedUserId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    deleted: false,
  } as CauseMaster;

  const mockQueryBuilder = {
    leftJoin: jest.fn().mockReturnThis(),
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
    getMany: jest.fn(),
    orderBy: jest.fn().mockReturnThis(),
    createQueryBuilder: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    execute: jest.fn(),
  };

  const mockConnection = {
    transaction: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
  };

  const mockManager = {
    save: jest.fn(),
    update: jest.fn(),
    increment: jest.fn(),
    decrement: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CauseMasterRepository,
        {
          provide: Connection,
          useValue: mockConnection,
        },
      ],
    }).compile();

    repository = module.get<CauseMasterRepository>(CauseMasterRepository);
    connection = module.get<Connection>(Connection);

    // Mock repository methods
    repository.createQueryBuilder = jest.fn().mockReturnValue(mockQueryBuilder);
    repository.list = jest.fn();
    repository.find = jest.fn();
    repository.getDetailCauseMaster = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('listCauseMaster', () => {
    it('should return paginated list with filters', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        causeType: CauseTypeEnum.BASIC,
        status: StatusCommon.ACTIVE,
        content: 'Human',
      };

      const expectedResult = {
        data: [mockCauseMaster],
        totalItems: 1,
        totalPages: 1,
        currentPage: 1,
      };

      (repository.list as jest.Mock).mockResolvedValue(expectedResult);

      const result = await repository.listCauseMaster(query, mockTokenPayload);

      expect(repository.createQueryBuilder).toHaveBeenCalledWith('causeMaster');
      expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('causeMaster.createdUser', 'createdUser');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        '(causeMaster.companyId = :companyId OR company.parentId = :companyId)',
        { companyId: mockTokenPayload.companyId },
      );
      expect(result).toEqual(expectedResult);
    });

    it('should apply cause type filter', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        causeType: CauseTypeEnum.IMMEDIATE,
      };

      (repository.list as jest.Mock).mockResolvedValue({ data: [], totalItems: 0 });

      await repository.listCauseMaster(query, mockTokenPayload);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('causeMaster.causeType = :causeType', {
        causeType: CauseTypeEnum.IMMEDIATE,
      });
    });

    it('should apply status filter', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        status: StatusCommon.INACTIVE,
      };

      (repository.list as jest.Mock).mockResolvedValue({ data: [], totalItems: 0 });

      await repository.listCauseMaster(query, mockTokenPayload);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('causeMaster.status = :status', {
        status: StatusCommon.INACTIVE,
      });
    });

    it('should apply content search filter', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        content: 'error',
      };

      (repository.list as jest.Mock).mockResolvedValue({ data: [], totalItems: 0 });

      await repository.listCauseMaster(query, mockTokenPayload);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(causeMaster.mainCategory ILIKE :content OR causeMaster.description ILIKE :content)',
        { content: '%error%' },
      );
    });

    it('should apply parent ID filter', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        parentId: 'parent-id-123',
      };

      (repository.list as jest.Mock).mockResolvedValue({ data: [], totalItems: 0 });

      await repository.listCauseMaster(query, mockTokenPayload);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('causeMaster.parentId = :parentId', {
        parentId: 'parent-id-123',
      });
    });
  });

  describe('getDetailCauseMaster', () => {
    it('should return cause master details', async () => {
      const causeMasterId = 'cause-id-123';
      const companyId = 'company-id-123';

      mockQueryBuilder.getOne.mockResolvedValue(mockCauseMaster);

      const result = await repository.getDetailCauseMaster(companyId, causeMasterId);

      expect(repository.createQueryBuilder).toHaveBeenCalledWith('causeMaster');
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith('causeMaster.createdUser', 'createdUser');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('causeMaster.id = :causeMasterId', { causeMasterId });
      expect(result).toEqual(mockCauseMaster);
    });

    it('should throw error when cause master not found', async () => {
      const causeMasterId = 'non-existent-id';
      const companyId = 'company-id-123';

      mockQueryBuilder.getOne.mockResolvedValue(null);

      await expect(repository.getDetailCauseMaster(companyId, causeMasterId)).rejects.toThrow(BaseError);
    });
  });

  describe('getCauseHierarchy', () => {
    it('should return hierarchical structure', async () => {
      const causeMasterId = 'cause-id-123';
      const companyId = 'company-id-123';

      const parentCause = { ...mockCauseMaster };
      const childCause = {
        ...mockCauseMaster,
        id: 'child-id-123',
        parentId: 'cause-id-123',
        level: 2,
        mainCategory: 'Child Category',
      };

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(parentCause);
      repository.find = jest.fn()
        .mockResolvedValueOnce([childCause])
        .mockResolvedValueOnce([]);

      const result = await repository.getCauseHierarchy(companyId, causeMasterId);

      expect(result).toEqual({
        id: parentCause.id,
        causeType: parentCause.causeType,
        mainCategory: parentCause.mainCategory,
        referenceNo: parentCause.referenceNo,
        potentialRisk: parentCause.potentialRisk,
        status: parentCause.status,
        level: parentCause.level,
        description: parentCause.description,
        parentId: parentCause.parentId,
        children: [
          {
            id: childCause.id,
            causeType: childCause.causeType,
            mainCategory: childCause.mainCategory,
            referenceNo: childCause.referenceNo,
            potentialRisk: childCause.potentialRisk,
            status: childCause.status,
            level: childCause.level,
            description: childCause.description,
            parentId: childCause.parentId,
          },
        ],
      });
    });
  });

  describe('createCauseMaster', () => {
    it('should create a new root level cause master', async () => {
      const entityParam = {
        causeType: CauseTypeEnum.BASIC,
        mainCategory: 'Human Error',
        referenceNo: 1,
        potentialRisk: PotentialRiskEnum.HIGH,
        status: StatusCommon.ACTIVE,
      } as CauseMaster;

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      mockManager.save.mockResolvedValue(mockCauseMaster);

      const result = await repository.createCauseMaster(mockTokenPayload, entityParam);

      expect(mockConnection.transaction).toHaveBeenCalled();
      expect(mockManager.save).toHaveBeenCalledWith(
        CauseMaster,
        expect.objectContaining({
          ...entityParam,
          createdUserId: mockTokenPayload.id,
          companyId: mockTokenPayload.companyId,
        }),
      );
      expect(result).toEqual(mockCauseMaster);
    });

    it('should create a subcategory with parent validation', async () => {
      const parentCause = { ...mockCauseMaster, level: 2 };
      const entityParam = {
        causeType: CauseTypeEnum.BASIC,
        mainCategory: 'Subcategory',
        parentId: 'parent-id-123',
      } as CauseMaster;

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(parentCause);

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      const subcategory = { ...mockCauseMaster, parentId: 'parent-id-123', level: 3 };
      mockManager.save.mockResolvedValue(subcategory);

      const result = await repository.createCauseMaster(mockTokenPayload, entityParam);

      expect(mockManager.save).toHaveBeenCalledWith(
        CauseMaster,
        expect.objectContaining({
          level: 3,
          causeType: parentCause.causeType,
        }),
      );
      expect(mockManager.increment).toHaveBeenCalledWith(
        CauseMaster,
        { id: entityParam.parentId, deleted: false },
        'numChildren',
        1,
      );
      expect(result).toEqual(subcategory);
    });

    it('should throw error when trying to create more than 3 levels', async () => {
      const parentCause = { ...mockCauseMaster, level: 3 };
      const entityParam = {
        parentId: 'parent-id-123',
      } as CauseMaster;

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(parentCause);

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await expect(repository.createCauseMaster(mockTokenPayload, entityParam)).rejects.toThrow(BaseError);
    });

    it('should handle unique constraint violation', async () => {
      const entityParam = {
        causeType: CauseTypeEnum.BASIC,
        mainCategory: 'Duplicate Category',
      } as CauseMaster;

      const uniqueError = new Error('Unique constraint violation');
      (uniqueError as any).code = DBErrorCode.UNIQUE_VIOLATION;

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      mockManager.save.mockRejectedValue(uniqueError);

      await expect(repository.createCauseMaster(mockTokenPayload, entityParam)).rejects.toThrow();
    });
  });

  describe('updateCauseMaster', () => {
    it('should update an existing cause master', async () => {
      const causeMasterId = 'cause-id-123';
      const entityParam = {
        mainCategory: 'Updated Category',
        potentialRisk: PotentialRiskEnum.LOW,
      };

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(mockCauseMaster);

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      const updatedCauseMaster = { ...mockCauseMaster, ...entityParam };
      repository.getDetailCauseMaster = jest.fn()
        .mockResolvedValueOnce(mockCauseMaster)
        .mockResolvedValueOnce(updatedCauseMaster);

      const result = await repository.updateCauseMaster(mockTokenPayload, causeMasterId, entityParam);

      expect(mockManager.update).toHaveBeenCalledWith(
        CauseMaster,
        {
          id: causeMasterId,
          companyId: mockTokenPayload.companyId,
          deleted: false,
        },
        expect.objectContaining({
          ...entityParam,
          updatedUserId: mockTokenPayload.id,
        }),
      );
      expect(result).toEqual(updatedCauseMaster);
    });

    it('should throw error when updating cause master with dependents', async () => {
      const causeMasterId = 'cause-id-123';
      const causeWithDependents = { ...mockCauseMaster, numDependents: 5 };

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(causeWithDependents);

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await expect(
        repository.updateCauseMaster(mockTokenPayload, causeMasterId, {}),
      ).rejects.toThrow(BaseError);
    });

    it('should handle parent change and update children counts', async () => {
      const causeMasterId = 'cause-id-123';
      const oldParentId = 'old-parent-id';
      const newParentId = 'new-parent-id';
      const existingCause = { ...mockCauseMaster, parentId: oldParentId };
      const newParent = { ...mockCauseMaster, id: newParentId, level: 1 };

      repository.getDetailCauseMaster = jest.fn()
        .mockResolvedValueOnce(existingCause)
        .mockResolvedValueOnce(newParent)
        .mockResolvedValueOnce({ ...existingCause, parentId: newParentId });

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await repository.updateCauseMaster(mockTokenPayload, causeMasterId, { parentId: newParentId });

      expect(mockManager.decrement).toHaveBeenCalledWith(
        CauseMaster,
        { id: oldParentId, deleted: false },
        'numChildren',
        1,
      );
      expect(mockManager.increment).toHaveBeenCalledWith(
        CauseMaster,
        { id: newParentId, deleted: false },
        'numChildren',
        1,
      );
    });
  });

  describe('deleteCauseMaster', () => {
    it('should soft delete a cause master', async () => {
      const causeMasterId = 'cause-id-123';

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(mockCauseMaster);

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      const result = await repository.deleteCauseMaster(mockTokenPayload, causeMasterId);

      expect(mockManager.update).toHaveBeenCalledWith(
        CauseMaster,
        {
          id: causeMasterId,
          companyId: mockTokenPayload.companyId,
        },
        {
          deleted: true,
          updatedUserId: mockTokenPayload.id,
        },
      );
      expect(result).toEqual({ deleted: true });
    });

    it('should throw error when deleting cause master with children', async () => {
      const causeMasterId = 'cause-id-123';
      const causeWithChildren = { ...mockCauseMaster, numChildren: 3 };

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(causeWithChildren);

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await expect(repository.deleteCauseMaster(mockTokenPayload, causeMasterId)).rejects.toThrow(BaseError);
    });

    it('should throw error when deleting cause master with dependents', async () => {
      const causeMasterId = 'cause-id-123';
      const causeWithDependents = { ...mockCauseMaster, numDependents: 2 };

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(causeWithDependents);

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await expect(repository.deleteCauseMaster(mockTokenPayload, causeMasterId)).rejects.toThrow(BaseError);
    });

    it('should update parent numChildren when deleting child', async () => {
      const causeMasterId = 'cause-id-123';
      const childCause = { ...mockCauseMaster, parentId: 'parent-id-123' };

      repository.getDetailCauseMaster = jest.fn().mockResolvedValue(childCause);

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await repository.deleteCauseMaster(mockTokenPayload, causeMasterId);

      expect(mockManager.decrement).toHaveBeenCalledWith(
        CauseMaster,
        { id: childCause.parentId, deleted: false },
        'numChildren',
        1,
      );
    });
  });

  describe('activateCauseMaster', () => {
    it('should activate a cause master and deactivate others', async () => {
      const causeMasterId = 'cause-id-123';
      const rootCause = { ...mockCauseMaster, parentId: null };

      repository.getDetailCauseMaster = jest.fn()
        .mockResolvedValueOnce(rootCause)
        .mockResolvedValueOnce({ ...rootCause, status: StatusCommon.ACTIVE });

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      const result = await repository.activateCauseMaster(mockTokenPayload, causeMasterId);

      expect(mockManager.update).toHaveBeenCalledWith(
        CauseMaster,
        {
          id: causeMasterId,
          companyId: mockTokenPayload.companyId,
          deleted: false,
        },
        {
          status: StatusCommon.ACTIVE,
          updatedUserId: mockTokenPayload.id,
        },
      );
      expect(result.status).toBe(StatusCommon.ACTIVE);
    });

    it('should not deactivate others for Type of Loss cause type', async () => {
      const causeMasterId = 'cause-id-123';
      const typeOfLossCause = { ...mockCauseMaster, causeType: CauseTypeEnum.TYPE_OF_LOSS, parentId: null };

      repository.getDetailCauseMaster = jest.fn()
        .mockResolvedValueOnce(typeOfLossCause)
        .mockResolvedValueOnce({ ...typeOfLossCause, status: StatusCommon.ACTIVE });

      mockConnection.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await repository.activateCauseMaster(mockTokenPayload, causeMasterId);

      // Should not call deactivateOtherRecords for Type of Loss
      expect(mockManager.createQueryBuilder).not.toHaveBeenCalled();
    });
  });

  describe('getActiveCausesByType', () => {
    it('should return active causes by type', async () => {
      const causeType = CauseTypeEnum.BASIC;
      const activeCauses = [mockCauseMaster];

      repository.find = jest.fn().mockResolvedValue(activeCauses);

      const result = await repository.getActiveCausesByType(mockTokenPayload.companyId, causeType);

      expect(repository.find).toHaveBeenCalledWith({
        where: {
          companyId: mockTokenPayload.companyId,
          causeType,
          status: StatusCommon.ACTIVE,
          deleted: false,
        },
        order: { referenceNo: 'ASC', createdAt: 'ASC' },
      });
      expect(result).toEqual(activeCauses);
    });

    it('should return empty array when no active causes found', async () => {
      const causeType = CauseTypeEnum.IMMEDIATE;

      repository.find = jest.fn().mockResolvedValue([]);

      const result = await repository.getActiveCausesByType(mockTokenPayload.companyId, causeType);

      expect(result).toEqual([]);
    });
  });
});
