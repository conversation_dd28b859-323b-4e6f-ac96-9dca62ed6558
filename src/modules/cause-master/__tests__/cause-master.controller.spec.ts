import { Test, TestingModule } from '@nestjs/testing';
import { CauseMasterController } from '../cause-master.controller';
import { CauseMasterService } from '../cause-master.service';
import { CauseMaster } from '../cause-master.entity';
import { CauseTypeEnum, PotentialRiskEnum } from '../enums/cause-master.enum';
import { StatusCommon } from '../../../commons/enums';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import { I18nContext } from 'nestjs-i18n';
import {
  CreateCauseMasterDto,
  UpdateCauseMasterDto,
  ListCauseMasterDto,
  CauseHierarchyDto,
} from '../dto';

describe('CauseMasterController', () => {
  let controller: CauseMasterController;
  let service: CauseMasterService;

  const mockTokenPayload: TokenPayloadModel = {
    id: 'user-id-123',
    companyId: 'company-id-123',
    username: 'testuser',
    email: '<EMAIL>',
    roleScope: 'admin',
  } as TokenPayloadModel;

  const mockCauseMaster: CauseMaster = {
    id: 'cause-id-123',
    causeType: CauseTypeEnum.BASIC,
    mainCategory: 'Human Error',
    referenceNo: 1,
    potentialRisk: PotentialRiskEnum.HIGH,
    status: StatusCommon.ACTIVE,
    parentId: null,
    level: 1,
    numChildren: 0,
    numDependents: 0,
    description: 'Test description',
    companyId: 'company-id-123',
    createdUserId: 'user-id-123',
    updatedUserId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    deleted: false,
  } as CauseMaster;

  const mockCauseHierarchy: CauseHierarchyDto = {
    id: 'cause-id-123',
    causeType: CauseTypeEnum.BASIC,
    mainCategory: 'Human Error',
    referenceNo: 1,
    potentialRisk: PotentialRiskEnum.HIGH,
    status: StatusCommon.ACTIVE,
    level: 1,
    description: 'Test description',
    parentId: null,
    children: [],
  };

  const mockI18nContext = {
    t: jest.fn().mockResolvedValue('Success message'),
  } as unknown as I18nContext;

  const mockService = {
    listCauseMaster: jest.fn(),
    getDetailCauseMaster: jest.fn(),
    getCauseHierarchy: jest.fn(),
    createCauseMaster: jest.fn(),
    updateCauseMaster: jest.fn(),
    deleteCauseMaster: jest.fn(),
    activateCauseMaster: jest.fn(),
    getActiveCausesByType: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CauseMasterController],
      providers: [
        {
          provide: CauseMasterService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<CauseMasterController>(CauseMasterController);
    service = module.get<CauseMasterService>(CauseMasterService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('listCauseMaster', () => {
    it('should return paginated list of cause masters', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        causeType: CauseTypeEnum.BASIC,
        status: StatusCommon.ACTIVE,
      };

      const expectedResult = {
        data: [mockCauseMaster],
        totalItems: 1,
        totalPages: 1,
        currentPage: 1,
      };

      mockService.listCauseMaster.mockResolvedValue(expectedResult);

      const result = await controller.listCauseMaster(mockTokenPayload, query);

      expect(service.listCauseMaster).toHaveBeenCalledWith(query, mockTokenPayload);
      expect(result).toEqual(expectedResult);
    });

    it('should handle filtering by cause type', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        causeType: CauseTypeEnum.IMMEDIATE,
      };

      mockService.listCauseMaster.mockResolvedValue({ data: [], totalItems: 0 });

      await controller.listCauseMaster(mockTokenPayload, query);

      expect(service.listCauseMaster).toHaveBeenCalledWith(query, mockTokenPayload);
    });

    it('should handle filtering by status', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        status: StatusCommon.INACTIVE,
      };

      mockService.listCauseMaster.mockResolvedValue({ data: [], totalItems: 0 });

      await controller.listCauseMaster(mockTokenPayload, query);

      expect(service.listCauseMaster).toHaveBeenCalledWith(query, mockTokenPayload);
    });

    it('should handle search by content', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        content: 'Human Error',
      };

      mockService.listCauseMaster.mockResolvedValue({ data: [mockCauseMaster], totalItems: 1 });

      await controller.listCauseMaster(mockTokenPayload, query);

      expect(service.listCauseMaster).toHaveBeenCalledWith(query, mockTokenPayload);
    });

    it('should handle filtering by parent ID', async () => {
      const query: ListCauseMasterDto = {
        page: 1,
        pageSize: 10,
        parentId: 'parent-id-123',
      };

      mockService.listCauseMaster.mockResolvedValue({ data: [], totalItems: 0 });

      await controller.listCauseMaster(mockTokenPayload, query);

      expect(service.listCauseMaster).toHaveBeenCalledWith(query, mockTokenPayload);
    });
  });

  describe('getDetailCauseMaster', () => {
    it('should return cause master details', async () => {
      const causeMasterId = 'cause-id-123';

      mockService.getDetailCauseMaster.mockResolvedValue(mockCauseMaster);

      const result = await controller.getDetailCauseMaster(mockTokenPayload, causeMasterId);

      expect(service.getDetailCauseMaster).toHaveBeenCalledWith(mockTokenPayload, causeMasterId);
      expect(result).toEqual(mockCauseMaster);
    });
  });

  describe('getCauseHierarchy', () => {
    it('should return cause hierarchy tree', async () => {
      const causeMasterId = 'cause-id-123';

      mockService.getCauseHierarchy.mockResolvedValue(mockCauseHierarchy);

      const result = await controller.getCauseHierarchy(mockTokenPayload, causeMasterId);

      expect(service.getCauseHierarchy).toHaveBeenCalledWith(mockTokenPayload, causeMasterId);
      expect(result).toEqual(mockCauseHierarchy);
    });

    it('should return hierarchy with children', async () => {
      const causeMasterId = 'cause-id-123';
      const hierarchyWithChildren = {
        ...mockCauseHierarchy,
        children: [
          {
            id: 'child-id-123',
            causeType: CauseTypeEnum.BASIC,
            mainCategory: 'Child Category',
            referenceNo: 1,
            potentialRisk: PotentialRiskEnum.MEDIUM,
            status: StatusCommon.ACTIVE,
            level: 2,
            description: 'Child description',
            parentId: 'cause-id-123',
          },
        ],
      };

      mockService.getCauseHierarchy.mockResolvedValue(hierarchyWithChildren);

      const result = await controller.getCauseHierarchy(mockTokenPayload, causeMasterId);

      expect(result.children).toHaveLength(1);
      expect(result.children[0].parentId).toBe(causeMasterId);
    });
  });

  describe('createCauseMaster', () => {
    it('should create a new cause master', async () => {
      const createDto: CreateCauseMasterDto = {
        causeType: CauseTypeEnum.BASIC,
        mainCategory: 'Human Error',
        referenceNo: 1,
        potentialRisk: PotentialRiskEnum.HIGH,
        description: 'Test description',
      };

      mockService.createCauseMaster.mockResolvedValue(mockCauseMaster);

      const result = await controller.createCauseMaster(mockTokenPayload, createDto, mockI18nContext);

      expect(service.createCauseMaster).toHaveBeenCalledWith(mockTokenPayload, createDto);
      expect(result).toEqual({
        message: 'Success message',
        data: mockCauseMaster,
      });
      expect(mockI18nContext.t).toHaveBeenCalledWith('common.CREATE_SUCCESS');
    });

    it('should create a subcategory', async () => {
      const createDto: CreateCauseMasterDto = {
        causeType: CauseTypeEnum.BASIC,
        mainCategory: 'Subcategory',
        referenceNo: 1,
        potentialRisk: PotentialRiskEnum.MEDIUM,
        parentId: 'parent-id-123',
      };

      const subcategory = { ...mockCauseMaster, parentId: 'parent-id-123', level: 2 };
      mockService.createCauseMaster.mockResolvedValue(subcategory);

      const result = await controller.createCauseMaster(mockTokenPayload, createDto, mockI18nContext);

      expect(service.createCauseMaster).toHaveBeenCalledWith(mockTokenPayload, createDto);
      expect(result.data.parentId).toBe('parent-id-123');
      expect(result.data.level).toBe(2);
    });

    it('should handle validation errors', async () => {
      const invalidDto = {
        // Missing required fields
        mainCategory: '',
      } as CreateCauseMasterDto;

      // This would be handled by validation pipes in real scenario
      await expect(async () => {
        await controller.createCauseMaster(mockTokenPayload, invalidDto, mockI18nContext);
      }).not.toThrow();
    });
  });

  describe('updateCauseMaster', () => {
    it('should update an existing cause master', async () => {
      const causeMasterId = 'cause-id-123';
      const updateDto: UpdateCauseMasterDto = {
        mainCategory: 'Updated Category',
        potentialRisk: PotentialRiskEnum.LOW,
        status: StatusCommon.INACTIVE,
      };

      const updatedCauseMaster = { ...mockCauseMaster, ...updateDto };
      mockService.updateCauseMaster.mockResolvedValue(updatedCauseMaster);

      const result = await controller.updateCauseMaster(
        mockTokenPayload,
        causeMasterId,
        updateDto,
        mockI18nContext,
      );

      expect(service.updateCauseMaster).toHaveBeenCalledWith(mockTokenPayload, causeMasterId, updateDto);
      expect(result).toEqual({
        message: 'Success message',
        data: updatedCauseMaster,
      });
      expect(mockI18nContext.t).toHaveBeenCalledWith('common.UPDATE_SUCCESS');
    });

    it('should handle partial updates', async () => {
      const causeMasterId = 'cause-id-123';
      const updateDto: UpdateCauseMasterDto = {
        description: 'Updated description only',
      };

      const updatedCauseMaster = { ...mockCauseMaster, description: 'Updated description only' };
      mockService.updateCauseMaster.mockResolvedValue(updatedCauseMaster);

      const result = await controller.updateCauseMaster(
        mockTokenPayload,
        causeMasterId,
        updateDto,
        mockI18nContext,
      );

      expect(service.updateCauseMaster).toHaveBeenCalledWith(mockTokenPayload, causeMasterId, updateDto);
      expect(result.data.description).toBe('Updated description only');
    });

    it('should handle parent change', async () => {
      const causeMasterId = 'cause-id-123';
      const updateDto: UpdateCauseMasterDto = {
        parentId: 'new-parent-id-123',
      };

      const updatedCauseMaster = { ...mockCauseMaster, parentId: 'new-parent-id-123', level: 2 };
      mockService.updateCauseMaster.mockResolvedValue(updatedCauseMaster);

      const result = await controller.updateCauseMaster(
        mockTokenPayload,
        causeMasterId,
        updateDto,
        mockI18nContext,
      );

      expect(result.data.parentId).toBe('new-parent-id-123');
      expect(result.data.level).toBe(2);
    });
  });

  describe('deleteCauseMaster', () => {
    it('should delete a cause master', async () => {
      const causeMasterId = 'cause-id-123';

      mockService.deleteCauseMaster.mockResolvedValue({ deleted: true });

      const result = await controller.deleteCauseMaster(mockTokenPayload, causeMasterId, mockI18nContext);

      expect(service.deleteCauseMaster).toHaveBeenCalledWith(mockTokenPayload, causeMasterId);
      expect(result).toEqual({
        message: 'Success message',
      });
      expect(mockI18nContext.t).toHaveBeenCalledWith('common.DELETE_SUCCESS');
    });
  });

  describe('activateCauseMaster', () => {
    it('should activate a cause master', async () => {
      const causeMasterId = 'cause-id-123';
      const activatedCauseMaster = { ...mockCauseMaster, status: StatusCommon.ACTIVE };

      mockService.activateCauseMaster.mockResolvedValue(activatedCauseMaster);

      const result = await controller.activateCauseMaster(
        mockTokenPayload,
        causeMasterId,
        mockI18nContext,
      );

      expect(service.activateCauseMaster).toHaveBeenCalledWith(mockTokenPayload, causeMasterId);
      expect(result).toEqual({
        message: 'Success message',
        data: activatedCauseMaster,
      });
      expect(mockI18nContext.t).toHaveBeenCalledWith('common.UPDATE_SUCCESS');
    });

    it('should handle activation with auto-deactivation', async () => {
      const causeMasterId = 'cause-id-123';
      const activatedCauseMaster = { ...mockCauseMaster, status: StatusCommon.ACTIVE };

      mockService.activateCauseMaster.mockResolvedValue(activatedCauseMaster);

      const result = await controller.activateCauseMaster(
        mockTokenPayload,
        causeMasterId,
        mockI18nContext,
      );

      expect(result.data.status).toBe(StatusCommon.ACTIVE);
    });
  });

  describe('getActiveCausesByType', () => {
    it('should return active causes by type', async () => {
      const causeType = CauseTypeEnum.BASIC;
      const activeCauses = [mockCauseMaster];

      mockService.getActiveCausesByType.mockResolvedValue(activeCauses);

      const result = await controller.getActiveCausesByType(mockTokenPayload, causeType);

      expect(service.getActiveCausesByType).toHaveBeenCalledWith(mockTokenPayload, causeType);
      expect(result).toEqual(activeCauses);
    });

    it('should return empty array for cause type with no active records', async () => {
      const causeType = CauseTypeEnum.IMMEDIATE;

      mockService.getActiveCausesByType.mockResolvedValue([]);

      const result = await controller.getActiveCausesByType(mockTokenPayload, causeType);

      expect(result).toEqual([]);
    });

    it('should handle all cause types', async () => {
      const causeTypes = [
        CauseTypeEnum.BASIC,
        CauseTypeEnum.IMMEDIATE,
        CauseTypeEnum.CONTROL_ACTION_NEEDS,
        CauseTypeEnum.TYPE_OF_LOSS,
      ];

      for (const causeType of causeTypes) {
        mockService.getActiveCausesByType.mockResolvedValue([mockCauseMaster]);

        const result = await controller.getActiveCausesByType(mockTokenPayload, causeType);

        expect(service.getActiveCausesByType).toHaveBeenCalledWith(mockTokenPayload, causeType);
        expect(result).toEqual([mockCauseMaster]);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors in listCauseMaster', async () => {
      const query: ListCauseMasterDto = { page: 1, pageSize: 10 };
      const error = new Error('Database connection failed');

      mockService.listCauseMaster.mockRejectedValue(error);

      await expect(controller.listCauseMaster(mockTokenPayload, query)).rejects.toThrow(error);
    });

    it('should handle service errors in createCauseMaster', async () => {
      const createDto: CreateCauseMasterDto = {
        causeType: CauseTypeEnum.BASIC,
        mainCategory: 'Test',
        potentialRisk: PotentialRiskEnum.HIGH,
      };
      const error = new Error('Unique constraint violation');

      mockService.createCauseMaster.mockRejectedValue(error);

      await expect(
        controller.createCauseMaster(mockTokenPayload, createDto, mockI18nContext),
      ).rejects.toThrow(error);
    });

    it('should handle service errors in updateCauseMaster', async () => {
      const causeMasterId = 'cause-id-123';
      const updateDto: UpdateCauseMasterDto = { mainCategory: 'Updated' };
      const error = new Error('Record not found');

      mockService.updateCauseMaster.mockRejectedValue(error);

      await expect(
        controller.updateCauseMaster(mockTokenPayload, causeMasterId, updateDto, mockI18nContext),
      ).rejects.toThrow(error);
    });

    it('should handle service errors in deleteCauseMaster', async () => {
      const causeMasterId = 'cause-id-123';
      const error = new Error('Cannot delete record with dependencies');

      mockService.deleteCauseMaster.mockRejectedValue(error);

      await expect(
        controller.deleteCauseMaster(mockTokenPayload, causeMasterId, mockI18nContext),
      ).rejects.toThrow(error);
    });

    it('should handle service errors in activateCauseMaster', async () => {
      const causeMasterId = 'cause-id-123';
      const error = new Error('Record not found');

      mockService.activateCauseMaster.mockRejectedValue(error);

      await expect(
        controller.activateCauseMaster(mockTokenPayload, causeMasterId, mockI18nContext),
      ).rejects.toThrow(error);
    });

    it('should handle service errors in getActiveCausesByType', async () => {
      const causeType = CauseTypeEnum.BASIC;
      const error = new Error('Database query failed');

      mockService.getActiveCausesByType.mockRejectedValue(error);

      await expect(controller.getActiveCausesByType(mockTokenPayload, causeType)).rejects.toThrow(error);
    });
  });

  describe('Input Validation', () => {
    it('should handle invalid UUID in getDetailCauseMaster', async () => {
      // This would be handled by ParseUUIDPipe in real scenario
      const invalidId = 'invalid-uuid';

      mockService.getDetailCauseMaster.mockResolvedValue(mockCauseMaster);

      // In real scenario, ParseUUIDPipe would throw before reaching controller
      await expect(async () => {
        await controller.getDetailCauseMaster(mockTokenPayload, invalidId);
      }).not.toThrow();
    });

    it('should handle invalid UUID in updateCauseMaster', async () => {
      const invalidId = 'invalid-uuid';
      const updateDto: UpdateCauseMasterDto = { mainCategory: 'Updated' };

      mockService.updateCauseMaster.mockResolvedValue(mockCauseMaster);

      // In real scenario, ParseUUIDPipe would throw before reaching controller
      await expect(async () => {
        await controller.updateCauseMaster(mockTokenPayload, invalidId, updateDto, mockI18nContext);
      }).not.toThrow();
    });

    it('should handle invalid UUID in deleteCauseMaster', async () => {
      const invalidId = 'invalid-uuid';

      mockService.deleteCauseMaster.mockResolvedValue({ deleted: true });

      // In real scenario, ParseUUIDPipe would throw before reaching controller
      await expect(async () => {
        await controller.deleteCauseMaster(mockTokenPayload, invalidId, mockI18nContext);
      }).not.toThrow();
    });
  });
});
