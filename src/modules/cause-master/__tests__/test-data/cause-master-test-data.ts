import { CauseMaster } from '../../cause-master.entity';
import { CauseTypeEnum, PotentialRiskEnum } from '../../enums/cause-master.enum';
import { StatusCommon } from '../../../../commons/enums';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import {
  CreateCauseMasterDto,
  UpdateCauseMasterDto,
  ListCauseMasterDto,
  CauseHierarchyDto,
} from '../../dto';

export const mockTokenPayload: TokenPayloadModel = {
  id: 'user-id-123',
  companyId: 'company-id-123',
  username: 'testuser',
  email: '<EMAIL>',
  roleScope: 'admin',
  firstName: 'Test',
  lastName: 'User',
  roles: ['admin'],
  permissions: [],
  companyName: 'Test Company',
} as TokenPayloadModel;

export const mockBasicCauseMaster: CauseMaster = {
  id: 'basic-cause-id-123',
  causeType: CauseTypeEnum.BASIC,
  mainCategory: 'Human Error',
  referenceNo: 1,
  potentialRisk: PotentialRiskEnum.HIGH,
  status: StatusCommon.ACTIVE,
  parentId: null,
  level: 1,
  numChildren: 2,
  numDependents: 0,
  description: 'Basic cause related to human error',
  companyId: 'company-id-123',
  createdUserId: 'user-id-123',
  updatedUserId: null,
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  deleted: false,
} as CauseMaster;

export const mockImmediateCauseMaster: CauseMaster = {
  id: 'immediate-cause-id-123',
  causeType: CauseTypeEnum.IMMEDIATE,
  mainCategory: 'Equipment Failure',
  referenceNo: 1,
  potentialRisk: PotentialRiskEnum.HIGH,
  status: StatusCommon.ACTIVE,
  parentId: null,
  level: 1,
  numChildren: 1,
  numDependents: 0,
  description: 'Immediate cause related to equipment failure',
  companyId: 'company-id-123',
  createdUserId: 'user-id-123',
  updatedUserId: null,
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  deleted: false,
} as CauseMaster;

export const mockControlActionCauseMaster: CauseMaster = {
  id: 'control-action-cause-id-123',
  causeType: CauseTypeEnum.CONTROL_ACTION_NEEDS,
  mainCategory: 'Inadequate Procedures',
  referenceNo: 1,
  potentialRisk: PotentialRiskEnum.MEDIUM,
  status: StatusCommon.ACTIVE,
  parentId: null,
  level: 1,
  numChildren: 0,
  numDependents: 0,
  description: 'Control action needs related to inadequate procedures',
  companyId: 'company-id-123',
  createdUserId: 'user-id-123',
  updatedUserId: null,
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  deleted: false,
} as CauseMaster;

export const mockTypeOfLossCauseMaster: CauseMaster = {
  id: 'type-of-loss-cause-id-123',
  causeType: CauseTypeEnum.TYPE_OF_LOSS,
  mainCategory: 'Property Damage',
  referenceNo: 1,
  potentialRisk: PotentialRiskEnum.HIGH,
  status: StatusCommon.ACTIVE,
  parentId: null,
  level: 1,
  numChildren: 0,
  numDependents: 0,
  description: 'Type of loss related to property damage',
  companyId: 'company-id-123',
  createdUserId: 'user-id-123',
  updatedUserId: null,
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  deleted: false,
} as CauseMaster;

export const mockSubcategory: CauseMaster = {
  id: 'subcategory-id-123',
  causeType: CauseTypeEnum.BASIC,
  mainCategory: 'Lack of Training',
  referenceNo: 1,
  potentialRisk: PotentialRiskEnum.MEDIUM,
  status: StatusCommon.ACTIVE,
  parentId: 'basic-cause-id-123',
  level: 2,
  numChildren: 1,
  numDependents: 0,
  description: 'Subcategory under human error',
  companyId: 'company-id-123',
  createdUserId: 'user-id-123',
  updatedUserId: null,
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  deleted: false,
} as CauseMaster;

export const mockSecondSubcategory: CauseMaster = {
  id: 'second-subcategory-id-123',
  causeType: CauseTypeEnum.BASIC,
  mainCategory: 'Inadequate Onboarding',
  referenceNo: 1,
  potentialRisk: PotentialRiskEnum.LOW,
  status: StatusCommon.ACTIVE,
  parentId: 'subcategory-id-123',
  level: 3,
  numChildren: 0,
  numDependents: 0,
  description: 'Second level subcategory',
  companyId: 'company-id-123',
  createdUserId: 'user-id-123',
  updatedUserId: null,
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  deleted: false,
} as CauseMaster;

export const mockInactiveCauseMaster: CauseMaster = {
  id: 'inactive-cause-id-123',
  causeType: CauseTypeEnum.BASIC,
  mainCategory: 'Inactive Cause',
  referenceNo: 2,
  potentialRisk: PotentialRiskEnum.LOW,
  status: StatusCommon.INACTIVE,
  parentId: null,
  level: 1,
  numChildren: 0,
  numDependents: 0,
  description: 'Inactive cause master',
  companyId: 'company-id-123',
  createdUserId: 'user-id-123',
  updatedUserId: 'user-id-123',
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-02T00:00:00Z'),
  deleted: false,
} as CauseMaster;

export const mockCreateCauseMasterDto: CreateCauseMasterDto = {
  causeType: CauseTypeEnum.BASIC,
  mainCategory: 'New Human Error Category',
  referenceNo: 3,
  potentialRisk: PotentialRiskEnum.HIGH,
  description: 'New cause master for testing',
};

export const mockCreateSubcategoryDto: CreateCauseMasterDto = {
  causeType: CauseTypeEnum.BASIC,
  mainCategory: 'New Subcategory',
  referenceNo: 2,
  potentialRisk: PotentialRiskEnum.MEDIUM,
  description: 'New subcategory for testing',
  parentId: 'basic-cause-id-123',
};

export const mockUpdateCauseMasterDto: UpdateCauseMasterDto = {
  mainCategory: 'Updated Category Name',
  potentialRisk: PotentialRiskEnum.LOW,
  description: 'Updated description',
  status: StatusCommon.INACTIVE,
};

export const mockListCauseMasterDto: ListCauseMasterDto = {
  page: 1,
  pageSize: 10,
  causeType: CauseTypeEnum.BASIC,
  status: StatusCommon.ACTIVE,
  content: 'Human',
};

export const mockCauseHierarchy: CauseHierarchyDto = {
  id: 'basic-cause-id-123',
  causeType: CauseTypeEnum.BASIC,
  mainCategory: 'Human Error',
  referenceNo: 1,
  potentialRisk: PotentialRiskEnum.HIGH,
  status: StatusCommon.ACTIVE,
  level: 1,
  description: 'Basic cause related to human error',
  parentId: null,
  children: [
    {
      id: 'subcategory-id-123',
      causeType: CauseTypeEnum.BASIC,
      mainCategory: 'Lack of Training',
      referenceNo: 1,
      potentialRisk: PotentialRiskEnum.MEDIUM,
      status: StatusCommon.ACTIVE,
      level: 2,
      description: 'Subcategory under human error',
      parentId: 'basic-cause-id-123',
      children: [
        {
          id: 'second-subcategory-id-123',
          causeType: CauseTypeEnum.BASIC,
          mainCategory: 'Inadequate Onboarding',
          referenceNo: 1,
          potentialRisk: PotentialRiskEnum.LOW,
          status: StatusCommon.ACTIVE,
          level: 3,
          description: 'Second level subcategory',
          parentId: 'subcategory-id-123',
        },
      ],
    },
  ],
};

export const mockPaginatedResult = {
  data: [mockBasicCauseMaster, mockImmediateCauseMaster],
  totalItems: 2,
  totalPages: 1,
  currentPage: 1,
  pageSize: 10,
};

export const mockEmptyPaginatedResult = {
  data: [],
  totalItems: 0,
  totalPages: 0,
  currentPage: 1,
  pageSize: 10,
};

export const mockActiveCausesByType = [
  mockBasicCauseMaster,
  mockImmediateCauseMaster,
  mockControlActionCauseMaster,
];

export const mockTypeOfLossActiveCauses = [
  mockTypeOfLossCauseMaster,
  {
    ...mockTypeOfLossCauseMaster,
    id: 'type-of-loss-2',
    mainCategory: 'Environmental Damage',
    referenceNo: 2,
  },
];

// Error scenarios
export const mockCauseWithChildren = {
  ...mockBasicCauseMaster,
  numChildren: 3,
};

export const mockCauseWithDependents = {
  ...mockBasicCauseMaster,
  numDependents: 5,
};

export const mockMaxLevelCause = {
  ...mockSecondSubcategory,
  level: 3,
};

// Database error mocks
export const mockUniqueConstraintError = {
  code: '23505', // PostgreSQL unique constraint violation
  message: 'duplicate key value violates unique constraint',
};

export const mockNotFoundError = {
  status: 404,
  message: 'causeMaster.NOT_FOUND',
};

export const mockValidationErrors = {
  mainCategoryTooLong: {
    mainCategory: 'a'.repeat(101), // Exceeds 100 char limit
  },
  invalidReferenceNo: {
    referenceNo: -1, // Negative number
  },
  missingRequiredFields: {
    // Missing causeType and potentialRisk
    mainCategory: 'Test Category',
  },
};
