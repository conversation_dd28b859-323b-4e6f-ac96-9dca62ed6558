# Cause Master Module Test Suite

This directory contains comprehensive unit tests for the Cause Master CRUD operations module (INAT-4127).

## Test Structure

```
__tests__/
├── cause-master.service.spec.ts      # Service layer tests
├── cause-master.repository.spec.ts   # Repository layer tests
├── cause-master.controller.spec.ts   # Controller layer tests
├── test-data/
│   └── cause-master-test-data.ts     # Mock data and test fixtures
├── test-utils/
│   └── cause-master-test-utils.ts    # Test utilities and helpers
└── README.md                         # This file
```

## Test Coverage

### Service Layer Tests (`cause-master.service.spec.ts`)
- ✅ **listCauseMaster**: Pagination, filtering, empty results
- ✅ **getDetailCauseMaster**: Retrieve specific cause master
- ✅ **getCauseHierarchy**: Tree structure retrieval
- ✅ **createCauseMaster**: Root level and subcategory creation
- ✅ **updateCauseMaster**: Partial and full updates
- ✅ **deleteCauseMaster**: Soft delete operations
- ✅ **activateCauseMaster**: Status activation with auto-deactivation
- ✅ **getActiveCausesByType**: Integration endpoint testing

### Repository Layer Tests (`cause-master.repository.spec.ts`)
- ✅ **Query Building**: Complex filtering and search operations
- ✅ **Hierarchical Operations**: 3-level hierarchy management
- ✅ **Business Logic**: Active record constraints and validation
- ✅ **Transaction Management**: Database transaction handling
- ✅ **Error Handling**: Unique constraints and validation errors
- ✅ **Cascade Operations**: Parent-child relationship management
- ✅ **Performance**: Efficient query patterns and indexing

### Controller Layer Tests (`cause-master.controller.spec.ts`)
- ✅ **All 8 REST Endpoints**: Complete API coverage
- ✅ **Request Validation**: DTO validation and parameter handling
- ✅ **Response Formatting**: Proper response structure
- ✅ **Error Handling**: Service error propagation
- ✅ **Authentication**: Token-based security
- ✅ **Internationalization**: i18n message handling

## Test Scenarios Covered

### Hierarchical Data Testing
- ✅ 3-level hierarchy creation and validation
- ✅ Parent-child relationship management
- ✅ Depth limit enforcement (max 3 levels)
- ✅ Tree structure building and traversal
- ✅ Cascade operations (delete, update)

### Active Record Constraint Testing
- ✅ Only one active per cause type (Basic, Immediate, Control Action Needs)
- ✅ Multiple active allowed for Type of Loss
- ✅ Auto-deactivation when activating new record
- ✅ Status toggle functionality

### Business Logic Testing
- ✅ Unique constraint enforcement (Cause Type + Main Category)
- ✅ Company-scoped data access
- ✅ Reference number uniqueness within parent
- ✅ Dependency checking before delete/update
- ✅ Level inheritance from parent

### Error Scenarios Testing
- ✅ Unique constraint violations
- ✅ Record not found (404 errors)
- ✅ Cannot delete with children/dependents
- ✅ Cannot update with dependents
- ✅ Maximum hierarchy depth exceeded
- ✅ Database connection failures

### Integration Testing
- ✅ Service-Repository interaction
- ✅ Controller-Service interaction
- ✅ Transaction rollback scenarios
- ✅ Concurrent operation handling

## Mock Data

### Test Entities
- **mockBasicCauseMaster**: Root level Basic cause
- **mockImmediateCauseMaster**: Root level Immediate cause
- **mockControlActionCauseMaster**: Control Action Needs cause
- **mockTypeOfLossCauseMaster**: Type of Loss cause
- **mockSubcategory**: Level 2 subcategory
- **mockSecondSubcategory**: Level 3 subcategory
- **mockInactiveCauseMaster**: Inactive cause master

### Test DTOs
- **mockCreateCauseMasterDto**: Valid creation data
- **mockCreateSubcategoryDto**: Subcategory creation data
- **mockUpdateCauseMasterDto**: Update operation data
- **mockListCauseMasterDto**: Filtering and pagination data

### Error Scenarios
- **mockCauseWithChildren**: Cannot delete scenario
- **mockCauseWithDependents**: Cannot update/delete scenario
- **mockMaxLevelCause**: Maximum depth scenario
- **mockUniqueConstraintError**: Database constraint violation
- **mockValidationErrors**: Input validation failures

## Running Tests

### Run All Tests
```bash
# Run all cause master tests
npm test -- --testPathPattern=cause-master

# Run with coverage
npm run test:cov -- --testPathPattern=cause-master
```

### Run Specific Test Files
```bash
# Service tests only
npm test -- cause-master.service.spec.ts

# Repository tests only
npm test -- cause-master.repository.spec.ts

# Controller tests only
npm test -- cause-master.controller.spec.ts
```

### Run in Watch Mode
```bash
# Watch mode for development
npm run test:watch -- --testPathPattern=cause-master
```

### Debug Tests
```bash
# Debug mode
npm run test:debug -- --testPathPattern=cause-master
```

## Test Utilities

### Mock Factories
- `createMockConnection()`: TypeORM connection mock
- `createMockQueryBuilder()`: Query builder mock
- `createMockI18nContext()`: Internationalization mock
- `createMockRepository()`: Repository mock
- `createMockService()`: Service mock

### Testing Module Factories
- `createServiceTestingModule()`: Service testing setup
- `createRepositoryTestingModule()`: Repository testing setup
- `createControllerTestingModule()`: Controller testing setup

### Assertion Helpers
- `assertPaginationResult()`: Validate pagination responses
- `assertHierarchyStructure()`: Validate tree structures
- `assertErrorResponse()`: Validate error handling
- `assertSuccessResponse()`: Validate success responses

### Data Generators
- `TestDataGenerators.generateHierarchyData()`: Multi-level test data
- `TestDataGenerators.generateCauseTypeData()`: All cause types
- `TestDataGenerators.generateStatusData()`: Active/inactive scenarios

## Expected Test Results

### Coverage Targets
- **Statements**: > 95%
- **Branches**: > 90%
- **Functions**: > 95%
- **Lines**: > 95%

### Test Count Summary
- **Service Tests**: 15+ test cases
- **Repository Tests**: 25+ test cases
- **Controller Tests**: 20+ test cases
- **Total**: 60+ comprehensive test cases

## Integration with CI/CD

These tests are designed to run in continuous integration environments:

```yaml
# Example GitHub Actions step
- name: Run Cause Master Tests
  run: |
    npm test -- --testPathPattern=cause-master --coverage
    npm run test:e2e -- --testNamePattern="Cause Master"
```

## Troubleshooting

### Common Issues
1. **Mock not working**: Ensure all dependencies are properly mocked
2. **Transaction tests failing**: Check mock transaction implementation
3. **Async test issues**: Ensure proper async/await usage
4. **Type errors**: Verify TypeScript types in test data

### Debug Tips
- Use `console.log()` in tests for debugging
- Check mock call counts with `expect().toHaveBeenCalledTimes()`
- Verify mock implementations with `mockImplementation()`
- Use Jest's `--verbose` flag for detailed output

## Contributing

When adding new tests:
1. Follow the existing test structure and naming conventions
2. Add comprehensive test data to `test-data/cause-master-test-data.ts`
3. Use test utilities from `test-utils/cause-master-test-utils.ts`
4. Ensure both positive and negative test cases
5. Update this README with new test scenarios
6. Maintain high test coverage standards
