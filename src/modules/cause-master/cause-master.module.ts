import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CauseMasterController } from './cause-master.controller';
import { CauseMasterService } from './cause-master.service';
import { CauseMasterRepository } from './repositories/cause-master.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([CauseMasterRepository]),
  ],
  controllers: [CauseMasterController],
  providers: [CauseMasterService],
  exports: [],
})
export class CauseMasterModule {}
