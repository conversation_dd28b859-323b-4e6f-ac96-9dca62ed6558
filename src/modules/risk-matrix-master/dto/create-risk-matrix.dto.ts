import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt, Min, IsEnum, IsOptional, IsString, MaxLength, ValidateNested, IsArray, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { StatusCommon } from '../../../commons/enums';

export class CreateRiskMatrixCellDTO {
  @ApiProperty({ description: 'Risk value mapping ID', required: false })
  @IsOptional()
  @IsString()
  riskValueMappingId?: string;

  @ApiProperty({
    type: [String],
    description: 'Array of cell positions like ["r1c1", "r2c2", "r3c3"]',
    example: ['r1c1', 'r2c2', 'r3c3']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  cellPositions?: string[];
}

export class CreateRiskValueMappingDTO {
  @ApiProperty({ description: 'Value ID', required: false })
  @IsOptional()
  @IsUUID()
  riskValueMappingId?: string;

  @ApiProperty({ description: 'Value key' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  riskValue: string;

  @ApiProperty({ description: 'Color (hex)', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(7)
  color?: string;
}

export class CreateRiskLevelMappingDTO {

  @ApiProperty({ description: 'Value ID', required: false })
  @IsOptional()
  @IsUUID()
  priorityMasterId?: string;

  @ApiProperty({
    type: [String],
    description: 'Array of RiskValueMapping UUIDs for multiple selection',
    example: ['uuid-1', 'uuid-2', 'uuid-3']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  riskValueMappingIds?: string[];

}

export class CreateRiskMatrixDTO {
  @ApiProperty({ description: 'Matrix code' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  matrixCode: string;

  @ApiProperty({
    type: Number,
    description: 'Number of rows in the risk matrix',
    minimum: 1,
  })
  @IsNotEmpty()
  @Type(() => Number)
  @IsInt()
  @Min(1, { message: 'Rows must be a positive integer' })
  rows: number;

  @ApiProperty({
    type: Number,
    description: 'Number of columns in the risk matrix',
    minimum: 1,
  })
  @IsNotEmpty()
  @Type(() => Number)
  @IsInt()
  @Min(1, { message: 'Columns must be a positive integer' })
  columns: number;

  @ApiProperty({
    type: [String],
    description: 'Array of row names like ["Probability", "Likelihood"]',
    example: ['Probability', 'Likelihood'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  rowsName?: string[];

  @ApiProperty({
    type: [String],
    description: 'Array of column names like ["Impact", "Severity"]',
    example: ['Impact', 'Severity'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  columnsName?: string[];

  @ApiProperty({
    enum: StatusCommon,
    description: 'Status of the risk matrix',
    required: false,
    default: StatusCommon.ACTIVE,
  })
  @IsOptional()
  @IsEnum(StatusCommon)
  status?: string;

  @ApiProperty({ type: [CreateRiskMatrixCellDTO], description: 'Matrix cells', required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRiskMatrixCellDTO)
  cells?: CreateRiskMatrixCellDTO[];

  @ApiProperty({ type: [CreateRiskValueMappingDTO], description: 'Value mappings', required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRiskValueMappingDTO)
  valueMappings?: CreateRiskValueMappingDTO[];

  @ApiProperty({ type: [CreateRiskLevelMappingDTO], description: 'Level mappings', required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRiskLevelMappingDTO)
  levelMappings?: CreateRiskLevelMappingDTO[];
}