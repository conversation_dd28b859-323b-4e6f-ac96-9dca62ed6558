import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, ManyToMany, JoinTable, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';
import { RiskMatrixMaster } from './risk-matrix-master.entity';
import { PriorityMaster } from '../../priority-master/priority-master.entity';

export enum RiskLevelEnum {
  NEGLIGIBLE = 'negligible',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

@Entity()
export class RiskLevelMapping extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public riskMatrixId: string;

  @Column({
    type: 'uuid',
    array: true,
    default: [],
  })
  public riskValueMappingIds: string[]; // Array of RiskValueMapping UUIDs

  @Column({ type: 'uuid', nullable: true })
  public priorityMasterId: string;

  // Relationships
  @ManyToOne(() => RiskMatrixMaster, (matrix) => matrix.levelMappings, { onDelete: 'CASCADE' })
  riskMatrix: RiskMatrixMaster;

  @ManyToOne(() => PriorityMaster, (priorityMaster) => priorityMaster.riskLevelMapping, { onDelete: 'CASCADE' })
  priorityMaster: PriorityMaster;

}
