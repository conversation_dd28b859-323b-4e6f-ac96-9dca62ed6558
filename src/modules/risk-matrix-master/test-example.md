# Risk Matrix Master - Test Examples

**Note**: These examples reflect the current DTO structure which supports multiple value selection through `riskValueMappingIds` arrays and uses `priorityMasterId` for level mappings.

## Example 1: Create a Complete Risk Matrix

```bash
POST /risk-matrix-master
Content-Type: application/json
Authorization: Bearer <token>

{
  "matrixCode": "RM001",
  "rows": 4,
  "columns": 4,
  "rowsName": [
        "Probability",
        "Likelihood",
        "Frequency",
        "Occurrence"
    ],
    "columnsName": [
        "Impact",
        "Severity",
        "Consequence",
        "Effect"
    ],
  "cells": [
    {
      "riskValueMappingId": "uuid-of-text-mapping",
      "cellPositions": ["r1c1", "r1c2"]
    },
    {
      "riskValueMappingId": "uuid-of-test2-mapping",
      "cellPositions": ["r4c4"]
    }
  ],
  "valueMappings": [
    {
      "riskValueMappingId": "uuid-of-text-mapping",
      "riskValue": "text",
      "color": "#0000FF"
    },
    {
      "riskValueMappingId": "uuid-of-test2-mapping",
      "riskValue": "test2",
      "color": "#FF00FF"
    }
  ],
  "levelMappings": [
    {
      "priorityMasterId": "negligible-priority-uuid",
      "riskValueMappingIds": ["uuid-of-text-mapping"]
    },
    {
      "priorityMasterId": "low-priority-uuid",
      "riskValueMappingIds": ["uuid-of-test2-mapping"]
    },
    {
      "priorityMasterId": "medium-priority-uuid",
      "riskValueMappingIds": ["uuid-of-text-mapping", "uuid-of-test2-mapping"]
    }
  ]
}
```

## Example 2: Get Matrix Configuration

```bash
GET /risk-matrix-master/{matrix-id}/configuration
Authorization: Bearer <token>
```

## Example 3: Configure Matrix Cells Separately

```bash
POST /risk-matrix-master/configure-cells
Content-Type: application/json
Authorization: Bearer <token>

{
  "riskMatrixId": "uuid-of-matrix",
  "cells": [
    {
      "riskValueMappingId": "uuid-of-text-mapping",
      "cellPositions": ["r1c1"]
    },
    {
      "riskValueMappingId": "uuid-of-test2-mapping",
      "cellPositions": ["r1c2", "r2c1"]
    },
    {
      "riskValueMappingId": "uuid-of-text-mapping",
      "cellPositions": ["r2c2"]
    }
  ]
}
```

## Example 4: Configure Value Mappings

```bash
POST /risk-matrix-master/configure-value-mappings
Content-Type: application/json
Authorization: Bearer <token>

{
  "riskMatrixId": "uuid-of-matrix",
  "valueMappings": [
    {
      "riskValueMappingId": "uuid-of-text-mapping",
      "riskValue": "text",
      "color": "#0000FF"
    },
    {
      "riskValueMappingId": "uuid-of-test2-mapping",
      "riskValue": "test2",
      "color": "#800080"
    }
  ]
}
```

## Example 5: Configure Level Mappings

```bash
POST /risk-matrix-master/configure-level-mappings
Content-Type: application/json
Authorization: Bearer <token>

{
  "riskMatrixId": "uuid-of-matrix",
  "levelMappings": [
    {
      "priorityMasterId": "negligible-priority-uuid",
      "riskValueMappingIds": ["uuid-of-text-mapping"]
    },
    {
      "priorityMasterId": "low-priority-uuid",
      "riskValueMappingIds": ["uuid-of-test2-mapping"]
    },
    {
      "priorityMasterId": "medium-priority-uuid",
      "riskValueMappingIds": ["uuid-of-text-mapping", "uuid-of-test2-mapping"]
    },
    {
      "priorityMasterId": "high-priority-uuid",
      "riskValueMappingIds": ["uuid-of-text-mapping"]
    }
  ]
}
```

## Testing Workflow

1. **Create Matrix**: Use Example 1 to create a complete risk matrix
2. **Verify Creation**: Use GET /risk-matrix-master to list matrices
3. **Get Configuration**: Use Example 2 to get full configuration
4. **Update Components**: Use Examples 3-5 to update individual components
5. **Test Validation**: Try creating duplicate matrix codes (should fail)
6. **Test Business Rules**: Try creating multiple active matrices (should fail)
