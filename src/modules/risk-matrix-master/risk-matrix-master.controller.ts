import {
  Param,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Controller,
  Query,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { I18n, I18nContext } from 'nestjs-i18n';
import {
  Roles,
  RoleScope,
  AuthGuard,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
  RequiredPermissions,
} from 'svm-nest-lib-v3';
import {
  ApiParam,
  ApiBody,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';

import { CreateRiskMatrixDTO, UpdateRiskMatrixDTO, ListRiskMatrixQueryDTO } from './dto';
import { RiskMatrixMasterService } from './risk-matrix-master.service';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';

@ApiTags('Risk Matrix Master')
@Controller('risk-matrix-master')
@ApiBearerAuth()
// @UseGuards(AuthGuard, RolesGuard)

export class RiskMatrixMasterController {
  constructor(private readonly riskMatrixMasterService: RiskMatrixMasterService) {}

  @ApiResponse({ description: 'Create risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Create risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Create risk matrix', operationId: 'createRiskMatrix' })
  @ApiBearerAuth()
  @ApiBody({ type: CreateRiskMatrixDTO })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.RISK_MATRIX,
  //   action: ActionEnum.CREATE,
  // })
  @Post()
  async createRiskMatrix(
    @Body() body: CreateRiskMatrixDTO,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    await this.riskMatrixMasterService.createRiskMatrix(body, token);
    return {
      message: await i18n.t('common.CREATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'List risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List risk matrix', operationId: 'listRiskMatrix' })
  @ApiBearerAuth()
  @ApiQuery({
    description: 'Paginate params',
    type: ListRiskMatrixQueryDTO,
    required: false,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.RISK_MATRIX,
  //   action: ActionEnum.VIEW,
  // })
  @Get()
  async listRiskMatrix(@Query() query: ListRiskMatrixQueryDTO, @TokenDecorator() token: TokenPayloadModel) {
    return this.riskMatrixMasterService.listRiskMatrix(query, token);
  }

  @ApiResponse({ description: 'Detail risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Detail risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Detail risk matrix', operationId: 'detailRiskMatrix' })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Risk matrix id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.RISK_MATRIX,
  //   action: ActionEnum.VIEW,
  // })
  @Get('/:id')
  async detailRiskMatrix(@Param('id', ParseUUIDPipe) id: string) {
    return this.riskMatrixMasterService.getDetailRiskMatrix(id);
  }

  @ApiResponse({ description: 'Update risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Update risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Update risk matrix', operationId: 'updateRiskMatrix' })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Risk matrix id',
  })
  @ApiBody({ type: UpdateRiskMatrixDTO })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.RISK_MATRIX,
  //   action: ActionEnum.UPDATE,
  // })
  @Put('/:id')
  async updateRiskMatrixById(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: UpdateRiskMatrixDTO,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    await this.riskMatrixMasterService.updateRiskMatrix(id, body, token);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Delete risk matrix success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Delete risk matrix error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Delete risk matrix', operationId: 'deleteRiskMatrix' })
  @ApiBearerAuth()
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Risk matrix id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.CONFIGURATION_COMMON + '::' + SubFeatureEnum.RISK_MATRIX,
  //   action: ActionEnum.DELETE,
  // })
  @Delete('/:id')
  async deleteRiskMatrix(
    @Param('id', ParseUUIDPipe) id: string,
    @I18n() i18n: I18nContext,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    await this.riskMatrixMasterService.deleteRiskMatrix(id, token.companyId);
    return {
      message: await i18n.t('common.DELETE_SUCCESS'),
    };
  }

}