import { EntityRepository, Not, Connection } from 'typeorm';
import { InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { TokenPayloadModel, CommonStatus, TypeORMRepository } from 'svm-nest-lib-v3';
import { RiskMatrixMaster } from './entities/risk-matrix-master.entity';
import { RiskMatrixCell } from './entities/risk-matrix-cell.entity';
import { RiskValueMapping } from './entities/risk-value-mapping.entity';
import { RiskLevelMapping } from './entities/risk-level-mapping.entity';
import { CreateRiskMatrixDTO, UpdateRiskMatrixDTO, ListRiskMatrixQueryDTO } from './dto';
import { StatusCommon } from '../../commons/enums';

@EntityRepository(RiskMatrixMaster)
export class RiskMatrixMasterRepository extends TypeORMRepository<RiskMatrixMaster> {
  constructor(
      private readonly connection: Connection,
    ) {
      super();
    }
  async createRiskMatrix(
    createRiskMatrixDto: CreateRiskMatrixDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixMaster> {

    try {
      return await this.connection.transaction(async (manager) => {
      const existingMatrixCode = await this.findOne({
        where: {
          matrixCode: createRiskMatrixDto.matrixCode,
          companyId: token.companyId,
          deleted: false,
        },
      });

      if (existingMatrixCode) {
        throw new BadRequestException('Matrix code already exists for this company');
      }
      // Check if there's already an active risk matrix for this company
      const existingActiveMatrix = await this.findOne({
        where: {
          companyId: token.companyId,
          status: StatusCommon.ACTIVE,
          deleted: false,
        },
      });
      if (existingActiveMatrix) {
        existingActiveMatrix.status = StatusCommon.INACTIVE; // <-- modify what you need
        await manager.save(existingActiveMatrix); // <-- save updates
      }

      // Create the main risk matrix
      const riskMatrix = this.create({
        matrixCode: createRiskMatrixDto?.matrixCode,
        rows: createRiskMatrixDto?.rows,
        columns: createRiskMatrixDto?.columns,
        rowsName: createRiskMatrixDto?.rowsName,
        columnsName: createRiskMatrixDto?.columnsName,
        status: StatusCommon.ACTIVE,
        companyId: token?.companyId,
        createdUserId: token.id,
      });

      const savedMatrix = await manager.save(riskMatrix);
// Create value mappings if provided
if (createRiskMatrixDto?.valueMappings && createRiskMatrixDto?.valueMappings?.length > 0) {
  const valueMappingRepository = this.manager.getRepository(RiskValueMapping);
  const valueMappings = createRiskMatrixDto?.valueMappings?.map(mappingDto =>
    valueMappingRepository.create({
      ...mappingDto,
      id: mappingDto?.riskValueMappingId,
      riskMatrixId: savedMatrix?.id,
    })
  );
  await manager.save(valueMappings);
}
      // Create matrix cells if provided
      if (createRiskMatrixDto.cells && createRiskMatrixDto.cells.length > 0) {
        const cellRepository = manager.getRepository(RiskMatrixCell);
        const cells = createRiskMatrixDto.cells.map(cellDto =>
          cellRepository.create({
            ...cellDto,
            riskMatrixId: savedMatrix.id,
          })
        );
        await manager.save(cells);
      }

      

      // Create level mappings if provided
      if (createRiskMatrixDto.levelMappings && createRiskMatrixDto.levelMappings.length > 0) {
        const levelMappingRepository = manager.getRepository(RiskLevelMapping);
        const levelMappings = createRiskMatrixDto.levelMappings.map(mappingDto =>
          levelMappingRepository.create({
            ...mappingDto,
            riskMatrixId: savedMatrix.id,
          })
        );
        await manager.save(levelMappings);
      }

      return savedMatrix;
    });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create risk matrix');
    }
  }

async listRiskMatrix(query: ListRiskMatrixQueryDTO, token: TokenPayloadModel) {
      const queryBuilder = this.createQueryBuilder('rm')
      .leftJoin('rm.company', 'company')
        .leftJoin('rm.createdUser', 'createdUser')
        .leftJoin('rm.updatedUser', 'updatedUser')
        .select()
        .addSelect(['company.id', 'company.name', 'createdUser.username', 'updatedUser.username'])
        .where('(rm.companyId = :companyId OR company.parentId = :companyId)', {
        companyId: token.companyId,
        });

    if (query.companyId) {
      queryBuilder.andWhere('rm.companyId = :companyId', {
        companyId: query.companyId,
      });
    }

    if (query.content) {
      queryBuilder.andWhere('(rm.matrixCode LIKE :content)', {
        content: `%${query.content}%`,
      });
    }

    if (query.status) {
      queryBuilder.andWhere('rm.status = :status', {
        status: query.status,
      });
    }

    if (query.createdAtFrom) {
      queryBuilder.andWhere('rm.createdAt >= :createdAtFrom', {
        createdAtFrom: query.createdAtFrom,
      });
    }

    if (query.createdAtTo) {
      queryBuilder.andWhere('rm.createdAt <= :createdAtTo', {
        createdAtTo: query.createdAtTo,
      });
    }

    const dataList = await this.list(
      { page: query.page, limit: query.pageSize },
      {
        queryBuilder,
        sort: query.sort || 'rm.createdAt:-1',
      },
    );

    return dataList;
  }

  async getDetailRiskMatrixById(id: string): Promise<RiskMatrixMaster> {
    try {
      const riskMatrix = await this.createQueryBuilder('rm')
        .leftJoin('rm.createdUser', 'createdUser')
        .leftJoin('rm.updatedUser', 'updatedUser')
        .leftJoinAndSelect('rm.cells', 'cells')
        .leftJoinAndSelect('rm.valueMappings', 'valueMappings')
        .leftJoinAndSelect('rm.levelMappings', 'levelMappings')
        .leftJoin('levelMappings.priorityMaster', 'priorityMaster')
        .addSelect(['createdUser.username', 'updatedUser.username','priorityMaster.risk'])
        .where('rm.id = :id', { id })
        .andWhere('rm.deleted = false')
        .getOne();

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      return riskMatrix;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get risk matrix details');
    }
  }

  async updateRiskMatrix(
    id: string,
    updateRiskMatrixDto: UpdateRiskMatrixDTO,
    token: TokenPayloadModel,
  ): Promise<RiskMatrixMaster> {
    try {
      const riskMatrix = await this.findOne({
        where: { id, companyId: token.companyId, deleted: false },
      });

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      // If updating to active status, check if there's already an active matrix
      if (
        updateRiskMatrixDto.status === StatusCommon.ACTIVE &&
        riskMatrix.status !== StatusCommon.ACTIVE
      ) {
        const existingActiveMatrix = await this.findOne({
          where: {
            companyId: token.companyId,
            status: StatusCommon.ACTIVE,
            deleted: false,
            id: Not(id),
          },
        });

        if (existingActiveMatrix) {
          throw new BadRequestException('Only one risk matrix can be active at a time');
        }
      }

      Object.assign(riskMatrix, {
        ...updateRiskMatrixDto,
        updatedUserId: token.id,
        updatedAt: new Date(),
      });

      return await this.save(riskMatrix);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update risk matrix');
    }
  }

  async deleteRiskMatrix(id: string, companyId: string): Promise<void> {
    try {
      const riskMatrix = await this.findOne({
        where: { id, companyId, deleted: false },
      });

      if (!riskMatrix) {
        throw new BadRequestException('Risk matrix not found');
      }

      riskMatrix.deleted = true;

      await this.save(riskMatrix);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete risk matrix');
    }
  }
}