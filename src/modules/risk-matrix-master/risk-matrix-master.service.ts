import { Injectable } from '@nestjs/common';
import { TokenPayloadModel } from 'svm-nest-lib-v3';

import { RiskMatrixMasterRepository } from './risk-matrix-master.repository';
import { CreateRiskMatrixDTO, UpdateRiskMatrixDTO, ListRiskMatrixQueryDTO } from './dto';

@Injectable()
export class RiskMatrixMasterService {
  constructor(private readonly riskMatrixMasterRepository: RiskMatrixMasterRepository) {}

  async createRiskMatrix(body: CreateRiskMatrixDTO, token: TokenPayloadModel) {
    return this.riskMatrixMasterRepository.createRiskMatrix(body, token);
  }

  async listRiskMatrix(query: ListRiskMatrixQueryDTO, token: TokenPayloadModel) {
    return this.riskMatrixMasterRepository.listRiskMatrix(query, token);
  }

  async getDetailRiskMatrix(id: string) {
    return this.riskMatrixMasterRepository.getDetailRiskMatrixById(id);
  }

  async updateRiskMatrix(id: string, params: UpdateRiskMatrixDTO, token: TokenPayloadModel) {
    return this.riskMatrixMasterRepository.updateRiskMatrix(id, params, token);
  }

  async deleteRiskMatrix(id: string, companyId: string) {
    return this.riskMatrixMasterRepository.deleteRiskMatrix(id, companyId);
  }
}