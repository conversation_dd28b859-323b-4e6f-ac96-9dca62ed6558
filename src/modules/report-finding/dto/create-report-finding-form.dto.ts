import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  MaxLength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { IsMomentTimeZoneName } from 'svm-nest-lib-v3';
import { decryptAttachmentValues } from '../../../commons/functions';
import { CreateReportFindingItemDto } from 'src/modules/audit-workspace/dto/create-report-finding.dto';
import { CreateSAReportFindingItemDto } from 'src/modules/audit-workspace/dto/fill-sa-checklist-question.dto';

export class ReportFindingItem2Dto {
  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  id: string;

  @ApiProperty({ type: 'string', required: true })
  @ValidateIf((o, v) => v !== null)
  @IsUUID('all')
  natureFindingId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsUUID('all')
  auditTypeId: string;

  @ApiProperty({ type: 'boolean', required: false })
  @IsBoolean()
  isSignificant: boolean;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsBoolean()
  rectifiedOnBoard: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isUpdatedFinding?: boolean;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @MaxLength(3000)
  findingRemark: string;

  @ApiProperty({ type: 'string', required: true })
  @ValidateIf((o, v) => v !== null)
  @IsNotEmpty()
  @MaxLength(3000)
  findingComment: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsNotEmpty()
  @MaxLength(500)
  reference: string;

  @ApiProperty({ type: 'string', required: true })
  @ValidateIf((o, v) => v !== null)
  @IsNotEmpty()
  @IsUUID('all')
  mainCategoryId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  secondCategoryId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  thirdCategoryId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  departmentId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  chkQuestionId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  viqId: string;

  @ApiProperty({ type: 'string', required: true })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  observedRiskId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  locationId: string; // for keeping location of finding items from Fill Checklist

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @Transform((value: string[]) => {
    const newValue = decryptAttachmentValues(value);
    return newValue;
  })
  findingAttachments?: string[] = [];
}

export class PreviousNCFindingItemDto {
  @ApiProperty({ type: 'string', required: false })
  @IsNotEmpty()
  @IsUUID('all')
  id: string;

  @ApiProperty({ type: 'boolean' })
  @IsNotEmpty()
  @IsBoolean()
  isVerify: boolean;

  @ApiProperty({ type: 'boolean' })
  @IsNotEmpty()
  @IsBoolean()
  isOpen: boolean;
}

export class CreateReportFindingFormDto {
  @ApiProperty({ type: 'string' })
  @IsNotEmpty()
  @IsUUID('all')
  planningRequestId: string;

  @ApiProperty({ type: 'string', example: 'Asia/Ho_Chi_Minh' })
  @IsNotEmpty()
  @IsMomentTimeZoneName()
  timezone: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @Transform((value: string[]) => {
    const newValue = decryptAttachmentValues(value);
    return newValue;
  })
  attachments?: string[] = [];

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @Transform((value: string[]) => {
    const newValue = decryptAttachmentValues(value);
    return newValue;
  })
  checklistAttachments?: string[] = [];

  @ApiProperty({
    type: [ReportFindingItem2Dto],
    description: 'List report finding item',
    required: true,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ReportFindingItem2Dto)
  @IsArray()
  reportFindingItems?: ReportFindingItem2Dto[] = [];

  @ApiProperty({
    type: [CreateSAReportFindingItemDto],
    description: 'List report finding item',
    required: true,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateSAReportFindingItemDto)
  @IsArray()
  saFindingItems?: CreateSAReportFindingItemDto[] = [];

  @ApiProperty({
    type: [PreviousNCFindingItemDto],
    description: 'List report finding item',
    required: true,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => PreviousNCFindingItemDto)
  @IsArray()
  previousNCFindings: PreviousNCFindingItemDto[] = [];

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsNotEmpty()
  @MaxLength(500)
  officeComment: string;

  @ApiProperty({ type: [String], required: false })
  @IsOptional()
  @IsArray()
  @MaxLength(500, { each: true })
  comments: string[] = [];

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(500)
  workflowRemark: string;

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isSubmit: boolean;
}

export class ReportFindingItem2MobileDto extends ReportFindingItem2Dto {
  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  chkQuestionId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  thirdCategoryId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @MaxLength(500)
  remark: string;
}

export class SAFindingItemsDto {
  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @IsUUID('all')
  id?: string;

  @ApiProperty({ type: 'string', required: true })
  @ValidateIf((o, v) => v !== null)
  @IsUUID('all')
  natureFindingId: string;

  @ApiProperty({ type: 'string', required: false })
  @ValidateIf((o, v) => v !== null)
  @IsOptional()
  @IsUUID('all')
  auditTypeId: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(3000)
  findingRemark: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  elementStageQ: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  elementName: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(3000)
  keyPerformanceIndicator: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  natureFindingName: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  @MaxLength(3000)
  remarks: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  auditTypeName: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  complianceId: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  elementMasterId: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  auditorCompliance: string;

  @ApiProperty({ type: 'string', required: false })
  @IsOptional()
  stage: string;

  @ApiProperty({ type: 'boolean', required: false })
  @IsOptional()
  @IsBoolean()
  isUpdatedFinding: boolean;
}

/**
 === List NC of previous audit
 [
  {
    id: string;
    reportFindingForm: {
      id: 'string',
      planningRequest: {
        refId: string;
        auditNo: string;
      }
    },
    auditChecklist: {
      code: string;
      name: string;
    },
    findingComment: string;
    findingRemark: string;
    isVerify: boolean;
    isOpen: boolean;
  }
 ]
 */
