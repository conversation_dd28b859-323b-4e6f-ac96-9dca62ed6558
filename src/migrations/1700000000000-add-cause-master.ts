import { MigrationInterface, QueryRunner } from 'typeorm';

export class addCauseMaster1700000000000 implements MigrationInterface {
  name = 'addCauseMaster1700000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create cause type enum
    await queryRunner.query(
      `CREATE TYPE "cause_master_causetype_enum" AS ENUM('Basic', 'Immediate', 'Control Action Needs', 'Type of Loss')`,
    );

    // Create potential risk enum
    await queryRunner.query(
      `CREATE TYPE "cause_master_potentialrisk_enum" AS ENUM('High', 'Medium', 'Low', 'Negligible')`,
    );

    // Create status enum
    await queryRunner.query(`CREATE TYPE "cause_master_status_enum" AS ENUM('active', 'inactive')`);

    // Create cause_master table
    await queryRunner.query(`
      CREATE TABLE "cause_master" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "causeType" "cause_master_causetype_enum" NOT NULL,
        "mainCategory" character varying(100) NOT NULL,
        "referenceNo" integer,
        "potentialRisk" "cause_master_potentialrisk_enum" NOT NULL,
        "status" "cause_master_status_enum" NOT NULL DEFAULT 'active',
        "parentId" uuid,
        "level" smallint NOT NULL DEFAULT '1',
        "numChildren" smallint NOT NULL DEFAULT '0',
        "numDependents" smallint NOT NULL DEFAULT '0',
        "description" text,
        "companyId" uuid NOT NULL,
        "createdUserId" uuid,
        "updatedUserId" uuid,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "deleted" boolean NOT NULL DEFAULT false,
        CONSTRAINT "PK_cause_master_id" PRIMARY KEY ("id")
      )
    `);

    // Create unique indexes
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_causeMaster_causeType_mainCategory_companyId" ON "cause_master" ("causeType", "mainCategory", "companyId") WHERE deleted = false AND "parentId" IS NULL`,
    );

    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_causeMaster_parentId_referenceNo" ON "cause_master" ("parentId", "referenceNo") WHERE deleted = false AND "parentId" IS NOT NULL`,
    );

    // Create foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "cause_master" ADD CONSTRAINT "FK_cause_master_companyId" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );

    await queryRunner.query(
      `ALTER TABLE "cause_master" ADD CONSTRAINT "FK_cause_master_createdUserId" FOREIGN KEY ("createdUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    await queryRunner.query(
      `ALTER TABLE "cause_master" ADD CONSTRAINT "FK_cause_master_updatedUserId" FOREIGN KEY ("updatedUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    await queryRunner.query(
      `ALTER TABLE "cause_master" ADD CONSTRAINT "FK_cause_master_parentId" FOREIGN KEY ("parentId") REFERENCES "cause_master"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );

    // Create additional indexes for performance
    await queryRunner.query(
      `CREATE INDEX "IDX_cause_master_companyId" ON "cause_master" ("companyId")`,
    );

    await queryRunner.query(
      `CREATE INDEX "IDX_cause_master_causeType" ON "cause_master" ("causeType")`,
    );

    await queryRunner.query(`CREATE INDEX "IDX_cause_master_status" ON "cause_master" ("status")`);

    await queryRunner.query(
      `CREATE INDEX "IDX_cause_master_parentId" ON "cause_master" ("parentId")`,
    );

    await queryRunner.query(`CREATE INDEX "IDX_cause_master_level" ON "cause_master" ("level")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "cause_master" DROP CONSTRAINT "FK_cause_master_parentId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_master" DROP CONSTRAINT "FK_cause_master_updatedUserId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_master" DROP CONSTRAINT "FK_cause_master_createdUserId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_master" DROP CONSTRAINT "FK_cause_master_companyId"`,
    );

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_cause_master_level"`);
    await queryRunner.query(`DROP INDEX "IDX_cause_master_parentId"`);
    await queryRunner.query(`DROP INDEX "IDX_cause_master_status"`);
    await queryRunner.query(`DROP INDEX "IDX_cause_master_causeType"`);
    await queryRunner.query(`DROP INDEX "IDX_cause_master_companyId"`);
    await queryRunner.query(`DROP INDEX "idx_causeMaster_parentId_referenceNo"`);
    await queryRunner.query(`DROP INDEX "idx_causeMaster_causeType_mainCategory_companyId"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "cause_master"`);

    // Drop enums
    await queryRunner.query(`DROP TYPE "cause_master_status_enum"`);
    await queryRunner.query(`DROP TYPE "cause_master_potentialrisk_enum"`);
    await queryRunner.query(`DROP TYPE "cause_master_causetype_enum"`);
  }
}
