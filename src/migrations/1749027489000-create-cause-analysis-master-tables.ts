import { MigrationInterface, QueryRunner } from 'typeorm';

export class createCauseAnalysisMasterTables1749027489000 implements MigrationInterface {
  name = 'createCauseAnalysisMasterTables1749027489000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(
      `CREATE TYPE "cause_analysis_master_causetype_enum" AS ENUM('Basic', 'Immediate', 'Control Action Needs', 'Type of Loss')`,
    );
    await queryRunner.query(
      `CREATE TYPE "cause_analysis_master_status_enum" AS ENUM('active', 'inactive')`,
    );
    // Note: potentialRisk is now a foreign key to priority_master table, no enum needed
    await queryRunner.query(
      `CREATE TYPE "cause_main_category_status_enum" AS ENUM('published', 'draft')`,
    );
    await queryRunner.query(
      `CREATE TYPE "cause_sub_category_status_enum" AS ENUM('published', 'draft')`,
    );

    // Create cause_analysis_master table
    await queryRunner.query(
      `CREATE TABLE "cause_analysis_master" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "deleted" boolean NOT NULL DEFAULT false,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "causeType" "cause_analysis_master_causetype_enum" NOT NULL,
        "udfVersionNo" character varying NOT NULL,
        "refNo" character varying NOT NULL,
        "status" "cause_analysis_master_status_enum" NOT NULL,
        "timezone" character varying,
        "companyId" uuid NOT NULL,
        "createdUserId" uuid NOT NULL,
        "updatedUserId" uuid,
        CONSTRAINT "PK_cause_analysis_master" PRIMARY KEY ("id")
      )`,
    );

    // Create cause_main_category table
    await queryRunner.query(
      `CREATE TABLE "cause_main_category" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "deleted" boolean NOT NULL DEFAULT false,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "mainCategoryNo" character varying NOT NULL,
        "mainCategoryName" character varying NOT NULL,
        "potentialRiskId" uuid NOT NULL,
        "status" "cause_main_category_status_enum" NOT NULL DEFAULT 'published',
        "causeAnalysisMasterId" uuid NOT NULL,
        "companyId" uuid NOT NULL,
        CONSTRAINT "PK_cause_main_category" PRIMARY KEY ("id")
      )`,
    );

    // Create cause_sub_category table
    await queryRunner.query(
      `CREATE TABLE "cause_sub_category" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "deleted" boolean NOT NULL DEFAULT false,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "subCategoryName" character varying NOT NULL,
        "status" "cause_sub_category_status_enum" NOT NULL DEFAULT 'published',
        "parentId" uuid,
        "subRefNo" character varying,
        "causeMainCategoryId" uuid NOT NULL,
        "companyId" uuid NOT NULL,
        "level" integer NOT NULL DEFAULT 1,
        CONSTRAINT "PK_cause_sub_category" PRIMARY KEY ("id")
      )`,
    );

    // Create unique indexes
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_cause_analysis_master_udf_version_no_companyId" ON "cause_analysis_master" ("udfVersionNo", "companyId") WHERE deleted = false`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_main_cause_analysis_master_id_main_category_no" ON "cause_main_category" ("causeAnalysisMasterId", "mainCategoryNo") WHERE deleted = false`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_main_cause_analysis_master_id_main_category_name" ON "cause_main_category" ("causeAnalysisMasterId", "mainCategoryName") WHERE deleted = false`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_cause_sub_main_id_sub_no" ON "cause_sub_category" ("causeMainCategoryId", "subRefNo") WHERE ("parentId" IS NULL AND deleted = false)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_cause_sub_main_id_sub_no_parent" ON "cause_sub_category" ("causeMainCategoryId", "subRefNo", "parentId") WHERE ("parentId" IS NOT NULL AND deleted = false)`,
    );

    // Create regular indexes for performance
    await queryRunner.query(
      `CREATE INDEX "idx_cause_analysis_master_company_id" ON "cause_analysis_master" ("companyId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_analysis_master_cause_type" ON "cause_analysis_master" ("causeType")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_analysis_master_status" ON "cause_analysis_master" ("status")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_main_category_master_id" ON "cause_main_category" ("causeAnalysisMasterId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_main_category_company_id" ON "cause_main_category" ("companyId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_main_category_potential_risk_id" ON "cause_main_category" ("potentialRiskId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_sub_category_main_id" ON "cause_sub_category" ("causeMainCategoryId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_sub_category_parent_id" ON "cause_sub_category" ("parentId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_sub_category_company_id" ON "cause_sub_category" ("companyId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_cause_sub_category_level" ON "cause_sub_category" ("level")`,
    );

    // Add foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "cause_analysis_master" ADD CONSTRAINT "FK_cause_analysis_master_company" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_analysis_master" ADD CONSTRAINT "FK_cause_analysis_master_created_user" FOREIGN KEY ("createdUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_analysis_master" ADD CONSTRAINT "FK_cause_analysis_master_updated_user" FOREIGN KEY ("updatedUserId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_main_category" ADD CONSTRAINT "FK_cause_main_category_master" FOREIGN KEY ("causeAnalysisMasterId") REFERENCES "cause_analysis_master"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_main_category" ADD CONSTRAINT "FK_cause_main_category_company" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_main_category" ADD CONSTRAINT "FK_cause_main_category_potential_risk" FOREIGN KEY ("potentialRiskId") REFERENCES "priority_master"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_sub_category" ADD CONSTRAINT "FK_cause_sub_category_main" FOREIGN KEY ("causeMainCategoryId") REFERENCES "cause_main_category"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_sub_category" ADD CONSTRAINT "FK_cause_sub_category_parent" FOREIGN KEY ("parentId") REFERENCES "cause_sub_category"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "cause_sub_category" DROP CONSTRAINT "FK_cause_sub_category_parent"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_sub_category" DROP CONSTRAINT "FK_cause_sub_category_main"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_main_category" DROP CONSTRAINT "FK_cause_main_category_company"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_main_category" DROP CONSTRAINT "FK_cause_main_category_potential_risk"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_main_category" DROP CONSTRAINT "FK_cause_main_category_master"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_analysis_master" DROP CONSTRAINT "FK_cause_analysis_master_updated_user"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_analysis_master" DROP CONSTRAINT "FK_cause_analysis_master_created_user"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cause_analysis_master" DROP CONSTRAINT "FK_cause_analysis_master_company"`,
    );

    // Drop indexes
    await queryRunner.query(`DROP INDEX "idx_cause_sub_category_level"`);
    await queryRunner.query(`DROP INDEX "idx_cause_sub_category_company_id"`);
    await queryRunner.query(`DROP INDEX "idx_cause_sub_category_parent_id"`);
    await queryRunner.query(`DROP INDEX "idx_cause_sub_category_main_id"`);
    await queryRunner.query(`DROP INDEX "idx_cause_main_category_company_id"`);
    await queryRunner.query(`DROP INDEX "idx_cause_main_category_potential_risk_id"`);
    await queryRunner.query(`DROP INDEX "idx_cause_main_category_master_id"`);
    await queryRunner.query(`DROP INDEX "idx_cause_analysis_master_status"`);
    await queryRunner.query(`DROP INDEX "idx_cause_analysis_master_cause_type"`);
    await queryRunner.query(`DROP INDEX "idx_cause_analysis_master_company_id"`);
    await queryRunner.query(`DROP INDEX "idx_cause_sub_main_id_sub_no_parent"`);
    await queryRunner.query(`DROP INDEX "idx_cause_sub_main_id_sub_no"`);
    await queryRunner.query(`DROP INDEX "idx_main_cause_analysis_master_id_main_category_name"`);
    await queryRunner.query(`DROP INDEX "idx_main_cause_analysis_master_id_main_category_no"`);
    await queryRunner.query(`DROP INDEX "idx_cause_analysis_master_udf_version_no_companyId"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "cause_sub_category"`);
    await queryRunner.query(`DROP TABLE "cause_main_category"`);
    await queryRunner.query(`DROP TABLE "cause_analysis_master"`);

    // Drop enum types
    await queryRunner.query(`DROP TYPE "cause_sub_category_status_enum"`);
    await queryRunner.query(`DROP TYPE "cause_main_category_status_enum"`);
    await queryRunner.query(`DROP TYPE "cause_analysis_master_status_enum"`);
    await queryRunner.query(`DROP TYPE "cause_analysis_master_causetype_enum"`);
  }
}
