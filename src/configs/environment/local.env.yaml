SHARE:
  PUBLIC:
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.testing.i-nautix.com
    POLICY_INFO:
      TERMS_OF_SERVICE: https://www.solverminds.com/terms-of-use/
      CONTACT:
        NAME: 'Solverminds'
        URL: https://www.solverminds.sg/contact-us
        EMAIL: <EMAIL>
      LICENSE:
        NAME: 'LICENSE- SVM'
        URL: https://www.solverminds.sg/about-us
    RESOURCE:
      CDN_RESOURCE: 'https://svm-inautix-testing.s3.amazonaws.com'
      HOME_PAGE: 'https://testing.i-nautix.com'
      ADMIN_PAGE: 'https://admin.testing.i-nautix.com'
  SECURE:
    CORS:
      ORIGIN:
        - 'https://admin.testing.i-nautix.com'
        - 'https://api.testing.i-nautix.com'
        - 'https://testing.i-nautix.com'
      METHODS:
        - 'GET'
        - 'PUT'
        - 'POST'
        - 'DELETE'
        - 'PATCH'
        - 'HEAD'
        - 'OPTIONS'
      ALLOWED_HEADERS:
        - '*'
      EXPOSED_HEADERS:
        - '*'
      CREDENTIALS: true
      PREFLIGHT_CONTINUE: false
    JWT:
      JWT_SECRET: 'inautix-uat-ship2938232-c2##2021'
      TOKEN_EXPIRE: 604800
    API_RESTRICT:
      CLIENT_SECRET: 't8$Udfs9E2s-f34vkl2$91232MLY20212022410322'
    KEYS:
      ENCRYPT_KEY: '97bbd1130080079f4a6f1745b591b303'
      ENCRYPT_KEY_MOBILE: '#d@g$H907845%^U@'
      ENCRYPT_KEY_ATTACH: 'R%C#9qX^gJrgYQ4s'
    GATEWAY_CONFIG:
      METHOD: https
      HOST: api.testing.i-nautix.com
    SAML_CONFIG:
      ENTRY_POINT: 'https://login.microsoftonline.com/9899607f-26ff-4672-9990-24147884590b/saml2'
      ISSUER: 'https://api.testing.i-nautix.com/assets/api/v1/'
      CERT: 'MIIC8DCCAdigAwIBAgIQSvEapgPTqY9J4miqgLWCJDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMjExMTcxMDQyMTdaFw0yNTExMTcxMDQzMzlaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs3g7b3/xyQpgXynrySGz0LWc8skBFumrFRguuVYEfzqFwRalmUtLAOie4KE7XfFKHmU0kYcO0KQUY6SbNSEcCCjbIBNxvkV9C6IJ0irHWvc0ESlPuDrM7tKCYgaGg9e8GiU/2X6lygQlgjMgbS0bGqnh5l89/gGr6dkYjO5Ba6UU+Pq9EXldSR/6XdG5iAR3YEZQMcQMlRX8VW0ag++xJVM6NVwdOyDt4SAZWoPBVlNpt1knxAFYhSc+PgYG9z8T/E1iQ40NMW/O0X1d2rSbCaRPWZvbkAd5QfBgV6uhKUlEBPe4B8s1yjmep1elibHVKKq7FiA6vu+vx+Cdn/CQFQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAly7RyJsq4GY05CfBoTG984buQdveQcwg+dIbjcemX5b4E00Ci0feKvUlA/a0ARqQhRBj0OJjIFQoiBch/NJ3cs6DXqSUfPkcDPoABKphsoFtB/z4h94JBWzgLwfM/fMVvvx6QEFcclwqUoec159Fy616rd/SQsLiBbbt8JPnMdJtzBYuHQN5akPkq6S97HyAf+09+O0r04NK2YbtXX0JXiyEzUgVCjPZD8eyQIcbG7+C6FRodk+5h/s6cEOMuWRoOKPjTXoYZMV+fFQJ+ZEpChGEueqY3ld4vDHL+9p53PCgBCxwa/B+pJ+aX4CmR7zpXLXpQEB3X4NFZWigWtfa1'    
NAME: local
APP:
  NAME: assets
  PORT: 8787
  IP: 0.0.0.0
  ENV: testing  
AWS:
  CONFIG:
    ACCESS_KEY_ID: xxx
    SECRET_ACCESS_KEY: xxx
  SES:
    ACCESS_KEY_ID: ********************
    SECRET_ACCESS_KEY: weYguujZLKjdgeLyRkH4tQieFGTZNdm3XLEqCHzY
    REGION: us-east-1
DATABASE:
  POSTGRES:
    USERNAME: 'postgres'
    PASSWORD: 'P@ssw0rd'
    HOST: 'localhost'
    PORT: 5432
    NAME: 'testing_dump'

    # USERNAME: svmassetstesting
    # PASSWORD: 'VK[%YfZ5{::7WNv'
    # HOST: db210826x-testingelopment.i-nautix.com
    # PORT: 5432
    # NAME: svm-assets-testing
  REDIS:
    #HOST: 'svm-redis-development.egv0le.ng.0001.apse1.cache.amazonaws.com'
    HOST: 'localhost'
    PORT: '6379'
    PASSWORD: ''
    DB: 0
    KEY_PREFIX: ''
CONFIG:
  ADMIN_EMAIL:
    SALES:
      - <EMAIL>
      - <EMAIL>
    SUPER_ADMIN: <EMAIL>
  SENDER_MAIL:
    FROM: 'i-Nautix <<EMAIL>>'
NETWORK:
  XXX_API_KEYS:
    - xxx
NOTIFICATION:
  MAIL:
    FROM: '"Solverminds" <<EMAIL>>'
CONFIG_SYNC_DATA_VESSEL:
  USERNAME: 'vmouser'
  PASSWORD: 'l394M3S!nxMpoFg#Z606'
  URL: 'https://api.testing.i-nautix.com'
  COMPANY_ID: '3c9d41db-9c0e-4c5b-8636-d2ebd930f967'
  HOUR: 8
CONFIG_SYNC_NYK_DATA_VESSEL:
  URL: 'https://api.testing.i-nautix.com'
  COMPANY_ID: '8ba3c349-dc56-4911-a5ab-e37ebef5252c'
CONFIG_ZENITH_POTENTIAL_RISK_DATA:
  USERNAME: 'RioTinto@prod'
  PASSWORD: 'rioTinto@PROD'
  URL: 'https://riotinto-zenith.solverminds.net/zenith_backend'
  COMPANY_ID: '3c9d41db-9c0e-4c5b-8636-d2ebd930f967' 
