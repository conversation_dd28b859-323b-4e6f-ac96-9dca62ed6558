export enum FeatureEnum {
  DASHBOARD = 'Dashboard',

  AUDIT_INSPECTION = 'Audit & Inspection',
  GROUP_COMPANY = 'Group & Company',
  USER_ROLE = 'User & Roles',
  // QUALITY_ASSURANCE
  QUALITY_ASSURANCE = 'Quality Assurance',
  QUALITY_ASSURANCE_SELF_ASSESSMENT = 'Quality Assurance::Self-Assessment',
  QUALITY_ASSURANCE_SAILING_REPORT = 'Quality Assurance::Sailing Report',
  QUALITY_ASSURANCE_INCIDENTS = 'Quality Assurance::Incidents',
  QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK = 'Quality Assurance::Pilot/Terminal Feedback',
  QUALITY_ASSURANCE_VESSEL_SCREENING = 'Quality Assurance::Vessel Screening',
  VESSEL_SCHEDULE = 'Vessel Schedule',
  // End QUALITY_ASSURANCE
  CONFIGURATION = 'Configuration',
  CONFIGURATION_COMMON = 'Configuration::Common',
  CONFIGURATION_QA = 'Configuration::QA',
  CONFIGURATION_INSPECTION = 'Configuration::Inspection',
}

export enum SubFeatureEnum {
  DASHBOARD = 'Dashboard',
  AUDIT_INSPECTION_WORKSPACE = 'Audit Inspection Workspace',
  PLANNING_AND_REQUEST = 'Planning & request',
  INTERNAL_AUDIT_REPORT = 'Internal Audit Report',
  REPORT_OF_FINDING = 'Report of Findings',
  INSPECTION_FOLLOW_UP = 'Inspection Follow Up',
  // Group company
  GROUP_MASTER = 'Group master',
  COMPANY = 'Company',
  // User & role permission
  ROLE_AND_PERMISSION = 'Role and permission',
  USER = 'User',
  // Quality Assurance
  STANDARD_MASTER = 'Standard Master',
  ELEMENT_MASTER = 'Element Master',
  SELF_ASSESSMENT = 'Self-Assessment',
  SAILING_GENERAL_REPORT = 'Sailing General Report',
  VESSEL_SCREENING = 'Vessel Screening',
  VESSEL_OWNER_BUSINESS = 'Vessel Owner Business',
  //Vessel Schedule
  VESSEL_SCHEDULE = 'Vessel Schedule',
  // Configuration
  AUDIT_CHECKLIST = 'Audit Checklist',
  INSPECTION_MAPPING = 'Inspection Mapping',
  REPORT_TEMPLATE = 'Report template master',
  WORKFLOW_CONFIGURATION = 'Workflow configuration',
  FLEET = 'Fleet',
  VESSEL_TYPE = 'Vessel type',
  AUTHORITY_MASTER = 'Authority master',
  VESSEL = 'Vessel',
  AUDIT_TYPE = 'Audit type',
  INSPECTOR_TIME_OFF = 'Inspector time off',
  LOCATION_MASTER = 'Location master',
  PORT_MASTER = 'Port master',
  MAIL_TEMPLATE = 'Mail Template',
  MAIN_CATEGORY = 'Main category',
  SECOND_CATEGORY = 'Second category',
  THIRD_CATEGORY = 'Third category',
  CATEGORY_MAPPING = 'Category mapping',
  TOPIC = 'Topic',
  SHIP_DEPARTMENT = 'Ship department',
  SHIP_RANK = 'Ship rank',
  SHIP_DIRECT_RESPONSIBLE = 'Ship direct responsible',
  CHARTER_OWNER = 'Charter/Owner',
  AUDIT_TIME_TABLE = 'Audit Time Table',
  PSC_ACTION = 'PSC Action',
  SHORE_RANK = 'Shore rank',
  SHORE_DEPARTMENT = 'Shore department',
  DMS = 'DMS',
  CDI = 'Chemical Distribution Institute',
  VIQ = 'Vessel inspection questionnaire',
  PSC_DEFICIENCY = 'PSC Deficiency',
  RANK_MASTER = 'Rank',
  DEPARTMENT_MASTER = 'Department',
  NATURE_OF_FINDINGS_MASTER = 'Nature of Findings',
  MOBILE_CONFIG = 'Mobile config',
  DEVICE_CONTROL = 'Device Control',
  APP_TYPE_PROPERTY = 'App Type Property',
  FOCUS_REQUEST = 'Focus request',
  EVENT_TYPE = 'Event Type',
  INCIDENT_MASTER = 'Incident Master',
  TECH_ISSUE_NOTE = 'Technical Issue Note',
  INJURY_MASTER = 'Injury Master',
  INJURY_BODY = 'Injury Body',
  ATTACHMENT_KIT = 'Attachment Kit',
  TERMINAL = 'Terminal',
  RISK_FACTOR = 'Risk factor',
  TRANSFER_TYPE = 'Transfer Type',
  CARGO_TYPE = 'Cargo Type',
  CARGO = 'Cargo',
  ANSWER_VALUE = 'Answer Value',
  MAP_VIEW = 'Map view',
  PLANS_DRAWINGS_MASTER = 'Plans Drawings Master',
  CREW_GROUPING = 'Crew Grouping',
  DIVISION = 'Division',
  SUMMARY = 'Summary',
  INCIDENTS = 'Incidents',
  PILOT_TERMINAL_FEEDBACK = 'Pilot/Terminal Feedback',
  VESSEL_COMPANY_FEEDBACK = 'Vessel/Company Feedback',
  DIVISION_MAPPING = 'Division Mapping',
  COMPANY_TYPE = 'Company Type',
  REPEATED_FINDING = 'Repeated Finding',
  VOYAGE_STATUS = 'Voyage Status',
  CVIQ_VERSION = 'CVIQ Version',
  CVIQ_CHAPTER = 'CVIQ Chapter',
  CVIQ_CONDITIONALITY = 'CVIQ Conditionality',
  CVIQ_DETAILS_MAPPING = 'CVIQ Details Mapping',
  CATEGORIZATION_MASTER = 'Categorization Master',
  VOYAGE_TYPE = 'Voyage Type',
  CAUSE_MASTER = 'Cause Master',
}

export enum ActionEnum {
  CREATE = 'Create',
  DELETE = 'Delete',
  EXPORT = 'Export',
  UPDATE = 'Update',
  VIEW = 'View',
  EXECUTE = 'Execute',
  EMAIL = 'Email',
  RESTRICTED = 'Restricted',
  REVIEW = 'Review',
}

export const AUDIT_INSPECTION_VIEW_PERMISSIONS = [
  {
    feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.REPORT_TEMPLATE,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.AUDIT_CHECKLIST,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.CONFIGURATION_INSPECTION + '::' + SubFeatureEnum.INSPECTION_MAPPING,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.PLANNING_AND_REQUEST,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_TIME_TABLE,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.AUDIT_INSPECTION_WORKSPACE,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.REPORT_OF_FINDING,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INTERNAL_AUDIT_REPORT,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.AUDIT_INSPECTION + '::' + SubFeatureEnum.INSPECTION_FOLLOW_UP,
    action: ActionEnum.VIEW,
  },
];

export const QUALITY_ASSURANCE_VIEW_PERMISSIONS = [
  {
    feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.INCIDENTS,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.QUALITY_ASSURANCE_INCIDENTS + '::' + SubFeatureEnum.SUMMARY,
    action: ActionEnum.VIEW,
  },
  {
    feature:
      FeatureEnum.QUALITY_ASSURANCE_SAILING_REPORT + '::' + SubFeatureEnum.SAILING_GENERAL_REPORT,
    action: ActionEnum.VIEW,
  },
  {
    feature:
      FeatureEnum.QUALITY_ASSURANCE_PILOT_TERMINAL_FEEDBACK +
      '::' +
      SubFeatureEnum.PILOT_TERMINAL_FEEDBACK,
    action: ActionEnum.VIEW,
  },
  {
    feature:
      FeatureEnum.QUALITY_ASSURANCE_VESSEL_SCREENING + '::' + SubFeatureEnum.VESSEL_SCREENING,
    action: ActionEnum.VIEW,
  },
  {
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  },
];

export enum AllowedSubscriptionsEnum {
  INSPECTION = 'Inspections/Services',
  QUALITY_ASSURANCE = 'Quality assurance',
}
