import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Put,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';
import { Response } from 'express';
import {
  AuthGuard,
  RequiredPermissions,
  RoleScope,
  Roles,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
} from 'svm-nest-lib-v3';

import { FileInterceptor } from '@nestjs/platform-express';
import { I18n, I18nContext, I18nLang } from 'nestjs-i18n';
import { ActionEnum, FeatureEnum, SubFeatureEnum } from '../../commons/enums';
import { storageSelfAssessment } from '../../modules/transfer-data/storage.config';
import {
  CreateSelfAssessmentDto,
  ListSelfAssessmentDto,
  ListSelfAssessmentHistoryDto,
  ListSelfDeclarationDto,
  PublishSelfAssessmentDto,
  UpdateSelfAssessmentDto,
} from './dto';
import { SelfAssessmentService } from './self-assessment.service';
import { ListLabelConfigCompanyDto } from '../catalog/dto/list-label-config-company.dto';
import { UpdateSelfAssessmentCustomizationDto } from './dto/update-self-assessment-customization.dto';
import { SelfDeclarationService } from './self-declaration.service';
import { changePathFileName } from '../../commons/functions';
import { PayloadAGGridDto } from '../../utils';
import { SAFindingItem } from '../../modules/audit-workspace/entities/sa-finding-items.entity';

@ApiTags('Self Assessment')
@Controller('/self-assessment')
@ApiBearerAuth()
// @UseGuards(AuthGuard, RolesGuard)
export class SelfAssessmentController {
  constructor(
    private readonly selfAssessmentService: SelfAssessmentService,
    private readonly selfDeclarationService: SelfDeclarationService,
  ) {}

  @ApiResponse({ description: 'List self assessment success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List self assessment error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List self assessment', operationId: 'listSelfAssessment' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('')
  async listSelfAssessment(
    @Query() query: ListSelfAssessmentDto,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.selfAssessmentService.listSelfAssessment(token, query);
  }

  @ApiResponse({ description: 'List self assessment success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List self assessment error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'List self assessment', operationId: 'listSelfAssessment' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Post('/listSelfAssessment')
  async optimizedListSelfAssessment(
    @Query() query: ListSelfAssessmentDto,
    @TokenDecorator() token: TokenPayloadModel,
    @Body() body: PayloadAGGridDto,
  ) {
    return this.selfAssessmentService.listSelfAssessment(token, query, body);
  }

  @ApiResponse({ description: 'Export self assessment success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Export self assessment error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Export self assessment', operationId: 'exportSelfAssessment' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Post('/exportSelfAssessment')
  async exportSelfAssessment(
    @Query() query: ListSelfAssessmentDto,
    @TokenDecorator() token: TokenPayloadModel,
    @Body() body: PayloadAGGridDto,
    @Res() res: Response,
  ) {
    return this.selfAssessmentService.exportSelfAssessment(token, query, body, res);
  }

  @ApiResponse({ description: 'List self assessment history success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'List self assessment history error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({ summary: 'List self assessment', operationId: 'listSelfAssessmentHistoryTable' })
  @ApiQuery({
    description: 'Paginate params',
    type: ListSelfAssessmentHistoryDto,
    required: false,
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/history')
  async listSelfAssessmentHistory(@Query() query, @TokenDecorator() token: TokenPayloadModel) {
    return this.selfAssessmentService.listSelfAssessmentHistory(token, query);
  }

  @ApiResponse({ description: 'Create self assessment success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Create self assessment error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'Create self assessment', operationId: 'createSelfAssessment' })
  @ApiBody({ type: CreateSelfAssessmentDto })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.CREATE,
  })
  // @RequiredWorkflowPermissions({
  //   workflowType: WorkflowType.SELF_ASSESSMENT,
  //   permission: WorkflowPermission.CREATOR,
  // })
  @Post('')
  async createSelfAssessment(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: CreateSelfAssessmentDto,
    @I18n() i18n: I18nContext,
  ) {
    return this.selfAssessmentService.createSelfAssessment(user, body);
  }

  @ApiResponse({ description: 'Update self assessment success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'update self assessment error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'update self assessment',
    operationId: 'updateSelfAssessment',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'self assessment id',
  })
  @ApiBody({ type: UpdateSelfAssessmentDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.UPDATE,
  })
  // @RequiredWorkflowPermissions({
  //   workflowType: WorkflowType.SELF_ASSESSMENT,
  //   permission: WorkflowPermission.CREATOR,
  // })
  @Put('/:id')
  async updateSelfAssessment(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) id: string,
    @I18nLang() lang: string,
    @Body() body: UpdateSelfAssessmentDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfAssessmentService.updateSelfAssessment(user, id, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Get detail self assessment success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get detail self assessment' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/:id')
  async getDetailSelfAssessment(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) selfAssessmentId: string,
    @Query() query?: ListSelfDeclarationDto,
  ) {
    return this.selfAssessmentService.getDetailSelfAssessment(user, selfAssessmentId, query);
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Delete self assessment success',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Delete self assessment' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.DELETE,
  })
  // @RequiredWorkflowPermissions({
  //   workflowType: WorkflowType.SELF_ASSESSMENT,
  //   permission: WorkflowPermission.CREATOR,
  // })
  @Delete('/:id')
  async deleteSelfAssessment(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) selfAssessmentId: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfAssessmentService.deleteSelfAssessment(user, selfAssessmentId);
    return {
      message: await i18n.t('common.DELETE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Publish official self assessment success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'publish official self assessment error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'publish official self assessment',
    operationId: 'publishOfficialSelfAssessment',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'self assessment id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
  //   action: ActionEnum.EXECUTE,
  // })
  @ApiBody({ type: PublishSelfAssessmentDto, required: false })
  // @RequiredWorkflowPermissions({
  //   workflowType: WorkflowType.SELF_ASSESSMENT,
  //   permission: WorkflowPermission.PUBLISHER,
  // })
  @Patch('/:id/publish-official')
  async publishOfficialSelfAssessment(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: PublishSelfAssessmentDto,
    @I18nLang() lang: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfAssessmentService.publishOfficialSelfAssessment(user, id, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'Un-publish official self assessment success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Un-publish official self assessment error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Un-publish official self assessment',
    operationId: 'UnPublishOfficialSelfAssessment',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'self assessment id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
  //   action: ActionEnum.EXECUTE,
  // })
  // @RequiredWorkflowPermissions({
  //   workflowType: WorkflowType.SELF_ASSESSMENT,
  //   permission: WorkflowPermission.PUBLISHER,
  // })
  @Patch('/:id/un-publish-official')
  async UnPublishOfficialSelfAssessment(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) id: string,
    @I18nLang() lang: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfAssessmentService.unPublishOfficialSelfAssessment(user, id);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiParam({ name: 'id', type: 'string', required: true })
  @ApiResponse({
    description: 'Check exist publish self assessment',
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Check exist publish self assessment' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/:id/check-exist-official')
  async checkExistPublishOfficial(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) selfAssessmentId: string,
  ) {
    return this.selfAssessmentService.checkExistPublishOfficial(user, selfAssessmentId);
  }

  @ApiResponse({ description: 'Upload file excel success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'Save and get transfer data error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'self assessment id',
  })
  @ApiOperation({ summary: 'Import compliance', operationId: 'importCompliance' })
  @HttpCode(HttpStatus.OK)
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN, RoleScope.USER)
  @Post('/:id/import-compliance')
  @UseInterceptors(FileInterceptor('file', { ...storageSelfAssessment }))
  async importCompliance(
    @UploadedFile() file: Express.Multer.File,
    @Param('id', ParseUUIDPipe) selfAssessmentId: string,
    @I18n() i18n: I18nContext,
    @Res() res: Response,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    try {
      await changePathFileName(file);
      const rs = await this.selfAssessmentService.importCompliance(file, user, selfAssessmentId);
      res.status(200).send(rs);
    } catch (error) {
      throw error;
    }
  }

  @ApiOperation({
    summary: 'Get SA finding items for self assessment',
    operationId: 'getSAFindingItems',
  })
  @ApiParam({
    name: 'id',
    description: 'Self Assessment ID',
    type: 'string',
  })
  @ApiOkResponse({
    description: 'SA finding items retrieved successfully',
    type: [SAFindingItem],
  })
  @ApiBadRequestResponse({
    description: 'Invalid request parameters',
  })
  @ApiNotFoundResponse({
    description: 'Self assessment not found',
  })
  @HttpCode(HttpStatus.OK)
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/:id/sa-finding-items')
  async getSAFindingItems(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('id', ParseUUIDPipe) id: string,
  ) {
    return this.selfAssessmentService.getSAFindingItems(user, id);
  }

  @ApiResponse({ description: 'Download file excel success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'Get transfer data error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'self assessment id',
  })
  @ApiOperation({ summary: 'Download template compliance', operationId: 'DownloadTemplate' })
  @HttpCode(HttpStatus.OK)
  @Roles(RoleScope.SUPER_ADMIN, RoleScope.ADMIN, RoleScope.USER)
  @Get('/:id/download-compliance')
  async downloadTemplate(
    @Param('id', ParseUUIDPipe) selfAssessmentId: string,
    @Res() res: Response,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    try {
      const rs = await this.selfAssessmentService.downloadTemplateSelfAsscessment(
        res,
        user,
        selfAssessmentId,
      );
      return rs.xlsx.write(res).then(function () {
        res.status(200).end();
      });
    } catch (error) {
      throw error;
    }
  }

  @ApiResponse({ description: 'List customization export success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List customization export error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({ summary: 'list customization export', operationId: 'listCustomizationExport' })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/config/customization-export')
  async listCustomizationExport(
    @Query() query: ListLabelConfigCompanyDto,
    @TokenDecorator() user: TokenPayloadModel,
  ) {
    return await this.selfDeclarationService.listCustomizationByStandard(query, user);
  }

  @ApiResponse({ description: 'Update customization export success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'update customization export error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'update customization export',
    operationId: 'updateCustomizationExport',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Field id',
  })
  @ApiBody({ type: UpdateSelfAssessmentCustomizationDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Put('/config/customization-export/:standardMasterId')
  async updateCustomizationExport(
    @TokenDecorator() user: TokenPayloadModel,
    @Param('standardMasterId', ParseUUIDPipe) standardMasterId: string,
    @Body() body: UpdateSelfAssessmentCustomizationDto,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfDeclarationService.updateCustomizationExport(user, standardMasterId, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Reset customization export success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'reset customization export error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'Reset customization export',
    operationId: 'resetCustomizationExport',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Post('/config/customization-export/:standardMasterId/reset')
  async resetCustomizationExport(
    @Param('standardMasterId', ParseUUIDPipe) standardMasterId: string,
    @TokenDecorator() user: TokenPayloadModel,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfDeclarationService.resetCustomizationExport(standardMasterId, user);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }
}
