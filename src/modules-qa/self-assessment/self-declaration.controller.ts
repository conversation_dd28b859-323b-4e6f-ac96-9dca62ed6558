import {
  Get,
  Controller,
  Query,
  HttpStatus,
  UseGuards,
  Body,
  Param,
  ParseUUI<PERSON>ipe,
  Put,
  Patch,
  Res,
  Post,
} from '@nestjs/common';
import {
  Roles,
  RoleScope,
  AuthGuard,
  RolesGuard,
  TokenDecorator,
  TokenPayloadModel,
  RequiredPermissions,
  RequiredWorkflowPermissions,
  WorkflowPermissionsDecorator,
} from 'svm-nest-lib-v3';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';

import { I18n, I18nContext, I18nLang } from 'nestjs-i18n';
import { SelfDeclarationService } from './self-declaration.service';
import { Response } from 'express';
import {
  ReOpenSelfDeclarationDto,
  ListSelfDeclarationDto,
  UpdateSelfDeclarationDto,
  ListLoopBackCommentDto,
  ReAssignSelfDeclarationDto,
  UpdateSelfDeclarationForListingDto,
} from './dto';
import {
  ActionEnum,
  FeatureEnum,
  SubFeatureEnum,
  WorkflowPermission,
  WorkflowType,
} from '../../commons/enums';
@ApiTags('Self Declaration')
@Controller('/self-assessment/:selfAssessmentId/self-declaration')
@ApiBearerAuth()
@UseGuards(AuthGuard, RolesGuard)
export class SelfDeclarationController {
  constructor(private readonly selfDeclarationService: SelfDeclarationService) {}

  @ApiResponse({ description: 'Update self declaration success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'Update self declaration error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiBody({ type: UpdateSelfDeclarationDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.UPDATE,
  })
  // @RequiredWorkflowPermissions(
  //   {
  //     workflowType: WorkflowType.SELF_ASSESSMENT,
  //     permission: WorkflowPermission.CREATOR,
  //   },
  //   {
  //     workflowType: WorkflowType.SELF_ASSESSMENT,
  //     permission: WorkflowPermission.REVIEWER,
  //   },
  // )
  @Put('/:id')
  async updateSelfDeclaration(
    @Body() body: UpdateSelfDeclarationDto,
    @Param('selfAssessmentId', ParseUUIDPipe) selfAssessmentId: string,
    @Param('id', ParseUUIDPipe) id: string,
    // @WorkflowPermissionsDecorator() workflowPermissions: string[],
    @TokenDecorator() token: TokenPayloadModel,
    @I18nLang() lang: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfDeclarationService.updateSelfDeclaration(
      id,
      selfAssessmentId,
      body,
      token,
      // workflowPermissions,
    );
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'Re-open self declaration success', status: HttpStatus.OK })
  @ApiResponse({
    description: 're-open self declaration error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 're-open self declaration',
    operationId: 'reOpenSelfDeclaration',
  })
  @ApiBody({ type: ReOpenSelfDeclarationDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
  //   action: ActionEnum.EXECUTE,
  // })
  @Patch('/re-open-self-declaration')
  async reOpenSelfDeclaration(
    @TokenDecorator() user: TokenPayloadModel,
    @Body() body: ReOpenSelfDeclarationDto,
    @Param('selfAssessmentId', ParseUUIDPipe) selfAssessmentId: string,
    @I18nLang() lang: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfDeclarationService.reOpenSelfDeclaration(user, body, selfAssessmentId);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({ description: 'List self declaration success', status: HttpStatus.OK })
  @ApiResponse({ description: 'List self declaration error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({
    summary: 'List self declaration',
    operationId: 'listSelfDeclarationBySelfAssessmentId',
  })
  @ApiQuery({
    description: 'Paginate params',
    type: ListSelfDeclarationDto,
    required: false,
  })
  @ApiParam({
    name: 'selfAssessmentId',
    type: 'string',
    required: true,
    description: 'self assessment id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('')
  async listSelfDeclarationBySelfAssessmentId(
    @Query() query: ListSelfDeclarationDto,
    @Param('selfAssessmentId', ParseUUIDPipe) selfAssessmentId: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.selfDeclarationService.getListSelfDeclarationBySelfAssessmentId(
      selfAssessmentId,
      query,
    );
  }

  @ApiResponse({ description: 'Export self declaration success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Export self declaration error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({
    summary: 'Export self declaration',
    operationId: 'exportSelfDeclarationBySelfAssessmentId',
  })
  @ApiParam({
    name: 'selfAssessmentId',
    type: 'string',
    required: true,
    description: 'self assessment id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Post('/export')
  async exportSelfDeclarationBySelfAssessmentId(
    @Param('selfAssessmentId', ParseUUIDPipe) selfAssessmentId: string,
    @TokenDecorator() token: TokenPayloadModel,
    @Res() res: Response,
  ) {
    return this.selfDeclarationService.exportSelfDeclarationBySelfAssessmentId(
      selfAssessmentId,
      token,
      res,
    );
  }

  @ApiResponse({ description: 'count self declaration success', status: HttpStatus.OK })
  @ApiResponse({ description: 'count self declaration error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({
    summary: 'count self declaration',
    operationId: 'countSelfDeclarationBySelfAssessmentId',
  })
  @ApiParam({
    name: 'selfAssessmentId',
    type: 'string',
    required: true,
    description: 'self assessment id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/count')
  async countSelfDeclarationBySelfAssessmentId(
    @Param('selfAssessmentId', ParseUUIDPipe) selfAssessmentId: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.selfDeclarationService.getCountSelfDeclarationBySelfAssessmentId(selfAssessmentId);
  }

  //matrix
  @ApiResponse({ description: 'List self declaration for matrix success', status: HttpStatus.OK })
  @ApiResponse({
    description: 'List self declaration for matrix error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'List self declaration from matrix',
    operationId: 'listSelfDeclarationForMatrixBySelfAssessmentId',
  })
  @ApiQuery({
    description: 'Paginate params',
    type: ListSelfDeclarationDto,
    required: false,
  })
  @ApiParam({
    name: 'selfAssessmentId',
    type: 'string',
    required: true,
    description: 'Self assessment id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/matrix')
  async listSelfDeclarationForMatrix(
    @Query() query: ListSelfDeclarationDto,
    @Param('selfAssessmentId', ParseUUIDPipe) selfAssessmentId: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.selfDeclarationService.listSelfDeclarationForMatrix(selfAssessmentId, query);
  }

  @ApiResponse({ description: 'Detail self declaration success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Detail self declaration error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({
    summary: 'Detail self declaration',
    operationId: 'getDetailSelfDeclarationId',
  })
  @ApiParam({
    name: 'selfDeclarationId',
    type: 'string',
    required: true,
    description: 'self declaration id',
  })
  @ApiQuery({
    name: 'type',
    type: 'string',
    required: false,
    description:
      'Type of data to return - "audit" for fill audit checklist data, "self-assessment" or omit for self declaration data',
    enum: ['audit', 'self-assessment'],
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/:selfDeclarationId')
  async getDetailSelfDeclarationId(
    @Param('selfDeclarationId', ParseUUIDPipe) selfDeclarationId: string,
    @TokenDecorator() token: TokenPayloadModel,
    @Query('type') type?: string,
  ) {
    return this.selfDeclarationService.getDetailSelfDeclarationById(selfDeclarationId, type);
  }

  @ApiResponse({ description: 'Re-assign self declaration success', status: HttpStatus.OK })
  @ApiResponse({ description: 'Re-assign self declaration error', status: HttpStatus.BAD_REQUEST })
  @ApiOperation({
    summary: 'Re-assign self declaration',
    operationId: 'reAssignSelfDeclaration',
  })
  @ApiBody({ type: ReAssignSelfDeclarationDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
  //   action: ActionEnum.EXECUTE,
  // })
  // @RequiredWorkflowPermissions({
  //   workflowType: WorkflowType.SELF_ASSESSMENT,
  //   permission: WorkflowPermission.REVIEWER,
  // })
  @Patch('/re-assign')
  async reAssignSelfDeclaration(
    @Body() body: ReAssignSelfDeclarationDto,
    @TokenDecorator() token: TokenPayloadModel,
    @I18nLang() lang: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfDeclarationService.reAssignSelfDeclaration(token, body);
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }

  @ApiResponse({
    description: 'List loop back comment by self declaration id success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'List loop back comment by self declaration id error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiOperation({
    summary: 'List loop back comment by self declaration id',
    operationId: 'listLoopBackCommentBySelfDeclarationId',
  })
  @ApiQuery({
    description: 'Paginate params',
    type: ListLoopBackCommentDto,
    required: false,
  })
  @ApiParam({
    name: 'selfDeclarationId',
    type: 'string',
    required: true,
    description: 'self declaration id',
  })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  @RequiredPermissions({
    feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
    action: ActionEnum.VIEW,
  })
  @Get('/:selfDeclarationId/loop-back-comment')
  async listLoopBackCommentBySelfDeclarationId(
    @Query() query: ListLoopBackCommentDto,
    @Param('selfDeclarationId', ParseUUIDPipe) selfDeclarationId: string,
    @TokenDecorator() token: TokenPayloadModel,
  ) {
    return this.selfDeclarationService.getListLoopBackCommentBySelfDeclarationId(
      selfDeclarationId,
      query,
    );
  }
  @ApiResponse({
    description: 'Update self declaration for listing success',
    status: HttpStatus.OK,
  })
  @ApiResponse({
    description: 'Update self declaration for listing error',
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiBody({ type: UpdateSelfDeclarationForListingDto, required: true })
  @Roles(RoleScope.ADMIN, RoleScope.USER)
  // @RequiredPermissions({
  //   feature: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
  //   action: ActionEnum.EXECUTE,
  // })
  @Patch('/:id')
  async updateSelfDeclarationListing(
    @Body() body: UpdateSelfDeclarationForListingDto,
    @Param('selfAssessmentId', ParseUUIDPipe) selfAssessmentId: string,
    @Param('id', ParseUUIDPipe) id: string,
    @TokenDecorator() token: TokenPayloadModel,
    @I18nLang() lang: string,
    @I18n() i18n: I18nContext,
  ) {
    await this.selfDeclarationService.updateSelfDeclarationForListing(
      id,
      selfAssessmentId,
      body,
      token,
    );
    return {
      message: await i18n.t('common.UPDATE_SUCCESS'),
    };
  }
}
