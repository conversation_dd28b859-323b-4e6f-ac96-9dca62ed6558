import moment from 'moment';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  RoleScope<PERSON>heck,
  TokenPayloadModel,
  TypeORMRepository,
  Utils,
} from 'svm-nest-lib-v3';
import { Connection, EntityRepository, getConnection, In } from 'typeorm';
import {
  ActionEnum,
  CompanyLevelEnum,
  EmailTypeEnum,
  FIXED_PACKAGE_ROLE_NAME,
  FeatureEnum,
  MailTemplate,
  ModulePathEnum,
  PushTypeEnum,
  ROLE_NAME_FIXED,
  SelfAssessmentMonthEnum,
  SelfAssessmentStatusEnum,
  SelfAssessmentTabEnum,
  SelfAssessmentTypeEnum,
  SelfDeclarationStatusEnum,
  SubFeatureEnum,
} from '../../../commons/enums';
import { decryptAttachmentValues } from '../../../commons/functions';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  <PERSON>U<PERSON>,
} from '../../../micro-services/async/notification.producer';
import { CompanyFeatureVersionRepository } from '../../../modules/commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../../../modules/commons/company-feature-version/feature-version.config';
import { Company } from '../../../modules/company/company.entity';
import { ModuleType } from '../../../modules/user-assignment/user-assignment.enum';
import { UserAssignmentRepository } from '../../../modules/user-assignment/user-assignment.repository';
import { UserRepository } from '../../../modules/user/user.repository';
import {
  convertFilterField,
  createSelectSql,
  createWhereSql,
  handleGetDataForAGGrid,
  isDoingGrouping,
  leadingZero,
  PayloadAGGridDto,
} from '../../../utils';
import { ElementMaster } from '../../element-master/element-master.entity';
import { StandardMaster } from '../../standard-master/entity/standard-master.entity';
import {
  CreateSelfAssessmentDto,
  ListSelfAssessmentDto,
  ListSelfAssessmentHistoryDto,
  ListSelfDeclarationDto,
  PublishSelfAssessmentDto,
  SELF_ASSESSMENT_FILTER_FIELDS,
  UpdateSelfAssessmentDto,
} from '../dto';
import { ReviewPrepComment } from '../entity/review-prep-comment.entity';
import { SelfAssessment } from '../entity/self-assessment.entity';
import { SelfDeclarationComment } from '../entity/self-declaration-comment.entity';
import { SelfDeclarationDocument } from '../entity/self-declaration-document.entity';
import { SelfDeclarationReference } from '../entity/self-declaration-reference.entity';
import { SelfDeclaration } from '../entity/self-declaration.entity';
import APP_CONFIG from '../../../configs/app.config';
import { SelfAssessmentAcknowledgeReviewRepository } from './self-assessment-acknowledge-review.repository';
import { SelfDeclarationRepository } from './self-declaration.repository';
import { HeadingExcelSA } from 'src/commons/enums/catalog.enum';
import { ComplianceAnswerRepository } from 'src/modules-qa/standard-master/repository/compliance-answer.repository';
import { BadRequestException } from '@nestjs/common';
import { StandardMasterRepository } from '../../standard-master/repository/standard-master.repository';
import { AuthorityMasterRepository } from '../../../modules/authority-master/authority-master.repository';
import { VESSEL_FILTER_FIELDS } from '../../../modules/vessel/dto/filter-vessel.dto';
import { DateRangeDto } from 'src/modules-qa/summary/dto';
import { FillSAChecklistQuestion } from '../../../modules/audit-workspace/entities/fill-sa-checklist-question.entity';
import { SAFindingItem } from '../../../modules/audit-workspace/entities/sa-finding-items.entity';
import { FillAuditChecklist } from '../../../modules/audit-workspace/entities/fill-audit-checklist.entity';
@EntityRepository(SelfAssessment)
export class SelfAssessmentRepository extends TypeORMRepository<SelfAssessment> {
  constructor(private readonly connection: Connection) {
    super();
  }
  async createSelfAssessment(user: TokenPayloadModel, body: CreateSelfAssessmentDto) {
    try {
      // const checkRoleOperator = await this.manager
      //   .getCustomRepository(SelfAssessmentRepository)
      //   .checkRoleByName(user, ROLE_NAME_FIXED.OPERATOR_DOC_HOLDER);
      // if (!checkRoleOperator) {
      //   throw new ForbiddenError({ message: 'common.FORBIDDEN_RESOURCE' });
      // }

      //check active standard master
      if (body.standardMasterId) {
        await this.manager
          .getCustomRepository(StandardMasterRepository)
          ._checkActiveStandardMaster([body.standardMasterId], user.companyId);
      }
      //check active Authority Master
      if (body.authorityId) {
        await this.manager
          .getCustomRepository(AuthorityMasterRepository)
          ._checkActiveAuthorityMaster([body.authorityId], user.companyId);
      }
      // Prepare general info
      const { ...dataCreate } = body;
      let prepareSelfAssessment: any = {
        ...dataCreate,
        companyId: user.companyId,
        createdUserId: user.id,
        auditCompanyId: user.explicitCompanyId,
      };

      // if (body.type === SelfAssessmentTypeEnum.REVIEW_PREP && body.closeOpenReviewPrep === true) {
      //   prepareSelfAssessment.status = SelfAssessmentStatusEnum.CLOSED;
      // }

      const currYear = moment().year();
      const rs = await this.connection.transaction(async (manager) => {
        const standardMaster = await manager.findOne(StandardMaster, {
          where: {
            id: prepareSelfAssessment.standardMasterId,
            deleted: false,
            companyId: user.companyId,
          },
        });
        if (!standardMaster) {
          throw new BaseError({ message: 'selfAssessment.STANDARD_MASTER_NOT_FOUND' });
        }
        const counter = await this.manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager,
            companyId: user.companyId,
            feature: FeatureVersionConfig.SELF_ASSESSMENT_SNO,
            year: Number(currYear),
          });
        const code = await this._genSelfAssessmentTableSNo(
          user.companyId,
          counter,
          Number(currYear),
        );

        //Find newest self assessment which have the same standard master
        const foundSA = await manager.findOne(SelfAssessment, {
          where: {
            standardMasterId: standardMaster.id,
            type: SelfAssessmentTypeEnum.OFFICIAL,
            //createdUserId: user.id,
            auditCompanyId: user.explicitCompanyId,
            deleted: false,
          },
          order: {
            createdAt: 'DESC',
          },
        });

        //If SA found copy all data from prev SA to current SA
        console.log(' foundSA)', foundSA);
        if (foundSA) {
          prepareSelfAssessment = {
            type: body.type,
            standardMasterId: foundSA.standardMasterId,
            lastExternalSubmission: body.lastExternalSubmission,
            submittedTo: body.submittedTo,
            companyName: body.companyName,
            inspectionTypeId: body.inspectionTypeId,
            authorityId: body.authorityId,
            attachments: decryptAttachmentValues(foundSA.attachments),
            closeOpenReviewPrep: body.closeOpenReviewPrep,
            companyId: user.companyId,
            auditCompanyId: user.explicitCompanyId,
            createdUserId: user.id,
          };
        }
        console.log(' prepareSelfAssessment)', prepareSelfAssessment);
        const selfAssessmentId = Utils.strings.generateUUID();
        const selfAssessmentCreate = await manager.insert(SelfAssessment, {
          ...prepareSelfAssessment,
          sNo: code,
          id: selfAssessmentId,
        });

        // insert self declaration
        const listElementMaster = await manager.find(ElementMaster, {
          where: {
            deleted: false,
            standardMasterId: standardMaster.id,
          },
        });

        if (listElementMaster.length > 0) {
          //Find self declaration from prev self assessment
          let listPrevSelfDeclaration = [];
          if (foundSA && body.new !== true) {
            listPrevSelfDeclaration = await manager.find(SelfDeclaration, {
              where: {
                selfAssessmentId: foundSA.id,
                elementMasterId: In(listElementMaster.map((eleMaster) => eleMaster.id)),
                deleted: false,
              },
            });
          }

          const counterRefId = await this.manager
            .getCustomRepository(CompanyFeatureVersionRepository)
            .getNextVersion({
              manager,
              companyId: user.companyId,
              feature: FeatureVersionConfig.SELF_DECLARATION_REF_ID,
              year: Number(currYear),
              take: listElementMaster.length,
            });
          const company = await this.manager.findOne(Company, {
            where: { id: user.companyId },
            select: ['code'],
          });

          const prepareSelfDeclaration = [];
          let listPrevSelfDeclarationCmt = [];
          let listPrevSelfDeclarationDocument = [];
          let listPrevSelfDeclarationReference = [];
          let listPrevReviewPrepComment = [];
          const prepareSelfDeclarationComment = [];
          const prepareSelfDeclarationDocument = [];
          const prepareSelfDeclarationReference = [];
          const prepareReviewPrepComment = [];
          if (listPrevSelfDeclaration.length > 0) {
            //Get previous self declaration related data
            const listPrevSelfDeclarationIds = listPrevSelfDeclaration.map((prevSD) => prevSD.id);
            listPrevSelfDeclarationCmt = await this.manager.find(SelfDeclarationComment, {
              where: {
                selfDeclarationId: In(listPrevSelfDeclarationIds),
              },
              relations: ['selfDeclaration'],
            });
            listPrevSelfDeclarationDocument = await this.manager.find(SelfDeclarationDocument, {
              where: {
                selfDeclarationId: In(listPrevSelfDeclarationIds),
              },
              relations: ['selfDeclaration'],
            });
            listPrevSelfDeclarationReference = await this.manager.find(SelfDeclarationReference, {
              where: {
                selfDeclarationId: In(listPrevSelfDeclarationIds),
              },
              relations: ['selfDeclaration'],
            });
            listPrevReviewPrepComment = await this.manager.find(ReviewPrepComment, {
              where: {
                selfDeclarationId: In(listPrevSelfDeclarationIds),
              },
              relations: ['selfDeclaration'],
            });

            listPrevSelfDeclaration.forEach((prevSD, index) => {
              const version = leadingZero(
                counterRefId - listPrevSelfDeclaration.length + index + 1,
                3,
              );
              prepareSelfDeclaration.push({
                complianceId: prevSD.complianceId,
                selfClose: prevSD.selfClose,
                targetCompletionDate: prevSD.targetCompletionDate,
                proposal: prevSD.proposal,
                operatingManagerCompletionDate: prevSD.operatingManagerCompletionDate,
                attachments: decryptAttachmentValues(prevSD.attachments),
                newestExternalComment: prevSD.newestExternalComment,
                elementMasterId: prevSD.elementMasterId,
                status: prevSD.status,
                selfAssessmentId,
                refId: `${company.code}/MSA/${version}/${currYear}`,
                reuseElement: true,
              });
            });
          } else {
            listElementMaster.forEach((elementMaster, index) => {
              const version = leadingZero(counterRefId - listElementMaster.length + index + 1, 3);
              prepareSelfDeclaration.push({
                elementMasterId: elementMaster.id,
                selfAssessmentId,
                refId: `${company.code}/MSA/${version}/${currYear}`,
              });
            });
          }

          const selfDeclarationCreated = await manager.save(
            SelfDeclaration,
            prepareSelfDeclaration,
          );
          // create history for self declaration
          if (listPrevSelfDeclarationCmt.length > 0) {
            selfDeclarationCreated.forEach((item) => {
              for (const prevCmt of listPrevSelfDeclarationCmt) {
                if (prevCmt.selfDeclaration.elementMasterId === item.elementMasterId) {
                  prepareSelfDeclarationComment.push({
                    type: prevCmt.type,
                    comment: prevCmt.comment,
                    createdUser: prevCmt.createdUser,
                    selfDeclarationId: item.id,
                  });
                }
              }
            });
            await manager.insert(SelfDeclarationComment, prepareSelfDeclarationComment);
          }

          if (listPrevSelfDeclarationDocument.length > 0) {
            selfDeclarationCreated.forEach((item) => {
              for (const prevDoc of listPrevSelfDeclarationDocument) {
                if (prevDoc.selfDeclaration.elementMasterId === item.elementMasterId) {
                  prepareSelfDeclarationDocument.push({
                    documentTitle: prevDoc.documentTitle,
                    selfDeclarationId: item.id,
                  });
                }
              }
            });
            await manager.insert(SelfDeclarationDocument, prepareSelfDeclarationDocument);
          }

          if (listPrevSelfDeclarationReference.length > 0) {
            selfDeclarationCreated.forEach((item) => {
              for (const prevRef of listPrevSelfDeclarationReference) {
                if (prevRef.selfDeclaration.elementMasterId === item.elementMasterId) {
                  prepareSelfDeclarationReference.push({
                    referenceModule: prevRef.referenceModule,
                    attachments: decryptAttachmentValues(prevRef.attachments),
                    selfDeclarationId: item.id,
                  });
                }
              }
            });
            await manager.insert(SelfDeclarationReference, prepareSelfDeclarationReference);
          }

          if (listPrevReviewPrepComment.length > 0) {
            selfDeclarationCreated.forEach((item) => {
              for (const prevReview of listPrevReviewPrepComment) {
                if (prevReview.selfDeclaration.elementMasterId === item.elementMasterId) {
                  prepareReviewPrepComment.push({
                    topic: prevReview.topic,
                    description: prevReview.description,
                    attachments: decryptAttachmentValues(prevReview.attachments),
                    selfDeclarationId: item.id,
                  });
                }
              }
            });
            await manager.insert(ReviewPrepComment, prepareReviewPrepComment);
          }
        }

        if (body.userAssignment) {
          // Update user assignment
          await manager.getCustomRepository(UserAssignmentRepository).createUserAssignment({
            selfAssessmentId,
            usersPermissions: body.userAssignment.usersPermissions,
          });
        }
        return selfAssessmentCreate;
      });
      return rs;
    } catch (ex) {
      LoggerCommon.error(
        '[SelfAssessmentRepository] createSelfAssessment error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
  async _listValidSACheckListsByPR(
    planningRequestId: string,
    inspectionMappingId?: string,
    auditChecklistIds?: string[],
    selectFields?: string[],
    entityType?: string,
    auditCompanyId?: string,
  ) {
    const qb = this.createQueryBuilder('selfAssessment')
      // .leftJoin('selfAssessment.inspectionMapping', 'inspectionMapping')
      .leftJoin('selfAssessment.selfDeclarations', 'selfDeclarations')
      .leftJoin('selfAssessment.standardMaster', 'standardMaster')
      .leftJoin('standardMaster.inspectionMapping', 'inspectionMapping')
      .leftJoin('selfDeclarations.compliance', 'compliance')
      .leftJoin('selfDeclarations.selfDeclarationComments', 'selfDeclarationComments')
      .leftJoin('selfDeclarations.elementMaster', 'elementMaster')
      .leftJoin('inspectionMapping.auditType', 'auditType')
      .leftJoin('auditType.planningRequests', 'planningRequest')
      .where(
        `
        planningRequest.id = :planningRequestId AND 
          inspectionMapping.deleted = :deleted and selfAssessment.auditCompanyId = :auditCompanyId and selfAssessment.type=:type and selfAssessment.deleted = :deleted and selfAssessment.status IN (:...status)`,
        {
          planningRequestId,
          deleted: false,
          auditCompanyId: auditCompanyId,
          type: SelfAssessmentTypeEnum.OFFICIAL,
          status: [SelfAssessmentStatusEnum.REVIEWED, SelfAssessmentStatusEnum.SUBMITTED],
        },
      )
      .addSelect([
        'selfDeclarations.id',
        'standardMaster.id',
        'compliance.id',
        'elementMaster.id',
        'selfDeclarationComments.id',
        'standardMaster.name',
        'elementMaster.stage',
        'elementMaster.code',
        'elementMaster.number',
        'elementMaster.keyPerformanceIndicator',
        'elementMaster.bestPracticeGuidance',
        'compliance.answer',
        'selfDeclarationComments.comment',
        'inspectionMapping.id',
        'auditType.id',
      ]);

    // if (entityType == 'Office') {
    //   qb.andWhere(
    //     'LOWER("planningRequest"."typeOfAudit"::TEXT) = ANY(LOWER("selfassessment"."visitTypes"::TEXT)::TEXT[])',
    //   );
    // }

    // if (inspectionMappingId) {
    //   qb.andWhere('inspectionMapping.id = :inspectionMappingId', { inspectionMappingId });
    // }

    // if (auditChecklistIds && auditChecklistIds?.length > 0) {
    //   qb.andWhere('selfassessment.id IN (:...auditChecklistIds)', {
    //     auditChecklistIds,
    //   });
    // }
    // return await this.getManyQB(qb); 
    // if (selectFields) {
    //   qb.select(
    //     selectFields.map((field) => {
    //       if (field.includes('.')) {
    //         return field;
    //       } else {
    //         return 'selfAssessment.' + field;
    //       }
    //     }),
    //   );
    // } else {
    //   qb.select();
    // }

    return this.getManyQB(qb);
  }

  async updateSelfAssessment(
    user: TokenPayloadModel,
    selfAssessmentId: string,
    body: UpdateSelfAssessmentDto,
  ) {
    try {
      const selfAssessment = await this.manager.findOne(SelfAssessment, {
        where: {
          id: selfAssessmentId,
          companyId: user.companyId,
          deleted: false,
        },
      });
      if (!selfAssessment) {
        throw new BaseError({ status: 404, message: 'selfAssessment.NOT_FOUND' });
      }

      //check active Authority Master
      if (body.authorityId && body.authorityId !== selfAssessment.authorityId) {
        await this.manager
          .getCustomRepository(AuthorityMasterRepository)
          ._checkActiveAuthorityMaster([body.authorityId], user.companyId);
      }

      // Prepare general info
      const { ...dataUpdate } = body;
      const prepareSelfAssessment: any = {
        ...dataUpdate,
        companyId: user.companyId,
        updatedUserId: user.id,
      };

      // if (body.type === SelfAssessmentTypeEnum.REVIEW_PREP && body.closeOpenReviewPrep === true) {
      //   prepareSelfAssessment.status = SelfAssessmentStatusEnum.CLOSED;
      // }
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      return await this.connection.transaction(async (manager) => {
        await manager.update(
          SelfAssessment,
          { id: selfAssessmentId, companyId: user.companyId, deleted: false },
          prepareSelfAssessment,
        );
        if (body.userAssignment) {
          // Update user assignment
          await manager
            .getCustomRepository(UserAssignmentRepository)
            .updateUserAssignment(
              manager,
              ModuleType.SELF_ASSESSMENT,
              selfAssessmentId,
              body.userAssignment.usersPermissions,
            );
        }
        // if (
        //   body.type &&
        //   selfAssessment.type === SelfAssessmentTypeEnum.WORKING &&
        //   body.type === SelfAssessmentTypeEnum.OFFICIAL
        // ) {
        //   // update all status self declaration to Pending
        //   // await manager.update(
        //   //   SelfDeclaration,
        //   //   { selfAssessmentId: selfAssessment },
        //   //   { status: SelfDeclarationStatusEnum.PENDING },
        //   // );
        //   const listUserRoleVettingManager = await this._getAllListUserByRoleName(
        //     user,
        //     ROLE_NAME_FIXED.VETTING_MANAGER.toLowerCase(),
        //   );
        //   const listReceiverNoti = await manager
        //     .getCustomRepository(UserRepository)
        //     .listByIds(listUserRoleVettingManager);
        //   const performer = await manager
        //     .getCustomRepository(UserRepository)
        //     .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
        //   dataNoti.push({
        //     receivers: listReceiverNoti as IUser[],
        //     module: ModuleType.SELF_ASSESSMENT,
        //     recordId: selfAssessment.id,
        //     recordRef: selfAssessment.sNo,
        //     type: PushTypeEnum.CHANGE_RECORD_TYPE,
        //     currentStatus: SelfAssessmentTypeEnum.OFFICIAL,
        //     previousStatus: SelfAssessmentTypeEnum.WORKING,
        //     performer: performer,
        //     executedAt: new Date(),
        //   });
        // }
        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[SelfAssessmentRepository] updateSelfAssessment error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  private async _genSelfAssessmentTableSNo(companyId: string, counter: number, currYear: number) {
    const version = leadingZero(counter, 4);
    const company = await this.manager.findOne(Company, {
      where: { id: companyId },
      select: ['code'],
    });
    return `MSA${company.code}${currYear}${version}`;
  }

  async listSelfAssessment(
    token: TokenPayloadModel,
    query: ListSelfAssessmentDto,
    rolePermissions?: string[],
    body?: PayloadAGGridDto,
  ) {
    const queryBuilder = this.createQueryBuilder('selfAssessment')
      .leftJoin('selfAssessment.createdUser', 'createdUser')
      .leftJoin('createdUser.company', 'createdCompany')
      .leftJoin('selfAssessment.updatedUser', 'updatedUser')
      .leftJoin('selfAssessment.company', 'company')
      .leftJoin('selfAssessment.standardMaster', 'standardMaster')
      .leftJoin('selfAssessment.inspectionType', 'inspectionType')
      .leftJoin('selfAssessment.authority', 'authority')
      .where(`selfAssessment.companyId = '${token.companyId}'`)
      .select();

    const fieldSelects = [
      'company.name',
      'createdUser.username',
      'createdUser.companyId',
      'createdCompany.name',
      'updatedUser.username',
      'standardMaster.name',
      'standardMaster.code',
      'inspectionType.name',
      'authority.name',
    ];

    if (query.content) {
      queryBuilder.andWhere(
        `(standardMaster.code LIKE :content OR standardMaster.name LIKE '%${query.content}%')`,
      );
    }

    if (query.ids?.length) {
      queryBuilder
        .leftJoin('selfAssessment.selfDeclarations', 'selfDeclarations')
        .addSelect(['selfDeclarations.id', 'selfDeclarations.complianceId']);

      queryBuilder.andWhere('selfAssessment.id IN (:...ids)', {
        ids: query.ids,
      });
    }
    switch (query.tab) {
      case SelfAssessmentTabEnum.WORKING:
        queryBuilder.andWhere(`selfAssessment.type = '${query.tab}'`);
        break;
      case SelfAssessmentTabEnum.OFFICIAL:
        queryBuilder.andWhere(
          `selfAssessment.type = '${query.tab}' AND selfAssessment.status != '${SelfAssessmentStatusEnum.INACTIVE}'`,
        );
        break;
      case SelfAssessmentTabEnum.INACTIVE:
        queryBuilder.andWhere(`selfAssessment.status = '${SelfAssessmentStatusEnum.INACTIVE}'`);
        if (query?.auditCompanyId) {
          queryBuilder.andWhere(`selfAssessment.auditCompanyId = '${query?.auditCompanyId}'`);
        }
        break;
      default:
    }
    let isSelfAssesmentPermission: boolean;

    if (rolePermissions?.length > 0) {
      const checkSelfAssesmentPermission = rolePermissions?.includes(
        FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT +
          '::' +
          SubFeatureEnum.SELF_ASSESSMENT +
          '::' +
          ActionEnum.REVIEW,
      );
      isSelfAssesmentPermission =
        token.companyLevel === CompanyLevelEnum.MAIN_COMPANY && checkSelfAssesmentPermission;
    }
    // if (!RoleScopeCheck.isAdmin(token)) {
    // const checkRoleVettingManager = await this.manager
    //   .getCustomRepository(SelfAssessmentRepository)
    //   .checkRoleByName(token, ROLE_NAME_FIXED.VETTING_MANAGER.toLowerCase());
    let reviewer: boolean;
    if (RoleScopeCheck.isAdmin(token) || isSelfAssesmentPermission) {
      reviewer = true;
      queryBuilder.andWhere(
        `(selfAssessment.type = '${SelfAssessmentTypeEnum.OFFICIAL}' OR selfAssessment.createdUserId = '${token.id}' OR createdUser.companyId = '${token.explicitCompanyId}')`,
      );
    } else {
      reviewer = false;
      //operator and creator create record
      queryBuilder.andWhere(`(createdUser.companyId = '${token.explicitCompanyId}')`);
    }
    // }

    //AG-Grid
    const connection = getConnection();
    const subQueryBuilder = connection.createQueryBuilder();
    queryBuilder.addSelect(fieldSelects);

    if (body) {
      convertFilterField(body, SELF_ASSESSMENT_FILTER_FIELDS);

      queryBuilder.distinctOn(['selfAssessment.id'])
        .andWhere(`selfAssessment.deleted = false`);

      subQueryBuilder
        .select(`DISTINCT "selfAssessment_id" AS id`)
        .from(`(${queryBuilder.getQuery()})`, 'subquery');

      createWhereSql(body, subQueryBuilder);
      createSelectSql(body, subQueryBuilder, null, '"selfAssessment_id"');

      queryBuilder.groupBy();
    }

    //update after build sql ag-grid
    queryBuilder
      .leftJoin('selfAssessment.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      .addSelect(['userAssignments.id', 'userAssignments.permission', 'user.id', 'user.username']);

    const selfAssesmentdata = await handleGetDataForAGGrid(
      this,
      queryBuilder,
      query,
      body,
      subQueryBuilder,
      queryBuilder,
      'selfAssessment',
      [],
    );
    if (body && isDoingGrouping(body)) {
      return selfAssesmentdata;
    }

    selfAssesmentdata.data.forEach((item) => {
      return Object.assign(item, { reviewer });
    });
    return selfAssesmentdata;
  }

  async listSelfAssessmentHistory(token: TokenPayloadModel, query: ListSelfAssessmentHistoryDto) {
    const queryBuilder = this.createQueryBuilder('selfAssessment')
      .leftJoin('selfAssessment.selfDeclarations', 'selfDeclarations')
      .leftJoin(
        'selfAssessment.selfAssessmentAcknowledgeReviews',
        'selfAssessmentAcknowledgeReviews',
      )
      .leftJoin('selfAssessment.createdUser', 'createdUser')
      .leftJoin('selfAssessment.company', 'company')
      .leftJoin('selfAssessment.standardMaster', 'standardMaster')
      .where(
        'selfAssessment.companyId = :companyId  AND selfAssessment.status = :status AND selfDeclarations.deleted = false',
        {
          companyId: token.companyId,
          status: SelfAssessmentStatusEnum.INACTIVE,
        },
      )
      .select()
      .addSelect([
        'company.name',
        'selfAssessmentAcknowledgeReviews.id',
        'selfAssessmentAcknowledgeReviews.comment',
        'createdUser.username',
        'createdUser.companyId',
        'standardMaster.name',
        'standardMaster.code',
        'selfDeclarations.id',
        'selfDeclarations.complianceId',
      ]);

    if (query.createdUserId) {
      queryBuilder.andWhere('selfAssessment.createdUserId = :createdUserId', {
        createdUserId: query.createdUserId,
      });
    }

    if (query.standardMasterId) {
      queryBuilder.andWhere('selfAssessment.standardMasterId = :standardMasterId', {
        standardMasterId: query.standardMasterId,
      });
    }

    if (query.createdCompanyId) {
      queryBuilder.andWhere('createdUser.companyId = :createdCompanyId', {
        createdCompanyId: query.createdCompanyId,
      });
    }
    // if (!RoleScopeCheck.isAdmin(token)) {
    const checkRoleVettingManager = await this.manager
      .getCustomRepository(SelfAssessmentRepository)
      .checkRoleByName(token, ROLE_NAME_FIXED.VETTING_MANAGER);

    if (RoleScopeCheck.isAdmin(token) || checkRoleVettingManager) {
      queryBuilder.andWhere(
        '(selfAssessment.type = :type OR selfAssessment.createdUserId = :userId OR createdUser.companyId = :ownCompany)',
        {
          type: SelfAssessmentTypeEnum.OFFICIAL,
          userId: token.id,
          ownCompany: token.explicitCompanyId,
        },
      );
    } else {
      queryBuilder.andWhere('(createdUser.companyId = :ownCompany)', {
        ownCompany: token.explicitCompanyId,
      });
    }
    // }

    const selfAssessments = await queryBuilder.getMany();

    const filteredSelfAssessments = selfAssessments.filter(
      (sa) => sa.selfAssessmentAcknowledgeReviews.filter((sar) => sar.deleted === true).length < 1,
    );

    const rs = [];
    for (let i = 0; i < filteredSelfAssessments.length; i++) {
      let countPending = 0;
      for (const selfDeclaration of filteredSelfAssessments[i].selfDeclarations) {
        if (!selfDeclaration.complianceId) {
          countPending++;
        }
      }
      rs.push({
        ...filteredSelfAssessments[i],
        countPending,
        countSaved: filteredSelfAssessments[i].selfDeclarations.length - countPending,
        inActiveDate: filteredSelfAssessments[i].updatedAt,
      });
    }
    return rs;
  }

  async getDetailSelfAssessment(
    token: TokenPayloadModel,
    selfAssessmentId: string,
    rolePermissions?: any[],
    query?: ListSelfDeclarationDto,
  ) {
    const selfAssessment = await this.getOneQB(
      this.createQueryBuilder('selfAssessment')
        .leftJoin('selfAssessment.company', 'company')
        .leftJoin('selfAssessment.auditCompany', 'auditCompany')
        .leftJoin('selfAssessment.createdUser', 'createdUser')
        .leftJoin('selfAssessment.standardMaster', 'standardMaster')
        .leftJoin('selfAssessment.selfDeclarations', 'selfDeclarations')
        .leftJoin('selfDeclarations.elementMaster', 'elementMaster')
        .leftJoin('selfDeclarations.compliance', 'compliance')
        .leftJoin('selfAssessment.userAssignments', 'userAssignments')
        .leftJoin('userAssignments.user', 'user')
        .leftJoin('user.company', 'userCompany')
        .leftJoin('user.divisions', 'userDivisions')
        .select()
        .addSelect([
          'company.name',
          'auditCompany.isCompanyRestricted',
          'createdUser.username',
          'createdUser.companyId',
          'standardMaster.name',
          'standardMaster.code',
          'standardMaster.scoreApplicable',
          'standardMaster.scoringMethod',
          'selfDeclarations.id',
          'selfDeclarations.status',
          'selfDeclarations.selfClose',
          'selfDeclarations.operatingManagerCompletionDate',
          'selfDeclarations.targetCompletionDate',
          'selfDeclarations.attachments',
          'elementMaster.id',
          'elementMaster.code',
          'elementMaster.name',
          'elementMaster.number',
          'elementMaster.questionNumber',
          'elementMaster.stage',
          'elementMaster.elementStageQ',
          'elementMaster.keyPerformanceIndicator',
          'elementMaster.bestPracticeGuidance',
          'elementMaster.otherInfoGuidance',
          'elementMaster.aimPrinciple',
          'elementMaster.createdAt',
          'compliance.answer',
          'compliance.compliance',
          'compliance.colour',
          'userAssignments.id',
          'userAssignments.permission',
          'user.id',
          'user.username',
          'user.jobTitle',
          'userCompany.name',
          'userCompany.code',
          'userDivisions.name',
          'userDivisions.code',
        ])
        .where('selfAssessment.companyId = :companyId AND selfAssessment.id = :selfAssessmentId', {
          companyId: token.companyId,
          selfAssessmentId,
        })
        .orderBy('elementMaster.createdAt', 'ASC')
        .addOrderBy('elementMaster.code', 'ASC'),
    );

    if (selfAssessment) {
      // Extract audit company details to parent object
      if (selfAssessment.auditCompany) {
        Object.assign(selfAssessment, selfAssessment.auditCompany);
        delete selfAssessment.auditCompany;
      }

      // add reviewElement key for known elements was reviewed
      // get the elementMaster
      const elements = selfAssessment.selfDeclarations.map((x) => {
        return x.elementMaster;
      });
      for (let i = 0; i < elements.length; i++) {
        // check the self assessment status was 'Reviewed'
        if (selfAssessment.type === SelfAssessmentTypeEnum.OFFICIAL) {
          // get review data using the elementMaster data
          const getReview = await this.connection
            .getCustomRepository(SelfAssessmentAcknowledgeReviewRepository)
            .createQueryBuilder('selfAssessmentAcknowledgeReview')
            .select()
            .where(
              'selfAssessmentAcknowledgeReview.code = :code AND selfAssessmentAcknowledgeReview.stage = :stage AND selfAssessmentAcknowledgeReview.questionNumber = :questionNumber AND selfAssessmentAcknowledgeReview.selfAssessmentId = :selfAssessmentId',
              {
                code: elements[i].code,
                stage: elements[i].stage,
                questionNumber: elements[i].questionNumber,
                selfAssessmentId: selfAssessment.id,
              },
            )
            .getOne();
          if (getReview && getReview !== undefined) {
            // if get the review data then put the reviewElement key
            Object.assign(selfAssessment.selfDeclarations[i], { reviewedElement: true });
          } else {
            // else put false
            Object.assign(selfAssessment.selfDeclarations[i], { reviewedElement: false });
          }
        } else {
          // else put false
          Object.assign(selfAssessment.selfDeclarations[i], { reviewedElement: false });
        }
      }

      const isReview = this._checkRoleReviewPermission(token, rolePermissions);
      Object.assign(selfAssessment, { reviewer: isReview });

      // Notifications will be filtered by notification status such as Pending, Saved
      if (query?.selfDeclarationStatus) {
        selfAssessment.selfDeclarations = selfAssessment.selfDeclarations.filter(
          (item) => item.status === query.selfDeclarationStatus,
        );
      }
      return selfAssessment;
    } else {
      throw new BaseError({ status: 404, message: 'selfAssessment.NOT_FOUND' });
    }
  }

  async deleteSelfAssessment(token: TokenPayloadModel, selfAssessmentId: string) {
    const selfAssessment = await this.manager.findOne(SelfAssessment, {
      where: {
        id: selfAssessmentId,
        companyId: token.companyId,
        deleted: false,
      },
    });
    if (!selfAssessment) {
      throw new BaseError({ status: 404, message: 'selfAssessment.NOT_FOUND' });
    }

    // soft delete
    const updateResult = await this.softDelete({
      id: selfAssessmentId,
      companyId: token.companyId,
    });
    if (updateResult.affected === 0) {
      throw new BaseError({ status: 404, message: 'selfAssessment.NOT_FOUND' });
    } else {
      return 1;
    }
  }

  async publishOfficialSelfAssessment(
    user: TokenPayloadModel,
    selfAssessmentId: string,
    body: PublishSelfAssessmentDto,
  ) {
    try {
      const { ...dataUpdate } = body;
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      return await this.connection.transaction(async (manager) => {
        const selfAssessment = await manager.findOne(SelfAssessment, {
          where: {
            id: selfAssessmentId,
            companyId: user.companyId,
          },
        });
        if (selfAssessment && selfAssessment.standardMasterId) {
          // update old self-assessment publish
          const publishSelfAssessent = await this.createQueryBuilder('selfAssessment')
            .leftJoin('selfAssessment.createdUser', 'createdUser')
            .where(
              `selfAssessment.id != :selfAssessmentId 
              AND selfAssessment.type = :type 
              AND selfAssessment.standardMasterId = :standardMasterId 
              AND (selfAssessment.status = :statusReviewed 
              OR selfAssessment.status = :statusSubmitted)
              AND createdUser.companyId = :companyId 
              AND selfAssessment.deleted = false`,
              {
                selfAssessmentId: selfAssessmentId,
                type: SelfAssessmentTypeEnum.OFFICIAL,
                standardMasterId: selfAssessment.standardMasterId,
                statusReviewed: SelfAssessmentStatusEnum.REVIEWED,
                statusSubmitted: SelfAssessmentStatusEnum.SUBMITTED,
                companyId: user.explicitCompanyId,
              },
            )
            .getOne();
          // update selfassessment status into "Inactive"
          if (publishSelfAssessent) {
            await manager.update(
              SelfAssessment,
              {
                type: SelfAssessmentTypeEnum.OFFICIAL,
                standardMasterId: selfAssessment.standardMasterId,
                status: publishSelfAssessent.status,
                id: publishSelfAssessent.id,
                deleted: false,
              },
              {
                status: SelfAssessmentStatusEnum.INACTIVE,
              },
            );
          }
        }
        // Prepare general info
        // update self assessment status info "Submitted"
        const tempSubmittedDate = new Date();
        const submittedDateMonth = tempSubmittedDate.getMonth();
        const monthList = Object.keys(SelfAssessmentMonthEnum).map(
          (key) => SelfAssessmentMonthEnum[key],
        );
        const finalSubmittedDateMonth = monthList[submittedDateMonth];
        const submittedDateYear = tempSubmittedDate.getFullYear();
        const prepareSelfAssessment: any = {
          ...dataUpdate,
          type: SelfAssessmentTypeEnum.OFFICIAL,
          updatedUserId: user.id,
          status: SelfAssessmentStatusEnum.SUBMITTED,
          submittedDate: tempSubmittedDate,
          submittedDate_Month: finalSubmittedDateMonth,
          submittedDate_Year: submittedDateYear,
        };
        await manager.update(
          SelfAssessment,
          { id: selfAssessmentId, companyId: user.companyId, deleted: false },
          prepareSelfAssessment,
        );
        // to fetch the standard master deatils for given the name in mail
        const standardMaster = await manager.findOne(StandardMaster, {
          where: { id: selfAssessment.standardMasterId },
        });
        // to fetch the assessment details for given the submittedDate in mail
        const getSelfAssessment = await manager.find(SelfAssessment, {
          id: selfAssessmentId,
          companyId: user.companyId,
          deleted: false,
        });
        // to get operator company name for mail
        const operatorCompany = await manager
          .getCustomRepository(UserRepository)
          .listByIds([user.id]);
        let listReceiverNoti = [];
        // change logic, only person who has review access [main company users only] can receive mail/notification
        const listUserReviewPermission = await this._getAllListUserByReviewPermission(user);
        if (listUserReviewPermission.length > 0) {
          listReceiverNoti = await manager
            .getCustomRepository(UserRepository)
            .listByIds(listUserReviewPermission);
        }
        const performer = await manager
          .getCustomRepository(UserRepository)
          .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
        dataNoti.push({
          receivers: listReceiverNoti as IUser[],
          module: ModuleType.SELF_ASSESSMENT,
          recordId: selfAssessment.id,
          recordRef: selfAssessment.sNo,
          type: PushTypeEnum.CHANGE_RECORD_TYPE,
          currentStatus: SelfAssessmentStatusEnum.SUBMITTED,
          previousStatus: SelfAssessmentStatusEnum.INPROGRESS,
          performer: performer,
          executedAt: new Date(),
        });

        // // list User Assignment
        // const listUserAssignment = await manager
        //   .getCustomRepository(UserAssignmentRepository)
        //   .listByModule(ModuleType.SELF_ASSESSMENT, selfAssessment.id);
        // const userCreator = await manager
        //   .getCustomRepository(UserRepository)
        //   .listByIds([selfAssessment.createdUserId]);
        // let listReceiverNoti = [];
        // listReceiverNoti = listReceiverNoti.concat(listUserAssignment[WorkflowPermission.REVIEWER]);
        // listReceiverNoti = listReceiverNoti.concat(
        //   listUserAssignment[WorkflowPermission.PUBLISHER],
        // );
        // listReceiverNoti = listReceiverNoti.concat(userCreator);

        // enable mail for operation submit the assessment

        const submittedDate = getSelfAssessment[0].submittedDate.toLocaleDateString('en-GB');
        for (const receiver of listReceiverNoti) {
          dataSendMail.push({
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.UPDATE_RECORD,
            templateKey: MailTemplate.SUBMITTED_SELF_ASSESSMENT,
            subject: 'Self Assessment Submitted',
            data: {
              userName: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordRef: selfAssessment.sNo,
              recordId: selfAssessment.id,
              standardName: standardMaster.name,
              operatorCompany: operatorCompany[0].company.name,
              path: ModulePathEnum.SELF_ASSESSMENT,
              // path: ModulePathEnum.SELF_ASSESSMENT + '/declaration',
              queryParam: '?standard-and-matrix',
              // currentStatus: SelfAssessmentStatusEnum.SUBMITTED,
              // previousStatus: selfAssessment.status,
              // performer: performer,
              submitionDate: submittedDate,
            },
          });
        }
        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[SelfAssessmentRepository] publishOfficialSelfAssessment error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
  async unPublishOfficialSelfAssessment(user: TokenPayloadModel, selfAssessmentId: string) {
    try {
      // Prepare general info
      const prepareSelfAssessment = {
        type: SelfAssessmentTypeEnum.WORKING,
        updatedUserId: user.id,
        status: SelfAssessmentStatusEnum.OPEN,
      };
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      return await this.connection.transaction(async (manager) => {
        const selfAssessment = await manager.findOne(SelfAssessment, {
          where: {
            id: selfAssessmentId,
            companyId: user.companyId,
          },
        });
        if (!selfAssessment) {
          throw new BaseError({ status: 404, message: 'selfAssessment.NOT_FOUND' });
        }
        await manager.update(
          SelfAssessment,
          { id: selfAssessmentId, companyId: user.companyId, deleted: false },
          prepareSelfAssessment,
        );
        // list User Assignment
        // const listUserAssignment = await manager
        //   .getCustomRepository(UserAssignmentRepository)
        //   .listByModule(ModuleType.SELF_ASSESSMENT, selfAssessment.id);
        // const userCreator = await manager
        //   .getCustomRepository(UserRepository)
        //   .listByIds([selfAssessment.createdUserId]);
        // let listReceiverNoti = [];
        // listReceiverNoti = listReceiverNoti.concat(listUserAssignment[WorkflowPermission.REVIEWER]);
        // listReceiverNoti = listReceiverNoti.concat(
        //   listUserAssignment[WorkflowPermission.PUBLISHER],
        // );
        // listReceiverNoti = listReceiverNoti.concat(userCreator);
        // const performer = await manager
        //   .getCustomRepository(UserRepository)
        //   .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
        // dataNoti.push({
        //   receivers: listReceiverNoti as IUser[],
        //   module: ModuleType.SELF_ASSESSMENT,
        //   recordId: selfAssessment.id,
        //   recordRef: selfAssessment.sNo,
        //   type: PushTypeEnum.UPDATE_RECORD,
        //   currentStatus: SelfAssessmentStatusEnum.OPEN,
        //   previousStatus: selfAssessment.status,
        //   performer: performer,
        //   executedAt: new Date(),
        // });
        // for (const receiver of listReceiverNoti) {
        //   dataSendMail.push({
        //     receiver: receiver as IUserEmail,
        //     type: EmailTypeEnum.UPDATE_RECORD,
        //     templateKey: MailTemplate.CHANGE_RECORD_STATUS,
        //     subject: '[Notification] Change status in a record',
        //     data: {
        //       username: receiver.username,
        //       baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
        //       recordRef: selfAssessment.sNo,
        //       recordId: selfAssessment.id,
        //       path: ModulePathEnum.SELF_ASSESSMENT,
        //       // path: ModulePathEnum.SELF_ASSESSMENT + '/declaration',
        //       // queryParam: '',
        //       currentStatus: SelfAssessmentStatusEnum.OPEN,
        //       previousStatus: selfAssessment.status,
        //       performer: performer,
        //       executedAt: new Date(),
        //     },
        //   });
        // }
        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[SelfAssessmentRepository] unPublishOfficialSelfAssessment error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
  async checkRoleByName(user: TokenPayloadModel, roleName) {
    const rawQuery = `
              select
                ur."userId"
              from
                user_role ur
              left join role on
                ur."roleId" = role.id
              where
              (role."companyId" = '${user.companyId}' OR role."companyId" IS NULL AND role."isDefault" = TRUE)
                and 
              role.name like '%${roleName}%' 
                and ur."userId" = '${user.id}'
    `;
    const listUserRole = await this.connection.query(rawQuery);
    if (listUserRole && listUserRole.length > 0) {
      return true;
    }
    return false;
  }

  async _getAllListUserByReviewPermission(user: TokenPayloadModel) {
    const rawQuery = `
    SELECT DISTINCT urp."userId" FROM user_role_permission urp 
    INNER JOIN "user" u ON CAST(u.id AS varchar) = urp."userId" 
    INNER JOIN company c ON c.id = u."companyId" 
    WHERE c."companyLevel" = 'Main Company' AND ((c.id = '${user.companyId}' AND u."parentCompanyId" IS NULL) 
    OR (c.id = '${user.companyId}' AND u."parentCompanyId" = '${user.companyId}'))
    AND urp."featureName" ILIKE '%${FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT}::${SubFeatureEnum.SELF_ASSESSMENT}%' 
    AND urp."actionName" ILIKE '%${ActionEnum.REVIEW}%'`;
    const listUserRole = await this.connection.query(rawQuery);
    const listUserId = [];
    for (const user of listUserRole) {
      listUserId.push(user.userId);
    }
    return listUserId;
  }

  async checkExistPublishOfficial(user: TokenPayloadModel, selfAssessmentId: string) {
    try {
      const selfAssessment = await this.findOne({
        where: {
          id: selfAssessmentId,
          companyId: user.companyId,
        },
      });
      if (selfAssessment && selfAssessment.standardMasterId) {
        // find old self-assessment publish
        const publishSelfAssessent = await this.createQueryBuilder('selfAssessment')
          .leftJoin('selfAssessment.createdUser', 'createdUser')
          .where(
            'selfAssessment.id != :selfAssessmentId AND selfAssessment.type = :type AND selfAssessment.standardMasterId = :standardMasterId AND selfAssessment.status = :status AND createdUser.companyId = :companyId  AND selfAssessment.deleted = false',
            {
              selfAssessmentId: selfAssessmentId,
              type: SelfAssessmentTypeEnum.OFFICIAL,
              standardMasterId: selfAssessment.standardMasterId,
              status: SelfAssessmentStatusEnum.SUBMITTED,
              companyId: user.explicitCompanyId,
            },
          )
          .getOne();

        if (publishSelfAssessent) {
          return true;
        }
      }
      return false;
    } catch (ex) {
      LoggerCommon.error(
        '[SelfAssessmentRepository] checkExistPublishOfficial error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async importCompliance(
    dataExcels: any[],
    user: TokenPayloadModel,
    selfAssessmentId: string,
    standardMasterId: string,
  ) {
    const complianceAnswers = await this.connection
      .getCustomRepository(ComplianceAnswerRepository)
      .find({
        where: { standardMasterId },
      });

    const preparedSelfDeclaration = [];
    const listSelfDeclarationId = dataExcels.map((i) => i[HeadingExcelSA.ID]);

    let selfDeclarations = [];
    if (listSelfDeclarationId.length > 0) {
      selfDeclarations = await this.connection
        .getCustomRepository(SelfDeclarationRepository)
        .createQueryBuilder('selfDeclaration')
        .where(
          'selfDeclaration.selfAssessmentId = :selfAssessmentId AND selfDeclaration.id IN (:...listSelfDeclarationId)',
          {
            selfAssessmentId,
            listSelfDeclarationId: listSelfDeclarationId,
          },
        )
        .select(['selfDeclaration.id'])
        .getMany();
    }

    if (selfDeclarations.length !== dataExcels.length)
      throw new BadRequestException(`Id selfDeclarations not found!`);

    for (let i = 0; i < dataExcels.length; i++) {
      preparedSelfDeclaration.push({
        id: dataExcels[i][HeadingExcelSA.ID],
        newestExternalComment: dataExcels[i][HeadingExcelSA.COMPANY_OPERATOR_COMMENT],
        // Multiply the excel compliance values by 100. if the compliance answer has '%' values.
        complianceId: complianceAnswers.find(
          (value) =>
            value.answer ===
            (value.answer.includes('%')
              ? (parseFloat(dataExcels[i][HeadingExcelSA.COMPLIANCE]) * 100).toString() + '%'
              : dataExcels[i][HeadingExcelSA.COMPLIANCE]),
        )?.id,
        status: SelfDeclarationStatusEnum.SAVED, // attack compliance id to self declaration
      });
    }

    return await this.manager.transaction(async (managerTrans) => {
      await managerTrans.save(SelfDeclaration, preparedSelfDeclaration);
    });
  }

  async getComplianceBySelfAssessmentId(standardMasterId: string) {
    const complianceAnswers = await this.connection
      .getCustomRepository(ComplianceAnswerRepository)
      .createQueryBuilder('complianceAnswer')
      .where('complianceAnswer.standardMasterId = :standardMasterId', {
        standardMasterId,
      })
      .getMany();

    return complianceAnswers;
  }
  async getSelfassesentDashboard(user: TokenPayloadModel, tab: string, query: DateRangeDto) {
    try {
      let queryDate = '';
      if (query?.fromDate && query?.toDate && tab == 'Working') {
        queryDate = `
      AND sa."createdAt" >= '${query?.fromDate}'
      AND sa."createdAt" <= '${query?.toDate}'`;
      } else if (query?.fromDate && query?.toDate && tab == 'Official') {
        queryDate = `
      AND sa."submittedDate" >= '${query?.fromDate}'
      AND sa."submittedDate" <= '${query?.toDate}'`;
      } else if (query?.fromDate && query?.toDate && tab == 'Inactive') {
        queryDate = `
      AND sa."updatedAt" >= '${query?.fromDate}'
      AND sa."updatedAt" <= '${query?.toDate}'`;
      }
      const rawQuery = `SELECT
    sa."status" as "status",
    COUNT(distinct sa."id") AS  "total",
    json_agg(json_build_object(
      'id', sa.id,
      'sNo', sa."sNo",
      'status', sa.status,
      'submittedDate', sa."submittedDate",
      'companyName', c."name",
      'createdUser', u."username",
      'standardName', sm."name")) AS list
  FROM
  self_assessment sa
  LEFT JOIN standard_master sm on sa."standardMasterId" = sm.id
  LEFT JOIN "user" u on u.id = sm."createdUserId"
  LEFT JOIN company c on c.id = sm."companyId"
  WHERE
  sa."companyId" = $1
  AND (
    CASE $2
        WHEN 'Working' THEN CAST(sa.type AS TEXT) = $2
        WHEN 'Official' THEN CAST(sa.type AS TEXT) = $2 AND sa.status::TEXT != 'Inactive'
        WHEN 'Inactive' THEN sa.status::TEXT = $2
    END
) ${queryDate}
 AND
  sa."deleted" = false 
  GROUP BY sa."status"`;
      return await this.connection.query(rawQuery, [user.companyId, tab]);
    } catch (e) {
      LoggerCommon.error(
        '[SelfAssessmentRepository] getSelfassessmentDashboard error ',
        e.message || e,
      );
      throw e;
    }
  }

  _checkRoleReviewPermission(user: TokenPayloadModel, rolePermissions: any[]) {
    let reviewer = false;
    if (user.companyLevel === CompanyLevelEnum.MAIN_COMPANY) {
      // fetch the non package role permissions
      const nonPackageRoles = rolePermissions.filter((item) => {
        return (
          item.role_name !==
          (FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_AND_QA_PACKAGE_ROLES ||
            FIXED_PACKAGE_ROLE_NAME.INAUTIX_INSPECTIONS_PACKAGE_ROLES ||
            FIXED_PACKAGE_ROLE_NAME.INAUTIX_QA_PACKAGE_ROLES)
        );
      });
      // if non package roles have review permission the user should be can review the assessment
      if (
        nonPackageRoles &&
        nonPackageRoles.find((x) => {
          return (
            x.permission ===
            FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT +
              '::' +
              SubFeatureEnum.SELF_ASSESSMENT +
              '::' +
              ActionEnum.REVIEW
          );
        })
      ) {
        reviewer = true;
      }
    }
    return reviewer;
  }

  /**
   * Get SA finding items for a self assessment from the latest fill checklist only
   * @param user Current user
   * @param selfAssessmentId Self assessment ID
   * @returns SA finding items for the self assessment from the latest fill checklist only
   */
  async getSAFindingItems(
    user: TokenPayloadModel,
    selfAssessmentId: string,
  ): Promise<SAFindingItem[]> {
    try {
      // First, verify the self assessment exists and belongs to the user's company
      const selfAssessment = await this.createQueryBuilder('selfAssessment')
        .leftJoinAndSelect('selfAssessment.standardMaster', 'standardMaster')
        .where('selfAssessment.id = :selfAssessmentId', { selfAssessmentId })
        .andWhere('selfAssessment.companyId = :companyId', { companyId: user.companyId })
        .andWhere('selfAssessment.deleted = :deleted', { deleted: false })
        .getOne();

      if (!selfAssessment) {
        throw new BaseError({ status: 404, message: 'selfAssessment.NOT_FOUND' });
      }

      // Find the latest fillAuditChecklist for this self assessment
      const latestFillChecklist = await this.manager
        .createQueryBuilder(FillAuditChecklist, 'fillAuditChecklist')
        .where('fillAuditChecklist.selfAssessmentId = :selfAssessmentId', { selfAssessmentId })
        .andWhere('fillAuditChecklist.deleted = :deleted', { deleted: false })
        .orderBy('fillAuditChecklist.createdAt', 'DESC')
        .addOrderBy('fillAuditChecklist.updatedAt', 'DESC')
        .limit(1)
        .getOne();

      if (!latestFillChecklist) {
        // No fill checklist found, return empty array
        return [];
      }

      // Get SA finding items only from the latest fill checklist
      const saFindingItems = await this.manager
        .createQueryBuilder(SAFindingItem, 'saFindingItem')
        .leftJoinAndSelect('saFindingItem.elementMaster', 'elementMaster')
        .leftJoinAndSelect('saFindingItem.fillSAChecklistQuestion', 'fillSAChecklistQuestion')
        .leftJoinAndSelect('fillSAChecklistQuestion.fillAuditChecklist', 'fillAuditChecklist')
        .leftJoinAndSelect(
          'fillSAChecklistQuestion.auditorComplianceAnswer',
          'auditorComplianceAnswer',
        )
        .where('saFindingItem.selfAssessmentId = :selfAssessmentId', { selfAssessmentId })
        .andWhere('saFindingItem.deleted = :deleted', { deleted: false })
        .andWhere('fillAuditChecklist.id = :fillChecklistId', {
          fillChecklistId: latestFillChecklist.id,
        })
        .orderBy('saFindingItem.createdAt', 'DESC')
        .getMany();

      return saFindingItems;
    } catch (ex) {
      LoggerCommon.error('[SelfAssessmentRepository] getSAFindingItems error ', ex.message || ex);
      throw ex;
    }
  }
}
