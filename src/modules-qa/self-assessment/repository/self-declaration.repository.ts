import { Connection, EntityRepository, getRepository, In, Not } from 'typeorm';
import { SelfDeclaration } from '../entity/self-declaration.entity';
import { BaseError, LoggerCommon, TokenPayloadModel, TypeORMRepository } from 'svm-nest-lib-v3';
import {
  ListSelfDeclarationDto,
  ReAssignSelfDeclarationDto,
  ReOpenSelfDeclarationDto,
  TitleSelfDeclaration,
  UpdateSelfDeclarationDto,
  UpdateSelfDeclarationForListingDto,
} from '../dto';
import {
  EmailTypeEnum,
  FeatureEnum,
  ModulePathEnum,
  PushTypeEnum,
  SelfAssessmentStatusEnum,
  SelfAssessmentTypeEnum,
  SelfDeclarationCommentType,
  SelfDeclarationStatusEnum,
  SubFeatureEnum,
  WorkflowPermission,
} from '../../../commons/enums';
import { SelfDeclarationComment } from '../entity/self-declaration-comment.entity';
import { UserRepository } from '../../../modules/user/user.repository';
import { SelfDeclarationDocument } from '../entity/self-declaration-document.entity';
import { SelfDeclarationReference } from '../entity/self-declaration-reference.entity';
import { ReviewPrepComment } from '../entity/review-prep-comment.entity';
import { pick } from 'lodash';
import { SelfAssessment } from '../entity/self-assessment.entity';
import { SelfDeclarationHistory } from '../entity/self-declaration-history.entity';
import { ComplianceAnswer } from '../../../modules-qa/standard-master/entity/compliance-answer.entity';
import {
  INotificationEventModel,
  IUser,
} from '../../../micro-services/async/notification.producer';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import { UserAssignmentRepository } from '../../../modules/user-assignment/user-assignment.repository';
import { ModuleType } from '../../../modules/user-assignment/user-assignment.enum';
import APP_CONFIG from '../../../configs/app.config';
import { SelfDeclarationHistoryRepository } from './self-declaration-history.repository';
import { FillSAChecklistQuestion } from '../../../modules/audit-workspace/entities/fill-sa-checklist-question.entity';
import { FillAuditChecklist } from '../../../modules/audit-workspace/entities/fill-audit-checklist.entity';
import { SelfAssessmentAcknowledgeReviewRepository } from './self-assessment-acknowledge-review.repository';
import { MetaConfig } from '../../catalog/entity/meta-config.entity';
import { CatalogConst } from '../../catalog/catalog-key.const';
import { IncidentInvestigation } from '../../incident-investigation/entity/incident-investigation.entity';
import { ModuleConfigRepository } from '../../catalog/repository/module-config.repository';
import { LabelConfig } from '../../catalog/entity/label-config.entity';
import { ActionEnum, LanguageCodeEnum } from '../../../commons/enums/catalog.enum';
import { ModuleLabelConfig } from '../../catalog/entity/module-label-config.entity';
import { LabelConfigRepository } from '../../catalog/repository/label-config.repository';
import { ModuleLabelConfigRepository } from '../../catalog/repository/module-label-config.repository';
import { StandardModuleLabel } from '../../standard-master/entity/standard-module-label.entity';
import { StandardMasterRepository } from '../../standard-master/repository/standard-master.repository';
import { StandardModuleLabelRepository } from '../../standard-master/repository/standard-module-label.repository';

@EntityRepository(SelfDeclaration)
export class SelfDeclarationRepository extends TypeORMRepository<SelfDeclaration> {
  constructor(
    private readonly connection: Connection,
    private readonly selfDeclrationHistoryRepository: SelfDeclarationHistoryRepository,
  ) {
    super();
  }

  async _migrateSyncStandardModuleLabel() {
    const metaConfig = await this.connection
      .getRepository(MetaConfig)
      .createQueryBuilder('meta_config')
      .where('meta_config.key = :key', {
        key: CatalogConst.MIGRATE_SYNC_STANDARD_MODULE_LABEL,
      })
      .getOne();
    const version = '2024-01-22T16:20:00.000z';
    if (!metaConfig || version > metaConfig?.lastTimeSync) {
      return await this.connection.transaction(async (managerTrans) => {
        const defaultModule = await managerTrans
          .getCustomRepository(ModuleConfigRepository)
          .findOne({
            where: {
              key:
                FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT +
                '::' +
                SubFeatureEnum.SELF_ASSESSMENT,
              isDefault: true,
            },
          });
        const defaultLabels = await managerTrans
          .getCustomRepository(ModuleLabelConfigRepository)
          .find({
            moduleId: defaultModule.id,
            action: ActionEnum.EXPORT,
          });
        const labelIds = defaultLabels.map((item) => item.labelId);
        const labels = await managerTrans
          .getCustomRepository(LabelConfigRepository)
          .findByIds(labelIds);
        labels.forEach((value) => {
          value.userDefinedLabel = value.defaultLabel;
          value.isHide = false;
        });
        await managerTrans.getCustomRepository(LabelConfigRepository).save(labels);

        const standardMaster = await managerTrans
          .getCustomRepository(StandardMasterRepository)
          .find({
            deleted: false,
          });
        const standardMasterIds = standardMaster.map((item) => item.id);
        const standardModuleLabels = [];
        for (let i = 0; i < labels.length; i++) {
          for (let j = 0; j < standardMasterIds.length; j++) {
            standardModuleLabels.push({
              standardMasterId: standardMasterIds[j],
              moduleId: labels[i].moduleId,
              labelId: labels[i].id,
              action: ActionEnum.EXPORT,
              userDefinedLabel: labels[i].userDefinedLabel,
              isHide: labels[i].isHide,
            } as StandardModuleLabel);
          }
        }
        await managerTrans
          .getCustomRepository(StandardModuleLabelRepository)
          .save(standardModuleLabels);
        await managerTrans.save(MetaConfig, {
          key: CatalogConst.MIGRATE_SYNC_STANDARD_MODULE_LABEL,
          lastTimeSync: version,
        });
      });
    }
    return 1;
  }

  async updateSelfDeclaration(
    id: string,
    selfAssessmentId: string,
    body: UpdateSelfDeclarationDto,
    user: TokenPayloadModel,
    // workflowPermissions: string[],
  ) {
    try {
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      const getSelfDeclaration = await this.manager.findOne(SelfDeclaration, {
        where: { id },
        relations: ['selfAssessment'],
      });
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(user.id);
      // trim the comment to avoid extra spaces
      const prepareSelfDeclarationComment: SelfDeclarationComment[] = [];
      for (let i = 0; i < body.selfDeclarationComment?.length; i++) {
        prepareSelfDeclarationComment.push({
          comment: body.selfDeclarationComment[i].comment.trim(),
          type: body.selfDeclarationComment[i].type,
          createdUser: createdUser,
          selfDeclarationId: id,
        } as SelfDeclarationComment);
      }

      const prepareSelfDeclarationHistory: SelfDeclarationHistory[] = [];
      for (let i = 0; i < body.selfDeclarationComment?.length; i++) {
        prepareSelfDeclarationHistory.push({
          comment: body.selfDeclarationComment[i].comment.trim(),
          createdUser: createdUser,
          selfDeclarationId: id,
          complianceId: body.complianceId,
        } as SelfDeclarationHistory);
      }

      const prepareSelfDeclarationDocument: SelfDeclarationDocument[] = [];
      for (let i = 0; i < body.selfDeclarationDocument?.length; i++) {
        prepareSelfDeclarationDocument.push({
          ...body.selfDeclarationDocument[i],
          selfDeclarationId: id,
        } as SelfDeclarationDocument);
      }

      const prepareSelfDeclarationReference: SelfDeclarationReference[] = [];
      for (let i = 0; i < body.selfDeclarationReference?.length; i++) {
        prepareSelfDeclarationReference.push({
          ...body.selfDeclarationReference[i],
          selfDeclarationId: id,
        } as SelfDeclarationReference);
      }

      const prepareReviewPrepComment: ReviewPrepComment[] = [];
      for (let i = 0; i < body.reviewPrepComment?.length; i++) {
        prepareReviewPrepComment.push({
          ...body.reviewPrepComment[i],
          selfDeclarationId: id,
        } as ReviewPrepComment);
      }

      const prepareBody = pick(body, [
        'complianceId',
        'selfClose',
        'proposal',
        'targetCompletionDate',
        'operatingManagerCompletionDate',
        'attachments',
      ]);
      //Set default status for self declaration form until FE implement work flow permission
      // const formStatus: SelfDeclarationStatusEnum = SelfDeclarationStatusEnum.SUBMITTED;
      //#region temporary
      // if (
      //   getSelfDeclaration.status === SelfDeclarationStatusEnum.PENDING &&
      //   body.selfClose === false
      // ) {
      //   formStatus = SelfDeclarationStatusEnum.SUBMITTED;
      // } else if (
      //   getSelfDeclaration.status === SelfDeclarationStatusEnum.PENDING &&
      //   body.selfClose === true
      // ) {
      //   formStatus = SelfDeclarationStatusEnum.APPROVED;
      // } else if (getSelfDeclaration.status === SelfDeclarationStatusEnum.SUBMITTED) {
      //   formStatus = SelfDeclarationStatusEnum.APPROVED;
      // }
      //#endregion
      // const listReceiverNoti = [];
      // const performer = await this.manager
      //   .getCustomRepository(UserRepository)
      //   .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
      // if (
      //   getSelfDeclaration.status === SelfDeclarationStatusEnum.PENDING &&
      //   workflowPermissions.includes(WorkflowPermission.CREATOR) &&
      //   body.selfClose === false
      // ) {
      //   formStatus = SelfDeclarationStatusEnum.SUBMITTED;
      // } else if (
      //   getSelfDeclaration.status === SelfDeclarationStatusEnum.SUBMITTED &&
      //   workflowPermissions.includes(WorkflowPermission.REVIEWER)
      // ) {
      //   formStatus = SelfDeclarationStatusEnum.APPROVED;
      // } else if (
      //   getSelfDeclaration.status === SelfDeclarationStatusEnum.PENDING &&
      //   workflowPermissions.includes(WorkflowPermission.CREATOR) &&
      //   body.selfClose === true
      // ) {
      //   formStatus = SelfDeclarationStatusEnum.APPROVED;
      // }
      const prepareUpdateSelfDeclaration: any = {
        ...prepareBody,
        // status: formStatus,
      };
      // if (formStatus !== getSelfDeclaration.status) {
      //   if (
      //     formStatus === SelfDeclarationStatusEnum.SUBMITTED &&
      //     getSelfDeclaration.status === SelfDeclarationStatusEnum.PENDING
      //   ) {
      //     const listUserAssignment = await this.manager
      //       .getCustomRepository(UserAssignmentRepository)
      //       .listByModule(ModuleType.SELF_ASSESSMENT, getSelfDeclaration.selfAssessmentId);
      //     listReceiverNoti = listReceiverNoti.concat(
      //       listUserAssignment[WorkflowPermission.REVIEWER],
      //     );
      //     // listReceiverNoti = await this.manager
      //     //   .getCustomRepository(UserRepository)
      //     //   .listByIds([getSelfDeclaration.selfAssessment.createdUserId]);
      //   } else {
      //     listReceiverNoti = await this.manager
      //       .getCustomRepository(UserRepository)
      //       .listByIds([getSelfDeclaration.selfAssessment.createdUserId]);
      //   }
      //
      //   dataNoti.push({
      //     receivers: listReceiverNoti as IUser[],
      //     module: ModuleType.SELF_ASSESSMENT,
      //     recordId: getSelfDeclaration.id,
      //     recordRef: getSelfDeclaration.refId,
      //     type: PushTypeEnum.UPDATE_RECORD,
      //     currentStatus: formStatus,
      //     previousStatus: getSelfDeclaration.status,
      //     performer: performer,
      //     executedAt: new Date(),
      //     extendData: {
      //       selfAssessmentId: getSelfDeclaration.selfAssessmentId,
      //     },
      //   });
      //   for (const receiver of listReceiverNoti) {
      //     dataSendMail.push({
      //       receiver: receiver as IUserEmail,
      //       type: EmailTypeEnum.UPDATE_RECORD,
      //       templateKey: MailTemplate.CHANGE_RECORD_STATUS,
      //       subject: '[Notification] Change status in a record',
      //       data: {
      //         username: receiver.username,
      //         baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
      //         recordRef: getSelfDeclaration.refId,
      //         recordId: getSelfDeclaration.id,
      //         path: ModulePathEnum.SELF_ASSESSMENT + '/declaration',
      //         queryParam: `?selfAssessmentId=${getSelfDeclaration.selfAssessmentId}`,
      //         currentStatus: formStatus,
      //         previousStatus: getSelfDeclaration.status,
      //         performer: performer,
      //         executedAt: new Date(),
      //       },
      //     });
      //   }
      // }
      // check the previous comment is exist
      // remove findone methdod because that method not fetch latest record correctly
      const prevDeclarationComment = await getRepository(SelfDeclarationComment)
        .createQueryBuilder('selfDeclarationComment')
        .where(
          'selfDeclarationComment.selfDeclarationId = :selfDeclarationId AND selfDeclarationComment.comment = :comment',
          {
            selfDeclarationId: id,
            comment: body.selfDeclarationComment[0].comment.trim(),
          },
        )
        .orderBy('selfDeclarationComment.createdAt', 'DESC')
        .getOne();
      const prevDeclarationHistory = await getRepository(SelfDeclarationHistory)
        .createQueryBuilder('selfDeclarationHistory')
        .where(
          'selfDeclarationHistory.selfDeclarationId = :selfDeclarationId AND selfDeclarationHistory.comment = :comment',
          {
            selfDeclarationId: id,
            comment: body.selfDeclarationComment[0].comment.trim(),
          },
        )
        .orderBy('selfDeclarationHistory.createdAt', 'DESC')
        .getOne();

      if (body.selfDeclarationComment?.length > 0) {
        for (const item of body.selfDeclarationComment) {
          if (item.type === SelfDeclarationCommentType.INTERNAL) {
            prepareUpdateSelfDeclaration.newestInternalComment = item.comment;
          }
          if (item.type === SelfDeclarationCommentType.EXTERNAL) {
            prepareUpdateSelfDeclaration.newestExternalComment = item.comment;
          }
          if (item.type === SelfDeclarationCommentType.MANAGER) {
            prepareUpdateSelfDeclaration.newestOpManagerComment = item.comment;
          }
        }
      }

      await this.connection.transaction(async (manager) => {
        if (body.selfDeclarationComment?.length > 0) {
          // check if exist comment is there to check it is same comment or not
          if (prevDeclarationComment) {
            if (prevDeclarationComment.comment !== body.selfDeclarationComment[0].comment.trim()) {
              await manager.save(SelfDeclarationComment, prepareSelfDeclarationComment);
            }
          } else {
            await manager.save(SelfDeclarationComment, prepareSelfDeclarationComment);
          }
        }

        if (body.selfDeclarationComment?.length > 0) {
          // check if exist comment is there to check it is same comment or not
          if (prevDeclarationHistory) {
            if (prevDeclarationHistory.comment !== body.selfDeclarationComment[0].comment.trim()) {
              await manager.save(SelfDeclarationHistory, prepareSelfDeclarationHistory);
            } else if (prevDeclarationHistory.complianceId !== body.complianceId) {
              await manager.save(SelfDeclarationHistory, prepareSelfDeclarationHistory);
            }
          } else {
            await manager.save(SelfDeclarationHistory, prepareSelfDeclarationHistory);
          }
        }

        if (body.selfDeclarationDocument?.length > 0) {
          await manager.save(SelfDeclarationDocument, prepareSelfDeclarationDocument);
        }

        if (body.selfDeclarationReference?.length > 0) {
          await manager.save(SelfDeclarationReference, prepareSelfDeclarationReference);
        }

        if (body.reviewPrepComment?.length > 0) {
          await manager.save(ReviewPrepComment, prepareReviewPrepComment);
        }

        await manager.update(
          SelfDeclaration,
          {
            id: id,
            deleted: false,
          },
          { ...prepareUpdateSelfDeclaration, status: SelfDeclarationStatusEnum.SAVED },
        );

        // const foundSelfDeclarations = await manager.find(SelfDeclaration, {
        //   where: {
        //     selfAssessmentId: selfAssessmentId,
        //     id: Not(id),
        //   },
        // });
        // update self assessment status into "InProgess"
        const preparedUpdateSa = {
          updatedUserId: user.id,
          status: SelfAssessmentStatusEnum.INPROGRESS,
        };
        // if (
        //   foundSelfDeclarations.every((sd) => sd.status === SelfDeclarationStatusEnum.APPROVED) &&
        //   formStatus === SelfDeclarationStatusEnum.APPROVED
        // ) {
        //   Object.assign(preparedUpdateSa, { type: SelfAssessmentTypeEnum.OFFICIAL });
        // }

        // update self-assessment
        await manager.update(
          SelfAssessment,
          {
            id: selfAssessmentId,
            deleted: false,
          },
          preparedUpdateSa,
        );

        // insert self declaration history
        // const createdUser = await this.manager
        //   .getCustomRepository(UserRepository)
        //   ._getUserInfoForHistory(user.id);
        // const prepareInsertHistories = [];
        // if (body.selfClose === true && workflowPermissions.includes(WorkflowPermission.CREATOR)) {
        //   prepareInsertHistories.push({
        //     createdUser: createdUser,
        //     selfDeclarationId: id,
        //     status: SelfDeclarationStatusEnum.SUBMITTED,
        //     comment: undefined,
        //   });
        // }
        // const prepareInsertHistory = {
        //   createdUser: createdUser,
        //   selfDeclarationId: id,
        //   status:
        //     body.selfDeclarationHistory && body.selfDeclarationHistory.status
        //       ? body.selfDeclarationHistory.status
        //       : formStatus,
        //   comment:
        //     body.selfDeclarationHistory && body.selfDeclarationHistory.comment
        //       ? body.selfDeclarationHistory.comment
        //       : undefined,
        // } as SelfDeclarationHistory;
        // prepareInsertHistories.push(prepareInsertHistory);
        // await this.manager.insert(SelfDeclarationHistory, prepareInsertHistories);
      });
      return { dataNoti, dataSendMail };
    } catch (ex) {
      LoggerCommon.error(
        '[SelfDeclarationRepository] updateSelfDeclaration error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async reOpenSelfDeclaration(
    user: TokenPayloadModel,
    reOpenSelfDeclaration: ReOpenSelfDeclarationDto,
    selfAssessmentId: string,
  ) {
    try {
      //Clear data when reopen self declaration
      const prepareSelfDeclaration = {
        // updatedUserId: user.id,
        status: SelfDeclarationStatusEnum.PENDING,
        // selfClose: null,
        // complianceId: null,
        // newestInternalComment: null,
        // newestExternalComment: null,
        // targetCompletionDate: null,
      };
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      return await this.connection.transaction(async (manager) => {
        //Check user approved self declaration
        // const selfDeclarationApproveHistory = await manager
        //   .createQueryBuilder(SelfDeclarationHistory, 'selfDeclarationHistory')
        //   .where(`selfDeclarationHistory.selfDeclarationId IN (:...selfDeclarationIds)`, {
        //     selfDeclarationIds: reOpenSelfDeclaration.selfDeclarationIds,
        //   })
        //   .andWhere(
        //     `"selfDeclarationHistory"."createdUser"->>'id' = :createdUserId
        //   AND selfDeclarationHistory.status = :status
        //   AND selfDeclarationHistory.deleted = FALSE`,
        //     {
        //       createdUserId: user.id,
        //       status: SelfDeclarationStatusEnum.APPROVED,
        //     },
        //   )
        //   .select(['selfDeclarationHistory.id'])
        //   .getOne();

        // if (!selfDeclarationApproveHistory) {
        //   throw new BaseError({
        //     status: 400,
        //     message: 'selfDeclaration.USER_NOT_APPROVED_SELF_DECLARATION',
        //   });
        // }

        // const userCanUpdate = await this.checkUserCanUpdateRecord(user.id, selfAssessmentId);
        // if (!userCanUpdate) {
        //   throw new BaseError({
        //     status: 403,
        //     message: 'common.FORBIDDEN_RESOURCE',
        //   });
        // }

        const selfDeclarationFind = await manager.find(SelfDeclaration, {
          where: {
            id: In(reOpenSelfDeclaration.selfDeclarationIds),
          },
          relations: ['selfAssessment'],
        });
        const selfDeclarationUpdated = await manager.update(
          SelfDeclaration,
          {
            id: In(reOpenSelfDeclaration.selfDeclarationIds),
            deleted: false,
          },
          prepareSelfDeclaration,
        );
        // insert self declaration history
        // const createdUser = await manager
        //   .getCustomRepository(UserRepository)
        //   ._getUserInfoForHistory(user.id);
        // const prepareSelfDeclarationHistories = [];
        // reOpenSelfDeclaration.selfDeclarationIds.forEach((id) => {
        //   prepareSelfDeclarationHistories.push({
        //     status: SelfDeclarationStatusEnum.PENDING,
        //     selfDeclarationId: id,
        //     createdUser,
        //   } as SelfDeclarationHistory);
        // });
        // await manager.insert(SelfDeclarationHistory, prepareSelfDeclarationHistories);
        // list User Assignment
        // const listReceiverNoti = await this.manager
        //   .getCustomRepository(UserRepository)
        //   .listByIds([selfDeclarationFind[0].selfAssessment.createdUserId]);
        //
        // const performer = await manager
        //   .getCustomRepository(UserRepository)
        //   .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
        // for (const selfDeclarationId of reOpenSelfDeclaration.selfDeclarationIds) {
        //   dataNoti.push({
        //     receivers: listReceiverNoti as IUser[],
        //     module: ModuleType.SELF_ASSESSMENT,
        //     recordId: selfDeclarationId,
        //     recordRef: selfDeclarationFind.filter((item) => item.id === selfDeclarationId)[0].refId,
        //     type: PushTypeEnum.UPDATE_RECORD,
        //     performer: performer,
        //     currentStatus: SelfDeclarationStatusEnum.PENDING,
        //     previousStatus: selfDeclarationFind.filter((item) => item.id === selfDeclarationId)[0]
        //       .status,
        //     executedAt: new Date(),
        //     extendData: {
        //       selfAssessmentId: selfDeclarationFind[0].selfAssessmentId,
        //     },
        //   });
        //   for (const receiver of listReceiverNoti) {
        //     dataSendMail.push({
        //       receiver: receiver as IUserEmail,
        //       type: EmailTypeEnum.UPDATE_RECORD,
        //       templateKey: MailTemplate.CHANGE_RECORD_STATUS,
        //       subject: '[Notification] Change status in a record',
        //       data: {
        //         username: receiver.username,
        //         baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
        //         recordRef: selfDeclarationFind.filter((item) => item.id === selfDeclarationId)[0]
        //           .refId,
        //         recordId: selfDeclarationId,
        //         path: ModulePathEnum.SELF_ASSESSMENT + '/declaration',
        //         queryParam: `?selfAssessmentId=${selfDeclarationFind[0].selfAssessmentId}`,
        //         performer: performer,
        //         currentStatus: SelfDeclarationStatusEnum.PENDING,
        //         previousStatus: selfDeclarationFind.filter(
        //           (item) => item.id === selfDeclarationId,
        //         )[0].status,
        //         executedAt: new Date(),
        //       },
        //     });
        //   }
        // }

        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[SelfDeclarationRepository] reOpenSelfDeclaration error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async getListSelfDeclarationBySelfAssessmentId(
    selfAssessmentId: string,
    query: ListSelfDeclarationDto,
  ) {
    const queryBuilder = this.createQueryBuilder('selfDeclaration')
      .leftJoin('selfDeclaration.elementMaster', 'elementMaster')
      .leftJoin('selfDeclaration.compliance', 'compliance')
      .leftJoin('elementMaster.standardMaster', 'standardMaster')
      .leftJoin('standardMaster.complianceAnswers', 'complianceAnswers')
      .leftJoin('selfDeclaration.selfDeclarationHistories', 'selfDeclarationHistories')
      .leftJoin('selfDeclaration.selfAssessment', 'selfAssessment')
      .leftJoin('selfAssessment.userAssignments', 'userAssignments')
      .leftJoin('userAssignments.user', 'user')
      //.leftJoin('elementMaster.selfDeclarationComments', 'selfDeclarationComment')
      .where('selfDeclaration.selfAssessmentId = :selfAssessmentId', {
        selfAssessmentId: selfAssessmentId,
      })
      .select()
      .addSelect([
        'standardMaster.id',
        'standardMaster.code',
        'standardMaster.name',
        'standardMaster.levels',
        'elementMaster.id',
        'elementMaster.code',
        'elementMaster.name',
        'elementMaster.number',
        'elementMaster.questionNumber',
        'elementMaster.stage',
        'elementMaster.group',
        'elementMaster.elementStageQ',
        'elementMaster.keyPerformanceIndicator',
        'elementMaster.bestPracticeGuidance',
        'elementMaster.otherInfoGuidance',
        'elementMaster.aimPrinciple',
        'elementMaster.standardMasterId',
        'elementMaster.createdAt',
        // 'selfDeclarationComment.type',
        // 'selfDeclarationComment.comment',
        'compliance.answer',
        'compliance.compliance',
        'compliance.colour',
        'selfAssessment.sNo',
        'selfAssessment.type',
        'selfAssessment.status',
        'selfDeclarationHistories.id',
        'selfDeclarationHistories.comment',
        'selfDeclarationHistories.selfDeclarationId',
        'selfDeclarationHistories.complianceId',
        'selfDeclarationHistories.createdUser',
        'selfDeclarationHistories.createdAt',
        'userAssignments.id',
        'userAssignments.permission',
        'user.id',
        'user.username',
        'user.jobTitle',
        'complianceAnswers.id',
        'complianceAnswers.answer',
        'complianceAnswers.compliance',
        'complianceAnswers.colour',
      ]);
    // .orderBy('selfDeclarationHistories.createdAt', 'DESC');

    const selfDeclaration = await this.list(
      {
        page: query.page,
        limit: query.pageSize,
      },
      {
        queryBuilder,
        sort:
          query.sort ||
          'elementMaster.createdAt:1;elementMaster.code:1;elementMaster.questionNumber:1',
      },
    );

    // Notifications will be filtered by notification status such as Pending, Saved
    if (query?.selfDeclarationStatus) {
      selfDeclaration.data = selfDeclaration.data.filter(
        (item) => item.status === query.selfDeclarationStatus,
      );
    }

    return selfDeclaration;
  }

  async getCountSelfDeclarationBySelfAssessmentId(selfAssessmentId: string) {
    const listSelfDeclaration = await this.createQueryBuilder('selfDeclaration')
      .where(
        'selfDeclaration.selfAssessmentId = :selfAssessmentId and selfDeclaration.deleted = false',
        {
          selfAssessmentId,
        },
      )
      .select()
      .getMany();
    let countPending = 0;
    for (const selfDeclaration of listSelfDeclaration) {
      // if both compliance and comment was not filled then count as pending
      if (!selfDeclaration.complianceId || !selfDeclaration.newestExternalComment) {
        countPending++;
      }
    }
    return {
      countPending,
      countSaved: listSelfDeclaration.length - countPending,
    };
  }

  /**
   * Retrieves self-declaration data for matrix display with optimized performance.
   *
   * This method always returns both self-declarations and audit declarations:
   * - selfDeclarations: Always populated with user-filled assessment responses
   * - auditDeclarations: Populated with auditor responses if planning request has isSA=true,
   *   otherwise returns empty array
   *
   * Performance optimizations:
   * - Fetches shared data (levels, compliances) once and reuses across queries
   * - Uses parallel execution for independent data fetching
   * - Minimizes database calls by avoiding redundant queries
   *
   * @param selfAssessmentId - The ID of the self-assessment
   * @param query - Query parameters including filters
   * @returns Matrix response containing stages, both declaration datasets, and compliance data
   */
  async listSelfDeclarationForMatrix(selfAssessmentId: string, query: ListSelfDeclarationDto) {
    // Step 1: Get shared data (levels and standard master info) that will be used by both datasets
    // This avoids duplicate queries and improves performance
    const levels = await this.manager
      .createQueryBuilder(SelfAssessment, 'selfAssessment')
      .leftJoin('selfAssessment.standardMaster', 'standardMaster')
      .where('selfAssessment.id = :selfAssessmentId', { selfAssessmentId })
      .select([
        'selfAssessment.id',
        'selfAssessment.type', // Used for determining review logic
        'standardMaster.id', // Used for compliance queries
        'standardMaster.levels', // Used for stages in response
      ])
      .getOne();

    // Step 2: Get compliance answers for the standard master
    // These are the available compliance options
    const compliances = await this._getCompliancesUsingStandardMasterId(levels?.standardMaster?.id);

    // Step 3: Always fetch self-declarations and conditionally fetch audit declarations
    // This parallel execution improves response time significantly
    const [selfDeclarations, auditDeclarations] = await Promise.all([
      // Always get self-declarations (user-filled declarations)
      this._getOptimizedSelfDeclarations(selfAssessmentId, query, levels),
      // Conditionally get audit declarations based on planning request isSA flag
      this._getConditionalAuditDeclarations(selfAssessmentId, query, levels),
    ]);

    // Step 4: Combine both datasets in the response structure
    const matrixResponse = {
      stages: levels?.standardMaster.levels || null, // Matrix stages/levels
      selfDeclarations: selfDeclarations, // User self-declarations
      auditDeclarations: auditDeclarations, // Auditor declarations (empty if isSA=false)
      compliances: compliances, // Available compliance options
    };

    return matrixResponse;
  }

  /**
   * Fetches compliance answer options for a given standard master.
   *
   * Compliance answers are the predefined options that users can select when
   * filling out self-declarations.
   *
   * Performance optimization: Uses direct standardMasterId instead of making
   * additional queries to fetch it from self-assessment.
   *
   * @param standardMasterId - The ID of the standard master
   * @returns Array of compliance answer options with their properties
   */
  private async _getCompliancesUsingStandardMasterId(standardMasterId: string) {
    // Early return if no standardMasterId provided
    if (!standardMasterId) {
      return [];
    }

    // Fetch all compliance answers for the given standard master
    return this.manager
      .createQueryBuilder(ComplianceAnswer, 'complianceAnswer')
      .where('complianceAnswer.standardMasterId = :standardMasterId', {
        standardMasterId,
      })
      .select([
        'complianceAnswer.id', // Unique identifier
        'complianceAnswer.answer', // Display text (e.g., "Compliant")
        'complianceAnswer.compliance', // Numeric compliance value
        'complianceAnswer.colour', // Color code for UI display
        'complianceAnswer.createdAt', // For consistent ordering
      ])
      .orderBy('complianceAnswer.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Fetches and optimizes self-declaration data for matrix display.
   *
   * Self-declarations are the user-filled assessment responses for each element
   * in the standard master. This method optimizes the query and sorting for
   * better performance.
   *
   * @param selfAssessmentId - The ID of the self-assessment
   * @param query - Query parameters including filters
   * @param levels - Pre-fetched levels data for optimization
   * @returns Sorted and filtered array of self-declarations
   */
  private async _getOptimizedSelfDeclarations(
    selfAssessmentId: string,
    query: ListSelfDeclarationDto,
    levels: any,
  ) {
    // Fetch self-declarations with related element master and compliance data
    const selfDeclarations = await this.createQueryBuilder('selfDeclaration')
      .leftJoin('selfDeclaration.elementMaster', 'elementMaster')
      .leftJoin('selfDeclaration.compliance', 'complianceAnswer')
      .where('selfDeclaration.selfAssessmentId = :selfAssessmentId', {
        selfAssessmentId,
      })
      .select(['selfDeclaration.id', 'selfDeclaration.status', 'selfDeclaration.selfClose'])
      .addSelect([
        'elementMaster.id',
        'elementMaster.code', // Used for sorting
        'elementMaster.name',
        'elementMaster.number',
        'elementMaster.questionNumber',
        'elementMaster.stage',
        'elementMaster.elementStageQ',
        'elementMaster.createdAt',
        'complianceAnswer.id',
        'complianceAnswer.compliance', // User's selected compliance
        'complianceAnswer.colour', // Color for UI display
      ])
      .orderBy('elementMaster.code', 'ASC')
      .getMany();

    // Apply optimized sorting algorithm that handles element codes properly
    // This avoids database calls in loops and uses efficient in-memory sorting
    let sortedSelfDeclarations = await this._optimizedSortElementMaster(selfDeclarations, levels);

    // Apply status filter if specified in query
    if (query?.selfDeclarationStatus) {
      sortedSelfDeclarations = sortedSelfDeclarations.filter(
        (item) => item.status === query.selfDeclarationStatus,
      );
    }

    return sortedSelfDeclarations;
  }

  /**
   * Conditionally fetches audit declarations based on planning request isSA flag.
   *
   * This method checks if the planning request has isSA=true. If true, it fetches
   * audit declarations from the latest audit. If false, it returns an empty array.
   *
   * @param selfAssessmentId - The ID of the self-assessment
   * @param query - Query parameters including filters
   * @param levels - Pre-fetched levels data for optimization
   * @returns Audit declarations array (populated if isSA=true, empty if isSA=false)
   */
  private async _getConditionalAuditDeclarations(
    selfAssessmentId: string,
    query: ListSelfDeclarationDto,
    levels: any,
  ) {
    try {
      // Step 1: Check if there's any audit checklist with isSA=true for this self-assessment
      const hasValidAudit = await this.manager
        .getRepository(FillAuditChecklist)
        .createQueryBuilder('fillAuditChecklist')
        .leftJoin('fillAuditChecklist.auditWorkspace', 'auditWorkspace')
        .leftJoin('auditWorkspace.planningRequest', 'planningRequest')
        .where('fillAuditChecklist.selfAssessmentId = :selfAssessmentId', { selfAssessmentId })
        .andWhere('planningRequest.isSA = :isSA', { isSA: true })
        .andWhere('fillAuditChecklist.deleted = :deleted', { deleted: false })
        .select(['fillAuditChecklist.id'])
        .limit(1)
        .getOne();

      // Step 2: If no valid audit found (isSA=false), return empty array
      if (!hasValidAudit) {
        return [];
      }

      // Step 3: If valid audit found (isSA=true), fetch audit declarations
      return this._getOptimizedAuditDeclarations(selfAssessmentId, query, levels);
    } catch (error) {
      // Return empty array on any error to prevent breaking the main response
      return [];
    }
  }

  /**
   * Fetches and optimizes audit declaration data from the latest audit.
   *
   * Audit declarations are the auditor-filled responses during self-assessment
   * audits. This method finds the latest audit checklist and retrieves the
   * auditor's compliance answers for comparison with self-declarations.
   *
   * @param selfAssessmentId - The ID of the self-assessment
   * @param query - Query parameters including filters
   * @param levels - Pre-fetched levels data for optimization
   * @returns Sorted and filtered array of audit declarations, or empty array if no audit found
   */
  private async _getOptimizedAuditDeclarations(
    selfAssessmentId: string,
    query: ListSelfDeclarationDto,
    levels: any,
  ) {
    try {
      // Step 1: Find the latest audit checklist for this self-assessment
      // Only consider audits where isSA = true (self-assessment audits)
      const latestAuditChecklist = await this.manager
        .getRepository(FillAuditChecklist)
        .createQueryBuilder('fillAuditChecklist')
        .leftJoin('fillAuditChecklist.auditWorkspace', 'auditWorkspace')
        .leftJoin('auditWorkspace.planningRequest', 'planningRequest')
        .where('fillAuditChecklist.selfAssessmentId = :selfAssessmentId', { selfAssessmentId })
        .andWhere('planningRequest.isSA = :isSA', { isSA: true })
        .andWhere('fillAuditChecklist.deleted = :deleted', { deleted: false })
        .select(['fillAuditChecklist.id'])
        .orderBy('fillAuditChecklist.createdAt', 'DESC')
        .limit(1)
        .getOne();

      // Return empty array if no audit checklist found
      if (!latestAuditChecklist) {
        return [];
      }

      // Step 2: Fetch audit declarations (auditor's responses) for the latest audit
      const auditDeclarations = await this.manager
        .createQueryBuilder('FillSAChecklistQuestion', 'fillSAChecklistQuestion')
        .leftJoin('fillSAChecklistQuestion.SAQuestions', 'elementMaster')
        .leftJoin('fillSAChecklistQuestion.auditorComplianceAnswer', 'complianceAnswer')
        .where('fillSAChecklistQuestion.fillAuditChecklistId = :fillAuditChecklistId', {
          fillAuditChecklistId: latestAuditChecklist.id,
        })
        .andWhere('fillSAChecklistQuestion.deleted = :deleted', { deleted: false })
        .select([
          'fillSAChecklistQuestion.id',
          'fillSAChecklistQuestion.isFinding', // Whether auditor found an issue
          'fillSAChecklistQuestion.remarks', // Auditor's comments
        ])
        .addSelect([
          'elementMaster.id',
          'elementMaster.code', // Used for sorting
          'elementMaster.name',
          'elementMaster.number',
          'elementMaster.questionNumber',
          'elementMaster.stage',
          'elementMaster.elementStageQ',
          'elementMaster.createdAt',
          'complianceAnswer.id',
          'complianceAnswer.compliance', // Auditor's compliance assessment
          'complianceAnswer.colour', // Color for UI display
        ])
        .orderBy('elementMaster.code', 'ASC')
        .getMany();

      // Step 3: Transform audit data to match self-declaration structure
      // This ensures consistent data format for the frontend
      const transformedAuditDeclarations = auditDeclarations.map(
        (auditDecl: FillSAChecklistQuestion) => ({
          id: auditDecl.id,
          status: auditDecl.isFinding ? 'Finding' : 'Completed', // Convert boolean to status
          selfClose: false, // Audits are never self-closed
          elementMaster: auditDecl.SAQuestions, // Element details
          compliance: auditDecl.auditorComplianceAnswer, // Auditor's compliance choice
        }),
      );

      // Step 4: Apply optimized sorting algorithm
      let sortedAuditDeclarations = await this._optimizedSortElementMaster(
        transformedAuditDeclarations,
        levels,
      );

      // Step 5: Apply status filter if specified in query
      if (query?.selfDeclarationStatus) {
        sortedAuditDeclarations = sortedAuditDeclarations.filter(
          (item) => item.status === query.selfDeclarationStatus,
        );
      }

      return sortedAuditDeclarations;
    } catch (error) {
      // Return empty array on any error to prevent breaking the main response
      return [];
    }
  }

  /**
   * Optimized sorting algorithm for element master declarations.
   *
   * This method provides significant performance improvements over the original
   * sorting logic by:
   * 1. Batch fetching review data instead of individual queries per element
   * 2. Using Map for O(1) lookup instead of O(n) array searches
   * 3. Implementing efficient element code comparison algorithm
   *
   * The sorting prioritizes by:
   * 1. Element code length (shorter codes first)
   * 2. Alphanumeric sorting within same length groups
   *
   * @param declarations - Array of declarations to sort
   * @param levels - Self-assessment levels data containing type and ID
   * @returns Sorted array of declarations with priority and review flags
   */
  private async _optimizedSortElementMaster(declarations: any[], levels: any) {
    // Step 1: Batch fetch review data for 'Official' type assessments
    // This replaces individual database queries with a single batch query
    const reviewDataMap = new Map();

    if (levels?.type === 'Official') {
      // Fetch all review acknowledgments for this self-assessment in one query
      const reviewData = await this.connection
        .getCustomRepository(SelfAssessmentAcknowledgeReviewRepository)
        .createQueryBuilder('selfAssessmentAcknowledgeReview')
        .where('selfAssessmentAcknowledgeReview.selfAssessmentId = :selfAssessmentId', {
          selfAssessmentId: levels.id,
        })
        .select([
          'selfAssessmentAcknowledgeReview.code',
          'selfAssessmentAcknowledgeReview.stage',
          'selfAssessmentAcknowledgeReview.questionNumber',
        ])
        .getMany();

      // Create a Map for O(1) lookup performance instead of O(n) array searches
      reviewData.forEach((review) => {
        const key = `${review.code}-${review.stage}-${review.questionNumber}`;
        reviewDataMap.set(key, true);
      });
    }

    // Step 2: Process declarations and add sorting metadata
    const declarationsWithPriority = declarations.map((declaration) => {
      const elementMaster = declaration.elementMaster;
      const lengthElementCode = elementMaster.code.length;

      // Determine if this element has been reviewed (for Official assessments)
      if (levels?.type === 'Official') {
        const key = `${elementMaster.code}-${elementMaster.stage}-${elementMaster.questionNumber}`;
        elementMaster.reviewedElement = reviewDataMap.has(key);
      } else {
        elementMaster.reviewedElement = false;
      }

      // Add priority based on element code length for efficient sorting
      return {
        ...declaration,
        priority: lengthElementCode,
      };
    });

    // Step 3: Apply optimized sorting algorithm
    declarationsWithPriority.sort((a, b) => {
      // Primary sort: by element code length (priority)
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }

      // Secondary sort: by element code using specialized comparison
      return this._compareElementCodes(a.elementMaster.code, b.elementMaster.code);
    });

    return declarationsWithPriority;
  }

  /**
   * Specialized element code comparison algorithm.
   *
   * This method handles complex element codes that may contain:
   * - Alphanumeric characters (A1, B2, etc.)
   * - Special characters and separators
   * - Mixed numeric and text sections
   *
   * The algorithm:
   * 1. Splits codes into sections using special character delimiters
   * 2. Compares numeric sections numerically (not lexicographically)
   * 3. Compares text sections using locale-aware comparison
   *
   * @param codeA - First element code to compare
   * @param codeB - Second element code to compare
   * @returns Negative if codeA < codeB, positive if codeA > codeB, 0 if equal
   */
  private _compareElementCodes(codeA: string, codeB: string): number {
    // Split codes into sections using special character delimiters
    const sectionsA = codeA.split(/[^a-zA-Z0-9\-_\.,;!?:|\\(){}<>/\*\s]/);
    const sectionsB = codeB.split(/[^a-zA-Z0-9\-_\.,;!?:|\\(){}<>/\*\s]/);

    const maxLength = Math.max(sectionsA.length, sectionsB.length);

    // Compare each section of the element codes
    for (let i = 0; i < maxLength; i++) {
      const sectionA = sectionsA[i] || '';
      const sectionB = sectionsB[i] || '';

      // Handle numeric sections: compare as numbers, not strings
      const numA = parseInt(sectionA);
      const numB = parseInt(sectionB);

      if (!isNaN(numA) && !isNaN(numB)) {
        if (numA !== numB) {
          return numA - numB;
        }
      } else {
        // Handle text sections: use locale-aware comparison with numeric sensitivity
        const comparison = sectionA.localeCompare(sectionB, undefined, {
          numeric: true,
          sensitivity: 'base',
        });
        if (comparison !== 0) {
          return comparison;
        }
      }
    }

    return 0;
  }

  async getDetailSelfDeclaration(selfDeclarationId: string, type?: string) {
    if (type === 'audit') {
      // Return FillSAChecklistQuestion audit data
      const auditData = await this.connection
        .getRepository(FillSAChecklistQuestion)
        .createQueryBuilder('fillSAQuestion')
        .leftJoinAndSelect('fillSAQuestion.fillAuditChecklist', 'fillAuditChecklist')
        .leftJoinAndSelect('fillSAQuestion.SAQuestions', 'elementMaster')
        .leftJoinAndSelect('fillSAQuestion.SAFindingItem', 'saFindingItem')
        .leftJoinAndSelect(
          'fillSAQuestion.selfAssessmentComplianceAnswer',
          'selfAssessmentCompliance',
        )
        .leftJoinAndSelect('fillSAQuestion.auditorComplianceAnswer', 'auditorCompliance')
        .leftJoinAndSelect('fillSAQuestion.createdUser', 'createdUser')
        .leftJoinAndSelect('fillSAQuestion.updatedUser', 'updatedUser')
        .leftJoin('createdUser.company', 'createdUserCompany')
        .leftJoin('createdUser.divisions', 'createdUserDivisions')
        .leftJoin('updatedUser.company', 'updatedUserCompany')
        .leftJoin('updatedUser.divisions', 'updatedUserDivisions')
        .where('fillSAQuestion.selfDeclarationId = :selfDeclarationId', {
          selfDeclarationId,
        })
        .addSelect([
          'createdUserCompany.name',
          'createdUserCompany.code',
          'createdUserDivisions.name',
          'createdUserDivisions.code',
          'updatedUserCompany.name',
          'updatedUserCompany.code',
          'updatedUserDivisions.name',
          'updatedUserDivisions.code',
        ])
        .getOne();

      if (auditData) {
        return auditData;
      } else {
        throw new BaseError({ status: 404, message: 'fillSAChecklistQuestion.NOT_FOUND' });
      }
    } else if (type === 'self-assessment') {
      // Default behavior - return self-assessment data
      const selfDeclaration = await this.getOneQB(
        this.createQueryBuilder('selfDeclaration')
          .leftJoin('selfDeclaration.elementMaster', 'elementMaster')
          .leftJoin('selfDeclaration.compliance', 'compliance')
          .leftJoin('selfDeclaration.selfDeclarationHistories', 'selfDeclarationHistories')
          .leftJoin('selfDeclaration.selfAssessment', 'selfAssessment')
          .leftJoin('selfAssessment.userAssignments', 'userAssignments')
          .leftJoin('userAssignments.user', 'user')
          .leftJoin('user.company', 'userCompany')
          .leftJoin('user.divisions', 'userDivisions')
          .leftJoin('selfDeclaration.selfDeclarationComments', 'selfDeclarationComments')
          .leftJoin('selfDeclaration.selfDeclarationDocuments', 'selfDeclarationDocuments')
          .leftJoin('selfDeclaration.selfDeclarationReferences', 'selfDeclarationReferences')
          .leftJoin('selfDeclaration.reviewPrepComments', 'reviewPrepComments')
          .where('selfDeclaration.id = :selfDeclarationId', {
            selfDeclarationId,
          })
          .select()
          .addSelect([
            'selfDeclarationDocuments.id',
            'selfDeclarationDocuments.documentTitle',
            'selfDeclarationReferences.id',
            'selfDeclarationReferences.referenceModule',
            'selfDeclarationReferences.attachments',
            'reviewPrepComments.id',
            'reviewPrepComments.topic',
            'reviewPrepComments.description',
            'reviewPrepComments.attachments',
            'elementMaster.id',
            'elementMaster.code',
            'elementMaster.name',
            'elementMaster.number',
            'elementMaster.questionNumber',
            'elementMaster.stage',
            'elementMaster.elementStageQ',
            'elementMaster.keyPerformanceIndicator',
            'elementMaster.bestPracticeGuidance',
            'elementMaster.otherInfoGuidance',
            'elementMaster.aimPrinciple',
            'elementMaster.standardMasterId',
            'elementMaster.group',
            'selfDeclarationComments.id',
            'selfDeclarationComments.createdAt',
            'selfDeclarationComments.createdUser',
            'selfDeclarationComments.type',
            'selfDeclarationComments.complianceId',
            'selfDeclarationComments.comment',
            'compliance.id',
            'compliance.answer',
            'compliance.compliance',
            'compliance.colour',
            'selfAssessment.sNo',
            'selfAssessment.type',
            'selfAssessment.status',
            'selfAssessment.createdUserId',
            'selfDeclarationHistories.id',
            'selfDeclarationHistories.comment',
            'selfDeclarationHistories.selfDeclarationId',
            'selfDeclarationHistories.complianceId',
            'selfDeclarationHistories.createdUser',
            'selfDeclarationHistories.createdAt',
            'userAssignments.id',
            'userAssignments.permission',
            'user.id',
            'user.username',
            'user.jobTitle',
            'userCompany.name',
            'userCompany.code',
            'userDivisions.name',
            'userDivisions.code',
          ])
          .orderBy('"selfDeclarationHistories"."createdAt"', 'DESC'),
      );

      if (selfDeclaration) {
        // const workFlow = [];
        // const workFlowPending = await this.createWorkFlowFromHistorySelfDeclaration(
        //   SelfDeclarationStatusEnum.PENDING,
        //   selfDeclaration.selfDeclarationHistories,
        // );
        // if (workFlowPending) {
        //   workFlow.push({
        //     createdAt: workFlowPending.createdAt,
        //     createdUser: workFlowPending.createdUser,
        //     status: SelfDeclarationStatusEnum.PENDING,
        //   });
        //   const workFlowSubmitted = await this.createWorkFlowFromHistorySelfDeclaration(
        //     SelfDeclarationStatusEnum.SUBMITTED,
        //     selfDeclaration.selfDeclarationHistories,
        //   );
        //   if (workFlowSubmitted && workFlowSubmitted.createdAt > workFlowPending.createdAt) {
        //     workFlow.push({
        //       createdAt: workFlowSubmitted.createdAt,
        //       createdUser: workFlowSubmitted.createdUser,
        //       status: SelfDeclarationStatusEnum.SUBMITTED,
        //     });
        //     const workFlowApproved = await this.createWorkFlowFromHistorySelfDeclaration(
        //       SelfDeclarationStatusEnum.APPROVED,
        //       selfDeclaration.selfDeclarationHistories,
        //     );
        //     if (workFlowApproved && workFlowApproved.createdAt > workFlowSubmitted.createdAt) {
        //       workFlow.push({
        //         createdAt: workFlowApproved.createdAt,
        //         createdUser: workFlowApproved.createdUser,
        //         status: SelfDeclarationStatusEnum.APPROVED,
        //       });
        //     }
        //     const workFlowReAssign = await this.createWorkFlowFromHistorySelfDeclaration(
        //       SelfDeclarationStatusEnum.REASSIGN,
        //       selfDeclaration.selfDeclarationHistories,
        //     );
        //     if (workFlowReAssign && workFlowReAssign.createdAt > workFlowSubmitted.createdAt) {
        //       workFlow.push({
        //         createdAt: workFlowReAssign.createdAt,
        //         createdUser: workFlowReAssign.createdUser,
        //         status: SelfDeclarationStatusEnum.REASSIGN,
        //       });
        //     }
        //   }
        // }

        // return Object.assign({ ...selfDeclaration }, { workFlow: workFlow });
        // add complianceAnswer into the selfDeclarationHistories
        for (const declaration of selfDeclaration.selfDeclarationHistories) {
          const compliance = await getRepository(ComplianceAnswer)
            .createQueryBuilder('complianceAnswer')
            .where('complianceAnswer.id = :id', {
              id: declaration?.complianceId,
            })
            .getOne();
          Object.assign(declaration, { complianceAnswer: compliance?.answer || null });
        }
        return selfDeclaration;
      } else {
        throw new BaseError({ status: 404, message: 'selfDeclaration.NOT_FOUND' });
      }
    } else {
      throw new BaseError({ status: 404, message: 'selfDeclaration.NOT_FOUND' });
    }
  }
  async reAssignSelfDeclaration(user: TokenPayloadModel, body: ReAssignSelfDeclarationDto) {
    try {
      const prepareSelfDeclaration = {
        // updatedUserId: user.id,
        status: SelfDeclarationStatusEnum.REASSIGN,
      };
      const dataNoti: INotificationEventModel[] = [];
      const dataSendMail: IEmailEventModel[] = [];
      return await this.connection.transaction(async (manager) => {
        const selfDeclarationFind = await manager.findOne(SelfDeclaration, {
          where: {
            id: body.id,
            deleted: false,
          },
          relations: ['selfAssessment'],
        });
        if (!selfDeclarationFind) {
          throw new BaseError({ status: 404, message: 'selfDeclaration.NOT_FOUND' });
        }
        await manager.update(
          SelfDeclaration,
          {
            id: body.id,
            deleted: false,
          },
          prepareSelfDeclaration,
        );
        // insert self declaration history
        // const createdUser = await this.manager
        //   .getCustomRepository(UserRepository)
        //   ._getUserInfoForHistory(user.id);
        // const prepareInsertHistory = {
        //   createdUser: createdUser,
        //   selfDeclarationId: body.id,
        //   comment: body.comment,
        //   status: SelfDeclarationStatusEnum.REASSIGN,
        // } as SelfDeclarationHistory;
        // await manager.insert(SelfDeclarationHistory, prepareInsertHistory);
        // if (selfDeclarationFind.status === SelfDeclarationStatusEnum.SUBMITTED) {
        //   const listReceiverNoti = await manager
        //     .getCustomRepository(UserRepository)
        //     .listByIds([selfDeclarationFind.selfAssessment.createdUserId]);
        //   const performer = await manager
        //     .getCustomRepository(UserRepository)
        //     .getUserDetailAndSelect(user.id, ['id', 'username', 'jobTitle']);
        //   dataNoti.push({
        //     receivers: listReceiverNoti as IUser[],
        //     module: ModuleType.SELF_ASSESSMENT,
        //     recordId: selfDeclarationFind.selfAssessmentId,
        //     recordRef: selfDeclarationFind.selfAssessment.sNo,
        //     type: PushTypeEnum.UPDATE_RECORD,
        //     currentStatus: SelfDeclarationStatusEnum.REASSIGN,
        //     previousStatus: SelfDeclarationStatusEnum.SUBMITTED,
        //     performer: performer,
        //     executedAt: new Date(),
        //   });
        //   for (const receiver of listReceiverNoti) {
        //     dataSendMail.push({
        //       receiver: receiver as IUserEmail,
        //       type: EmailTypeEnum.UPDATE_RECORD,
        //       templateKey: MailTemplate.CHANGE_RECORD_STATUS,
        //       subject: '[Notification] Change status in a record',
        //       data: {
        //         username: receiver.username,
        //         baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
        //         recordRef: selfDeclarationFind.refId,
        //         recordId: selfDeclarationFind.id,
        //         path: ModulePathEnum.SELF_ASSESSMENT + '/declaration',
        //         queryParam: `?selfAssessmentId=${selfDeclarationFind.selfAssessmentId}`,
        //         currentStatus: SelfDeclarationStatusEnum.REASSIGN,
        //         previousStatus: SelfDeclarationStatusEnum.SUBMITTED,
        //         performer: performer,
        //         executedAt: new Date(),
        //       },
        //     });
        //   }
        // }
        return { dataNoti, dataSendMail };
      });
    } catch (ex) {
      LoggerCommon.error(
        '[SelfDeclarationRepository] reassignSelfDeclaration error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
  // async createWorkFlowFromHistorySelfDeclaration(
  //   status: string,
  //   listHistory: SelfDeclarationHistory[],
  // ) {
  //   let result = undefined;
  //   for (const item of listHistory) {
  //     if (item.status === status) {
  //       result = item;
  //       break;
  //     }
  //   }
  //   return result;
  // }

  async sortElementMasterInSelfDeclarations(selfDeclarations: any, levels: any) {
    // assign priority follow length code
    const selfDeclarationsHasPriority = [];
    for (let i = 0; i < selfDeclarations.length; i++) {
      const lengthElementCode = selfDeclarations[i].elementMaster.code.length;

      // add reviewElement key for known elements was reviewed get the elementMaster
      // check the self assessment status was 'Official'
      if (levels.type === 'Official') {
        // get review data using the elementMaster data
        const getReview = await this.connection
          .getCustomRepository(SelfAssessmentAcknowledgeReviewRepository)
          .createQueryBuilder('selfAssessmentAcknowledgeReview')
          .select()
          .where(
            'selfAssessmentAcknowledgeReview.code = :code AND selfAssessmentAcknowledgeReview.stage = :stage AND selfAssessmentAcknowledgeReview.questionNumber = :questionNumber AND selfAssessmentAcknowledgeReview.selfAssessmentId = :selfAssessmentId',
            {
              code: selfDeclarations[i].elementMaster.code,
              stage: selfDeclarations[i].elementMaster.stage,
              questionNumber: selfDeclarations[i].elementMaster.questionNumber,
              selfAssessmentId: levels.id,
            },
          )
          .getOne();
        if (getReview && getReview !== undefined) {
          // if get the review data then put the reviewElement key
          Object.assign(selfDeclarations[i].elementMaster, { reviewedElement: true });
        } else {
          // else put false
          Object.assign(selfDeclarations[i].elementMaster, { reviewedElement: false });
        }
      } else {
        // else put false
        Object.assign(selfDeclarations[i].elementMaster, { reviewedElement: false });
      }

      selfDeclarationsHasPriority.push(
        Object.assign(selfDeclarations[i], { priority: lengthElementCode }),
      );
    }

    // sort by priority
    const selfDeclarationsSortedByPriority = selfDeclarationsHasPriority.sort((a, b) => {
      return a.priority - b.priority;
    });

    // const oneCharElementCodes = [];
    // const twoCharElementCodes = [];
    // const threeCharElementCodes = [];
    // const forCharElementCodes = [];

    // for (let i = 0; i < selfDeclarationsSortedByPriority.length; i++) {
    //   if (selfDeclarationsSortedByPriority[i].priority === 1) {
    //     oneCharElementCodes.push(selfDeclarationsSortedByPriority[i]);
    //   }
    //   if (selfDeclarationsSortedByPriority[i].priority === 2) {
    //     twoCharElementCodes.push(selfDeclarationsSortedByPriority[i]);
    //   }

    //   if (selfDeclarationsSortedByPriority[i].priority === 3) {
    //     threeCharElementCodes.push(selfDeclarationsSortedByPriority[i]);
    //   }

    //   if (selfDeclarationsSortedByPriority[i].priority === 4) {
    //     forCharElementCodes.push(selfDeclarationsSortedByPriority[i]);
    //   }
    // }

    // oneCharElementCodes.sort(function (a, b) {
    //   if (a.elementMaster.code < b.elementMaster.code) {
    //     return -1;
    //   }
    //   if (a.elementMaster.code > b.elementMaster.code) {
    //     return 1;
    //   }
    //   return 0;
    // });

    // twoCharElementCodes.sort(function (a, b) {
    //   if (a.elementMaster.code < b.elementMaster.code) {
    //     return -1;
    //   }
    //   if (a.elementMaster.code > b.elementMaster.code) {
    //     return 1;
    //   }
    //   return 0;
    // });

    // threeCharElementCodes.sort(function (a, b) {
    //   if (a.elementMaster.code < b.elementMaster.code) {
    //     return -1;
    //   }
    //   if (a.elementMaster.code > b.elementMaster.code) {
    //     return 1;
    //   }
    //   return 0;
    // });

    // forCharElementCodes.sort(function (a, b) {
    //   if (a.elementMaster.code < b.elementMaster.code) {
    //     return -1;
    //   }
    //   if (a.elementMaster.code > b.elementMaster.code) {
    //     return 1;
    //   }
    //   return 0;
    // });

    // return [
    //   ...oneCharElementCodes,
    //   ...twoCharElementCodes,
    //   ...threeCharElementCodes,
    //   ...forCharElementCodes,
    // ];

    // Sort the element code by character, number, and "-"
    const specialCharacters = new RegExp(`[^a-zA-Z0-9\-_\.,;!?:|\\(){}<>/\*\s]+`);

    selfDeclarationsSortedByPriority.sort((a, b) => {
      // Split the code string into sections
      const sectionsA = a.elementMaster.code.split(specialCharacters);
      const sectionsB = b.elementMaster.code.split(specialCharacters);

      // Compare each section of the code string
      for (let i = 0; i < sectionsA.length; i++) {
        const sectionA = sectionsA[i];
        const sectionB = sectionsB[i];

        // Compare section as number if it is a number
        if (!isNaN(parseInt(sectionA)) && !isNaN(parseInt(sectionB))) {
          const numA = parseInt(sectionA);
          const numB = parseInt(sectionB);

          if (numA !== numB) {
            return numA - numB;
          }
        }
        // Compare section as string if it is not a number
        else {
          if (sectionA !== sectionB) {
            return sectionA.localeCompare(sectionB, undefined, {
              numeric: true,
              sensitivity: 'base',
            });
          }
        }
      }
      return 0;
    });

    return selfDeclarationsSortedByPriority;
  }

  async checkUserCanUpdateRecord(userId: string, selfAssessmentId: string) {
    const userAssignments = await this.connection
      .getCustomRepository(UserAssignmentRepository)
      .find({
        where: {
          selfAssessmentId,
          userId,
          permission: In([WorkflowPermission.REVIEWER, WorkflowPermission.PUBLISHER]),
        },
      });

    return userAssignments.length > 0 ? true : false;
  }

  async updateSelfDeclarationForListing(
    id: string,
    selfAssessmentId: string,
    body: UpdateSelfDeclarationForListingDto,
    user: TokenPayloadModel,
  ) {
    try {
      if (!body.complianceId && !body.targetCompletionDate) {
        return 0;
      }
      const getSelfDeclaration = await this.manager.findOne(SelfDeclaration, {
        where: { id },
      });
      if (!getSelfDeclaration) {
        throw new BaseError({ status: 404, message: 'selfDeclaration.NOT_FOUND' });
      }
      // to update the status if we choose the compliance via AG Grid
      const prepareDeclarations = {
        complianceId: body.complianceId,
        targetCompletionDate: body.targetCompletionDate,
        status:
          getSelfDeclaration.status === SelfDeclarationStatusEnum.SAVED
            ? SelfDeclarationStatusEnum.SAVED
            : SelfDeclarationStatusEnum.PENDING,
      };
      await this.manager.update(
        SelfDeclaration,
        {
          id: id,
          deleted: false,
        },
        prepareDeclarations,
      );
      const complianceId = body.complianceId;
      // compliance id also add to self declaration comment table
      const selfDeclarationCommentCheck = await this.manager.findOne(SelfDeclarationComment, {
        selfDeclarationId: id,
      });
      const createdUser = await this.manager
        .getCustomRepository(UserRepository)
        ._getUserInfoForHistory(user.id);
      if (selfDeclarationCommentCheck) {
        await this.manager.update(
          SelfDeclarationComment,
          {
            selfDeclarationId: id,
          },
          { complianceId: complianceId },
        );
      } else {
        await this.manager.insert(SelfDeclarationComment, {
          selfDeclarationId: id,
          complianceId: complianceId,
          type: body.type,
          createdUser: createdUser,
          comment: '',
        });
      }

      const selfDeclarationHistoryCheck = await this.manager.findOne(SelfDeclarationHistory, {
        selfDeclarationId: id,
      });
      if (selfDeclarationHistoryCheck) {
        await this.manager.update(
          SelfDeclarationHistory,
          {
            selfDeclarationId: id,
          },
          { complianceId: complianceId },
        );
      } else {
        await this.manager.insert(SelfDeclarationHistory, {
          selfDeclarationId: id,
          complianceId: complianceId,
          createdUser: createdUser,
          comment: '',
        });
      }

      await this.manager.update(
        SelfAssessment,
        {
          id: getSelfDeclaration.selfAssessmentId,
          deleted: false,
        },
        {
          updatedUserId: user.id,
          status: SelfAssessmentStatusEnum.INPROGRESS,
        },
      );
      return 1;
    } catch (ex) {
      LoggerCommon.error(
        '[SelfDeclarationRepository] updateSelfDeclarationForListing error ',
        ex.message || ex,
      );
      throw ex;
    }
  }
}
