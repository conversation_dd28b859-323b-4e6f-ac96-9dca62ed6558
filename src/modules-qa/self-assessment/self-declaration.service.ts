import { BadRequestException, Injectable } from '@nestjs/common';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import { SelfDeclarationRepository } from './repository/self-declaration.repository';
import {
  ReOpenSelfDeclarationDto,
  ListSelfDeclarationDto,
  UpdateSelfDeclarationDto,
  ListLoopBackCommentDto,
  ReAssignSelfDeclarationDto,
  UpdateSelfDeclarationForListingDto,
  TitleSelfDeclaration,
} from './dto';
import { SelfDeclarationCommentRepository } from './repository/self-declaration-comment.repository';
import { NotificationProducer } from '../../micro-services/async/notification.producer';
import { EmailProducer } from '../../micro-services/async/email.producer';
import { Response } from 'express';
import _ from 'lodash';
import ExcelJS from 'exceljs';
import { ListLabelConfigCompanyDto } from '../catalog/dto/list-label-config-company.dto';
import { ActionEnum, LanguageCodeEnum } from '../../commons/enums/catalog.enum';
import { FeatureEnum, SubFeatureEnum } from '../../commons/enums';
import { LabelConfig } from '../catalog/entity/label-config.entity';
import { ModuleLabelConfig } from '../catalog/entity/module-label-config.entity';
import { UpdateSelfAssessmentCustomizationDto } from './dto/update-self-assessment-customization.dto';
import { LabelConfigRepository } from '../catalog/repository/label-config.repository';
import { ModuleConfigRepository } from '../catalog/repository/module-config.repository';
import { ModuleLabelConfigRepository } from '../catalog/repository/module-label-config.repository';
import { SelfAssessmentRepository } from './repository/self-assessment.repository';
import { StandardMasterRepository } from '../standard-master/repository/standard-master.repository';
import { StandardModuleLabel } from '../standard-master/entity/standard-module-label.entity';
import { MetaConfig } from '../catalog/entity/meta-config.entity';
import { CatalogConst } from '../catalog/catalog-key.const';
import { AuditWorkspace } from '../../modules/audit-workspace/entities/audit-workspace.entity';
import { StandardModuleLabelRepository } from '../standard-master/repository/standard-module-label.repository';
@Injectable()
export class SelfDeclarationService {
  constructor(
    private readonly selfDeclarationRepository: SelfDeclarationRepository,
    private readonly selfDeclarationComment: SelfDeclarationCommentRepository,
    private readonly notificationProducer: NotificationProducer,
    private readonly emailProducer: EmailProducer,
    private readonly labelConfigRepository: LabelConfigRepository,
    private readonly moduleConfigRepository: ModuleConfigRepository,
    private readonly moduleLabelConfigRepository: ModuleLabelConfigRepository,
    private readonly selfAssessmentRepository: SelfAssessmentRepository,
    private readonly standardModuleLabelRepository: StandardModuleLabelRepository,
  ) {
    this.selfDeclarationRepository._migrateSyncStandardModuleLabel();
  }

  async updateSelfDeclaration(
    id: string,
    selfAssessmentId: string,
    body: UpdateSelfDeclarationDto,
    user: TokenPayloadModel,
    // workflowPermissions: string[],
  ) {
    const { dataNoti, dataSendMail } = await this.selfDeclarationRepository.updateSelfDeclaration(
      id,
      selfAssessmentId,
      body,
      user,
      // workflowPermissions,
    );
    if (dataNoti.length > 0) {
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
    }
    if (dataSendMail.length > 0) {
      this.emailProducer.publishEmail(dataSendMail);
    }
    return 1;
  }

  async reOpenSelfDeclaration(
    user: TokenPayloadModel,
    reOpenSelfDeclaration: ReOpenSelfDeclarationDto,
    selfAssessmentId: string,
  ) {
    const { dataNoti, dataSendMail } = await this.selfDeclarationRepository.reOpenSelfDeclaration(
      user,
      reOpenSelfDeclaration,
      selfAssessmentId,
    );
    for (const data of dataNoti) {
      this.notificationProducer.publishNotification(data);
    }
    this.emailProducer.publishEmail(dataSendMail);
    return 1;
  }

  async getListSelfDeclarationBySelfAssessmentId(
    selfAssessmentId: string,
    query: ListSelfDeclarationDto,
  ) {
    return this.selfDeclarationRepository.getListSelfDeclarationBySelfAssessmentId(
      selfAssessmentId,
      query,
    );
  }

  async exportSelfDeclarationBySelfAssessmentId(
    selfAssessmentId: string,
    user: TokenPayloadModel,
    res: Response,
  ) {
    const selfDeclarations = (
      await this.selfDeclarationRepository.getListSelfDeclarationBySelfAssessmentId(
        selfAssessmentId,
        { page: 1, pageSize: -1 },
      )
    )?.data;

    const selfDeclarationsLevels = selfDeclarations[0]?.elementMaster?.standardMaster?.levels?.map(
      (x) => x.toUpperCase(),
    );
    const standardMasterId = selfDeclarations[0]?.elementMaster?.standardMaster?.id;

    const sortedSelfDeclarations = selfDeclarations.sort((item1, item2) => {
      return (
        selfDeclarationsLevels?.indexOf(item1.elementMaster.stage.toUpperCase()) -
        selfDeclarationsLevels?.indexOf(item2.elementMaster.stage.toUpperCase())
      );
    });

    selfDeclarations.forEach(
      (selfDec) =>
        ((selfDec as any).groupCode = `${selfDec.elementMaster.group} ${selfDec.elementMaster.code}`),
    );
    const selfDecGroupCodes = _.chain(selfDeclarations)
      .groupBy('groupCode')
      .map((value, key) => ({ groupCode: key, decs: value }))
      .value();

    const listCustomization = await this.listCustomizationByStandard(
      { standardMasterId: standardMasterId },
      user,
    );
    const customizationMap = listCustomization
      .filter((label) => !label.isHide)
      .reduce(function (map, label) {
        map[label.key] = label.userDefinedLabel;
        return map;
      }, {});

    const workbook = new ExcelJS.Workbook();
    try {
      selfDecGroupCodes.forEach((selfDecGroupCode) => {
        const elementMaster = selfDecGroupCode.decs[0].elementMaster;
        const group = elementMaster.group;
        const sheetColor =
          group === 'Performance' ? 'ff0000' : group === 'Process' ? 'ffe699' : 'f8cbad'; // Value for 'People' and 'Plant'
        const worksheet = workbook.addWorksheet(selfDecGroupCode.groupCode, {
          properties: { defaultColWidth: 25, tabColor: { argb: sheetColor } },
        });
        worksheet.protect('a', { selectLockedCells: false });

        const rowSub = worksheet.addRow([
          `Subject Area No. ${elementMaster.number}. ${elementMaster.name}\nAim/Principle:  ${
            elementMaster.aimPrinciple ? elementMaster.aimPrinciple : ''
          } `,
        ]);

        const rowTitle = worksheet.addRow(Object.values(customizationMap));

        const selfDecGroupStages = _.chain(selfDecGroupCode.decs)
          .groupBy('elementMaster.stage')
          .map((value, key) => ({ stage: key, decs: value }))
          .value();

        selfDecGroupStages.forEach((value) => {
          value.decs.forEach((dec) =>
            worksheet.addRow(
              [
                !customizationMap[TitleSelfDeclaration.LEVEL] ? null : value.stage || '',
                !customizationMap[TitleSelfDeclaration.EXPECTATIONS]
                  ? null
                  : dec.elementMaster?.bestPracticeGuidance || '',
                !customizationMap[TitleSelfDeclaration.TARGETS]
                  ? null
                  : dec.elementMaster?.keyPerformanceIndicator || '',
                !customizationMap[TitleSelfDeclaration.SUGGESTED_OBJECTIVE_EVIDENCE]
                  ? null
                  : dec.elementMaster?.otherInfoGuidance || '',
                !customizationMap[TitleSelfDeclaration.OPERATOR_ASSESSMENT]
                  ? null
                  : dec.compliance?.answer || '',
                !customizationMap[TitleSelfDeclaration.OPERATOR_COMMENTS]
                  ? null
                  : dec.newestExternalComment || '',
              ].filter((title) => title !== null),
            ),
          );
          const lastRow = worksheet.lastRow.number;
          worksheet.mergeCells(lastRow, 1, lastRow - (value.decs.length - 1), 1);
        });

        let colTitle;
        let cellTitle;
        Object.keys(customizationMap).forEach((value, index) => {
          colTitle = worksheet.getColumn(index + 1);
          cellTitle = worksheet.getCell(2, index + 1);
          switch (value) {
            case TitleSelfDeclaration.LEVEL:
              colTitle.alignment = {
                wrapText: true,
                vertical: 'middle',
                horizontal: 'center',
              };
              colTitle.width = 15;
              colTitle.font = {
                name: 'Verdana',
                bold: true,
                size: 10,
              };

              cellTitle.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'ffd966' },
              };
              cellTitle.font = {
                name: 'Calibri Light',
                bold: true,
                size: 11,
              };
              break;

            case TitleSelfDeclaration.EXPECTATIONS:
            case TitleSelfDeclaration.TARGETS:
            case TitleSelfDeclaration.SUGGESTED_OBJECTIVE_EVIDENCE:
              colTitle.alignment = {
                wrapText: true,
                vertical: 'top',
                horizontal: 'left',
              };
              colTitle.width = 30;
              colTitle.font = {
                name: 'Verdana',
                bold: false,
                size: 8,
              };

              cellTitle.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'ffd966' },
              };
              cellTitle.font = {
                name: 'Calibri Light',
                bold: true,
                size: 11,
              };
              cellTitle.alignment = {
                wrapText: true,
                vertical: 'middle',
                horizontal: 'left',
              };
              break;

            case TitleSelfDeclaration.OPERATOR_ASSESSMENT:
              colTitle.alignment = {
                wrapText: true,
                vertical: 'middle',
                horizontal: 'center',
              };
              colTitle.width = 15;
              colTitle.font = {
                name: 'Verdana',
                bold: true,
                size: 10,
              };
              colTitle.protection = { locked: false };

              cellTitle.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'c65911' },
              };
              cellTitle.font = {
                name: 'Calibri Light',
                bold: true,
                size: 11,
                color: { argb: 'FFFFFF' },
              };
              break;

            case TitleSelfDeclaration.OPERATOR_COMMENTS:
              colTitle.alignment = {
                wrapText: true,
                vertical: 'top',
                horizontal: 'left',
              };
              colTitle.width = 30;
              colTitle.font = {
                name: 'Verdana',
                bold: false,
                size: 8,
              };
              colTitle.protection = { locked: false };

              cellTitle.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'c65911' },
              };
              cellTitle.font = {
                name: 'Calibri Light',
                bold: true,
                size: 11,
                color: { argb: 'FFFFFF' },
              };
              cellTitle.alignment = {
                wrapText: true,
                vertical: 'middle',
                horizontal: 'center',
              };
              break;
          }
        });

        const cellSub = rowSub.getCell(1);
        rowSub.height = 50;
        cellSub.font = {
          name: 'Calibri',
          bold: true,
          size: 16,
        };
        cellSub.alignment = {
          wrapText: true,
          vertical: 'middle',
          horizontal: 'left',
        };
        cellSub.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'e2efda' },
        };

        worksheet.mergeCells(1, 1, 1, Object.keys(customizationMap).length);
        worksheet.columns.forEach((column) => {
          column['eachCell']({ includeEmpty: true }, function (cell) {
            cell.border = {
              top: { style: 'thin', color: { argb: '000000' } },
              left: { style: 'thin', color: { argb: '000000' } },
              bottom: { style: 'thin', color: { argb: '000000' } },
              right: { style: 'thin', color: { argb: '000000' } },
            };
          });
        });
      });

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      //get file name
      const selfAssessment = await this.selfAssessmentRepository
        .createQueryBuilder('selfAssessment')
        .leftJoin('selfAssessment.standardMaster', 'standardMaster')
        .select()
        .addSelect(['standardMaster.name', 'standardMaster.code', 'standardMaster.scoreApplicable'])
        .where('selfAssessment.companyId = :companyId AND selfAssessment.id = :selfAssessmentId', {
          companyId: user.companyId,
          selfAssessmentId,
        })
        .getOne();
      const fileName = `Self Assessment Report - ${selfAssessment?.standardMaster?.name}.xlsx`;
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    } catch (error) {
      throw new BadRequestException(error);
    }
    return workbook.xlsx.write(res).then(function () {
      res.status(200).end();
    });
  }

  async getCountSelfDeclarationBySelfAssessmentId(selfAssessmentId: string) {
    return this.selfDeclarationRepository.getCountSelfDeclarationBySelfAssessmentId(
      selfAssessmentId,
    );
  }

  async listSelfDeclarationForMatrix(selfAssessmentId: string, query: ListSelfDeclarationDto) {
    return this.selfDeclarationRepository.listSelfDeclarationForMatrix(selfAssessmentId, query);
  }

  async getDetailSelfDeclarationById(selfDeclarationId: string, type?: string) {
    return this.selfDeclarationRepository.getDetailSelfDeclaration(selfDeclarationId, type);
  }
  async reAssignSelfDeclaration(user: TokenPayloadModel, body: ReAssignSelfDeclarationDto) {
    const { dataNoti, dataSendMail } = await this.selfDeclarationRepository.reAssignSelfDeclaration(
      user,
      body,
    );
    if (dataNoti.length > 0) {
      for (const data of dataNoti) {
        this.notificationProducer.publishNotification(data);
      }
    }
    if (dataSendMail.length > 0) {
      this.emailProducer.publishEmail(dataSendMail);
    }
    return 1;
  }
  async getListLoopBackCommentBySelfDeclarationId(
    selfDeclarationId: string,
    query: ListLoopBackCommentDto,
  ) {
    return this.selfDeclarationComment.getListCommentBySelfDeclarationId(selfDeclarationId, query);
  }

  async updateSelfDeclarationForListing(
    id: string,
    selfAssessmentId: string,
    body: UpdateSelfDeclarationForListingDto,
    user: TokenPayloadModel,
  ) {
    return this.selfDeclarationRepository.updateSelfDeclarationForListing(
      id,
      selfAssessmentId,
      body,
      user,
    );
  }

  async listCustomizationExport(query: ListLabelConfigCompanyDto, user: TokenPayloadModel) {
    const lang = query.lang || LanguageCodeEnum.EN;

    const defaultModule = await this.moduleConfigRepository.findOne({
      where: {
        key: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        isDefault: true,
      },
    });
    const defaultLabels = await this.moduleLabelConfigRepository.find({
      moduleId: defaultModule.id,
      action: ActionEnum.EXPORT,
    });
    if (!defaultLabels?.length) {
      await this._initLabelConfigExport(defaultModule.id, lang);
    }

    let module = await this.moduleConfigRepository.findOne({
      where: {
        key: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        companyId: user.companyId,
      },
    });
    if (!module) {
      module = defaultModule;
    }

    const exportLabels = await this.labelConfigRepository.listLabelByModule(
      { lang, action: ActionEnum.EXPORT, companyId: user.companyId, pageSize: -1 },
      user,
      module.id,
    );
    return Object.values(TitleSelfDeclaration).map((title) => {
      const item = exportLabels.data.find((value) => title === value.key);
      return {
        id: item?.id,
        key: item?.key,
        defaultLabel: item?.defaultLabel,
        userDefinedLabel: item?.userDefinedLabel,
        isHide: item?.isHide,
      };
    });
  }

  private async _initLabelConfigExport(moduleId: string, lang: String) {
    const defaultLabels = Object.values(TitleSelfDeclaration).map(
      (value) =>
        ({
          moduleId: moduleId,
          defaultLabel: value,
          userDefinedLabel: value,
          key: value,
          language: lang,
          isDefault: true,
        } as LabelConfig),
    );
    const newLabels = await this.labelConfigRepository.save(defaultLabels);

    const moduleLabelConfigs = newLabels.map(
      (label) => ({ moduleId, labelId: label.id, action: ActionEnum.EXPORT } as ModuleLabelConfig),
    );

    await this.moduleLabelConfigRepository.save(moduleLabelConfigs);
  }

  async updateCustomizationExport(
    user: TokenPayloadModel,
    standardMasterId: string,
    body: UpdateSelfAssessmentCustomizationDto,
  ) {
    const module = await this.moduleConfigRepository.findOne({
      where: {
        key: FeatureEnum.QUALITY_ASSURANCE_SELF_ASSESSMENT + '::' + SubFeatureEnum.SELF_ASSESSMENT,
        isDefault: true,
      },
    });

    const standardCustomization = await this.standardModuleLabelRepository._getLabelByStandardAndModule(
      standardMasterId,
      module.id,
      body.labelId,
    );
    standardCustomization.userDefinedLabel = body.userDefinedLabel;
    standardCustomization.isHide = body.isHide;
    return this.standardModuleLabelRepository.save(standardCustomization);
  }

  async resetCustomizationExport(standardMasterId: string, user: TokenPayloadModel) {
    const listCustomization = await this.standardModuleLabelRepository._getByStandardId(
      standardMasterId,
      user,
    );
    listCustomization.forEach((value) => {
      value.userDefinedLabel = value.label.defaultLabel;
      value.isHide = false;
      delete value['label'];
    });
    await this.standardModuleLabelRepository.save(listCustomization);
  }

  async listCustomizationByStandard(query: ListLabelConfigCompanyDto, user: TokenPayloadModel) {
    const labelResponse = await this.standardModuleLabelRepository._getByStandardId(
      query.standardMasterId,
      user,
    );
    const response = [];
    Object.values(TitleSelfDeclaration).forEach((title) => {
      const item = labelResponse?.find((value) => title === value?.label?.key);
      if (item) {
        response.push({
          id: item?.label?.id,
          key: item?.label?.key,
          defaultLabel: item?.label?.defaultLabel,
          userDefinedLabel: item?.userDefinedLabel,
          isHide: item?.isHide,
          standardMasterId: item?.standardMasterId,
          moduleId: item?.moduleId,
        });
      }
    });
    return response;
  }
}
