import moment from 'moment';
import {
  <PERSON><PERSON>rror,
  CommonStatus,
  <PERSON>gger<PERSON>om<PERSON>,
  RoleScopeCheck,
  TokenPayloadModel,
  TypeORMRepository,
} from 'svm-nest-lib-v3';
import { Connection, EntityRepository } from 'typeorm';
import {
  ClassDispensationsStatusEnum,
  CompanyLevelEnum,
  EmailTypeEnum,
  MailTemplate,
  ModulePathEnum,
  PushTypeEnum,
  VesselScreeningFilterRiskEnum,
  VesselScreeningObservedRiskEnum,
  VesselScreeningPotentialRiskEnum,
} from '../../../commons/enums';
import APP_CONFIG from '../../../configs/app.config';
import { IEmailEventModel, IUserEmail } from '../../../micro-services/async/email.producer';
import {
  INotificationEventModel,
  IUser,
} from '../../../micro-services/async/notification.producer';
import { CompanyFeatureVersionRepository } from '../../../modules/commons/company-feature-version/company-feature-version.repository';
import { FeatureVersionConfig } from '../../../modules/commons/company-feature-version/feature-version.config';
import { Company } from '../../../modules/company/company.entity';
import { ModuleType } from '../../../modules/user-assignment/user-assignment.enum';
import { UserRepository } from '../../../modules/user/user.repository';
import { leadingZero } from '../../../utils';
import {
  VesselScreeningSummary,
  VesselScreeningSummaryReferenceEnum,
} from '../../vessel-screening/entity/vessel-screening-summary.entity';
import { VesselScreening } from '../../vessel-screening/entity/vessel-screening.entity';
import { VesselScreeningSummaryRepository } from '../../vessel-screening/repository/vessel-screening-summary.repository';
import { VesselScreeningRepository } from '../../vessel-screening/repository/vessel-screening.repository';
import { CreateClassDispensationsDto, UpdateClassDispensationsDto } from '../dto';
import { ListClassDispensationsDto } from '../dto/list-class-dispensations.dto';
import { ClassDispensationsRequest } from '../entity/class-dispensations-request.entity';
import { ClassDispensations } from '../entity/class-dispensations.entity';
import { VesselDocHolder } from '../../../modules/vessel/entity/vessel-doc-holder.entity';
import { VesselCharterer } from '../../../modules/vessel/entity/vessel-charterer.entity';
import { VesselOwner } from '../../../modules/vessel/entity/vessel-owner.entity';
import { VesselModuleEnum } from '../../../modules/vessel/vessel.enum';
import { VesselRepository } from 'src/modules/vessel/vessel.repository';
import { EventTypeRepository } from '../../event-type/event-type.repository';
import { AuthorityMasterRepository } from '../../../modules/authority-master/authority-master.repository';

@EntityRepository(ClassDispensations)
export class ClassDispensationsRepository extends TypeORMRepository<ClassDispensations> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async createClassDispensations(body: CreateClassDispensationsDto, token: TokenPayloadModel) {
    try {
      return await this.connection.transaction(async (manager) => {
        //check active vessel
        await manager
          .getCustomRepository(VesselRepository)
          ._checkActiveVessel([body.vesselId], token);
        //check active Event Type
        if (body.eventTypeId) {
          await this.manager
            .getCustomRepository(EventTypeRepository)
            ._checkActiveEventType([body.eventTypeId], token.companyId);
        }
        //check active Authority Master
        if (body.authorityId) {
          await this.manager
            .getCustomRepository(AuthorityMasterRepository)
            ._checkActiveAuthorityMaster([body.authorityId], token.companyId);
        }

        if (body.status !== ClassDispensationsStatusEnum.OPEN && !body.closedDate) {
          throw new BaseError({
            status: 400,
            message: 'classDispensations.CLOSED_DATE_REQUIRED',
          });
        }

        const currYear = moment().year();
        const counter = await manager
          .getCustomRepository(CompanyFeatureVersionRepository)
          .getNextVersion({
            manager: manager,
            companyId: token.companyId,
            feature: FeatureVersionConfig.DISPENSATIONS,
            year: Number(currYear),
          });
        const company = await manager.findOne(Company, {
          where: { id: token.companyId },
          select: ['code'],
        });

        const serialNumber = leadingZero(counter, 3);

        const prepareClassDispensation = {
          ...body,
          refId: `${company.code}/CCD/${serialNumber}/${currYear}`,
          createdUserId: token.id,
          companyId: token.companyId,
        };

        const classDispensation = await manager.insert(
          ClassDispensations,
          prepareClassDispensation,
        );
        const listVesselScreening = await manager.find(VesselScreening, {
          where: {
            vesselId: body.vesselId,
            deleted: false,
          },
          select: ['id'],
        });
        if (listVesselScreening.length > 0) {
          const classDispensationId = classDispensation.identifiers[0].id;
          const preparedClassDispensationsRequests: ClassDispensationsRequest[] = [];
          for (const itemVesselScreening of listVesselScreening) {
            const classDispensationsRequest = {
              classDispensationsId: classDispensationId,
              vesselScreeningId: itemVesselScreening.id,
            } as ClassDispensationsRequest;
            if (body.expiryDate > body.issueDate) {
              classDispensationsRequest.potentialRisk = VesselScreeningPotentialRiskEnum.HIGH;
              classDispensationsRequest.observedRisk = VesselScreeningObservedRiskEnum.HIGH;
              classDispensationsRequest.potentialScore = 10;
              classDispensationsRequest.observedScore = 10;
            } else {
              classDispensationsRequest.potentialRisk = VesselScreeningPotentialRiskEnum.MEDIUM;
              classDispensationsRequest.observedRisk = VesselScreeningObservedRiskEnum.MEDIUM;
              classDispensationsRequest.potentialScore = 5;
              classDispensationsRequest.observedScore = 5;
            }
            preparedClassDispensationsRequests.push(
              classDispensationsRequest as ClassDispensationsRequest,
            );
          }
          const classDispensationRequests = await manager.save(
            ClassDispensationsRequest,
            preparedClassDispensationsRequests,
          );
          for (let i = 0; i < classDispensationRequests.length; i++) {
            await manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                manager,
                VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS,
                classDispensationRequests[i].vesselScreeningId,
              );
          }
        }
        return 1;
      });
    } catch (ex) {
      LoggerCommon.error(
        '[ClassDispensationsRepository] createClassDispensations error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async listClassDispensations(query: ListClassDispensationsDto, token: TokenPayloadModel) {
    const queryBuilder = this.createQueryBuilder('classDispensations')
      .leftJoin('classDispensations.eventType', 'eventType')
      .leftJoin('classDispensations.authority', 'authority')
      .leftJoin('classDispensations.vessel', 'vessel')
      .select()
      .addSelect([
        'eventType.code',
        'eventType.name',
        'authority.name',
        'vessel.code',
        'vessel.name',
        'vessel.imoNumber',
      ])
      .where('classDispensations.companyId = :companyId', {
        companyId: token.companyId,
      });

    if (query.vesselId) {
      queryBuilder.andWhere('classDispensations.vesselId = :vesselId', {
        vesselId: query.vesselId,
      });
      if (!RoleScopeCheck.isAdmin(token)) {
        const { whereForExternal, whereForMainAndInternal } =
          await this._supportWhereDOCChartererOwner(token.explicitCompanyId, query.vesselId);
        queryBuilder
          .leftJoin('vessel.divisionMapping', 'divisionMapping')
          .leftJoin('divisionMapping.division', 'division')
          .leftJoin('division.users', 'users')
          .leftJoin('vessel.vesselDocHolders', 'vesselDocHolders')
          .addSelect([
            'vesselDocHolders.id',
            'vesselDocHolders.companyId',
            'vesselDocHolders.fromDate',
            'vesselDocHolders.toDate',
            'vesselDocHolders.responsiblePartyInspection',
            'vesselDocHolders.responsiblePartyQA',
            'vesselDocHolders.status',
          ])
          .leftJoin('vessel.vesselOwners', 'vesselOwners')
          .addSelect([
            'vesselOwners.id',
            'vesselOwners.companyId',
            'vesselOwners.fromDate',
            'vesselOwners.toDate',
            'vesselOwners.responsiblePartyInspection',
            'vesselOwners.responsiblePartyQA',
            'vesselOwners.status',
          ])
          .leftJoin('vessel.vesselCharterers', 'vesselCharterers')
          .addSelect([
            'vesselCharterers.id',
            'vesselCharterers.companyId',
            'vesselCharterers.fromDate',
            'vesselCharterers.toDate',
            'vesselCharterers.responsiblePartyInspection',
            'vesselCharterers.responsiblePartyQA',
            'vesselCharterers.status',
          ]);
        if (
          token.companyLevel === CompanyLevelEnum.MAIN_COMPANY ||
          token.companyLevel === CompanyLevelEnum.INTERNAL_COMPANY
        ) {
          queryBuilder.andWhere(`( users.id = :userId ` + whereForMainAndInternal + ')', {
            userId: token.id,
          });
        } else {
          queryBuilder.andWhere(`( users.id = :userId ` + whereForExternal + ')', {
            userId: token.id,
          });
        }
      }
    }

    if (query.issueDateFrom) {
      queryBuilder.andWhere('classDispensations.issueDate >= :issueDateFrom', {
        issueDateFrom: new Date(query.issueDateFrom),
      });
    }

    if (query.issueDateTo) {
      queryBuilder.andWhere('classDispensations.issueDate <= :issueDateTo', {
        issueDateTo: new Date(query.issueDateTo),
      });
    }

    const dataList = await this.list(
      { page: query.page, limit: query.pageSize },
      {
        queryBuilder,
        sort: query.sort || 'classDispensations.createdAt: -1',
        advanceConditions: {
          createdAtFrom: query.createdAtFrom,
          createdAtTo: query.createdAtTo,
        },
      },
    );

    return dataList;
  }

  async _supportWhereDOCChartererOwner(companyId: string, vesselId: string) {
    const listDOCHolder = await this.manager.find(VesselDocHolder, {
      deleted: false,
      companyId,
      vesselId,
    });

    const listVesselCharterers = await this.manager.find(VesselCharterer, {
      deleted: false,
      companyId,
      vesselId,
    });

    const listVesselOwners = await this.manager.find(VesselOwner, {
      deleted: false,
      companyId,
      vesselId,
    });
    let whereForExternal = '';
    let whereForMainAndInternal = '';
    if (listDOCHolder.length > 0) {
      for (const item of listDOCHolder) {
        whereForExternal += ` or (vesselDocHolders.companyId = '${
          item.companyId
        }' and classDispensations.createdAt >='${
          item.fromDate ? item.fromDate.toISOString() : new Date().toISOString()
        }' and classDispensations.createdAt <='${
          item.toDate ? item.toDate.toISOString() : new Date().toISOString()
        }' and vesselDocHolders.responsiblePartyQA = true and classDispensations.vesselId = '${
          item.vesselId
        }' and vesselDocHolders.id = '${item.id}' ) `;

        whereForMainAndInternal += ` or (vesselDocHolders.companyId = '${item.companyId}' and classDispensations.vesselId = '${item.vesselId}')`;
      }
    }

    if (listVesselCharterers.length > 0) {
      for (const item of listVesselCharterers) {
        whereForExternal += ` or (vesselCharterers.companyId = '${
          item.companyId
        }' and classDispensations.createdAt >='${
          item.fromDate ? item.fromDate.toISOString() : new Date().toISOString()
        }' and classDispensations.createdAt <='${
          item.toDate ? item.toDate.toISOString() : new Date().toISOString()
        }' and vesselCharterers.responsiblePartyQA = true and classDispensations.vesselId = '${
          item.vesselId
        }' and vesselCharterers.id = '${item.id}' )`;
        whereForMainAndInternal += ` or (vesselCharterers.companyId = '${item.companyId}' and classDispensations.vesselId = '${item.vesselId}')`;
      }
    }
    if (listVesselOwners.length > 0) {
      for (const item of listVesselOwners) {
        whereForExternal += ` or (vesselOwners.companyId = '${
          item.companyId
        }' and classDispensations.createdAt >='${
          item.fromDate ? item.fromDate.toISOString() : new Date().toISOString()
        }' and classDispensations.createdAt <='${
          item.toDate ? item.toDate.toISOString() : new Date().toISOString()
        }' and vesselOwners.responsiblePartyQA = true and classDispensations.vesselId = '${
          item.vesselId
        }' and vesselOwners.id = '${item.id}' )`;
        whereForMainAndInternal += ` or (vesselOwners.companyId = '${item.companyId}' and classDispensations.vesselId = '${item.vesselId}')`;
      }
    }
    return { whereForExternal, whereForMainAndInternal };
  }

  async detailClassDispensations(id: string, token: TokenPayloadModel) {
    const classDispensations = await this.getOneQB(
      this.createQueryBuilder('classDispensations')
        .leftJoin('classDispensations.eventType', 'eventType')
        .leftJoin('classDispensations.authority', 'authority')
        .leftJoin('classDispensations.vessel', 'vessel')
        .where('classDispensations.id = :id AND classDispensations.companyId = :companyId', {
          id,
          companyId: token.companyId,
        })
        .select()
        .addSelect([
          'eventType.code',
          'eventType.name',
          'authority.name',
          'vessel.code',
          'vessel.name',
          'vessel.imoNumber',
        ]),
    );
    if (classDispensations) {
      return classDispensations;
    } else {
      throw new BaseError({ status: 404, message: 'classDispensations.NOT_FOUND' });
    }
  }

  async updateClassDispensations(
    id: string,
    body: UpdateClassDispensationsDto,
    token: TokenPayloadModel,
  ) {
    if (body.status !== ClassDispensationsStatusEnum.OPEN && !body.closedDate) {
      throw new BaseError({
        status: 400,
        message: 'classDispensations.CLOSED_DATE_REQUIRED',
      });
    }

    try {
      const classDispensationsDB = await this.createQueryBuilder('classDispensation')
        .leftJoin('classDispensation.createdUser', 'createdUser')
        .addSelect([
          'createdUser.id',
          'createdUser.jobTitle',
          'createdUser.username',
          'createdUser.email',
        ])
        .where('classDispensation.id = :id', { id })
        .getOne();

      //check active vessel
      await this.manager
        .getCustomRepository(VesselRepository)
        ._compareAndCheckActiveVessel(classDispensationsDB.vesselId, body.vesselId, token);
      //check active Event Type
      if (body.eventTypeId && body.eventTypeId !== classDispensationsDB.eventTypeId) {
        await this.manager
          .getCustomRepository(EventTypeRepository)
          ._checkActiveEventType([body.eventTypeId], token.companyId);
      }
      //check active Authority Master
      if (body.authorityId && body.authorityId !== classDispensationsDB.authorityId) {
        await this.manager
          .getCustomRepository(AuthorityMasterRepository)
          ._checkActiveAuthorityMaster([body.authorityId], token.companyId);
      }

      const prepareUpdateClassDispensations = {
        ...body,
        updatedUserId: token.id,
      };

      const updateClassDispensations = await this.update(
        {
          id: id,
          companyId: token.companyId,
          deleted: false,
        },
        prepareUpdateClassDispensations,
      );

      if (updateClassDispensations.affected > 0) {
        // update risk
        const listClassDispensationsRequest = await this.manager.find(ClassDispensationsRequest, {
          where: {
            classDispensationsId: id,
          },
          select: ['id', 'updatedObservedRisk'],
        });
        if (listClassDispensationsRequest.length > 0) {
          const classDispensationsDetail = await this.detailClassDispensations(id, token);
          const preparedClassDispensationsRequests: ClassDispensationsRequest[] = [];
          for (const CDRequest of listClassDispensationsRequest) {
            const classDispensationsRequest = {
              id: CDRequest.id,
            } as ClassDispensationsRequest;
            if (classDispensationsDetail.closedDate > classDispensationsDetail.expiryDate) {
              classDispensationsRequest.potentialRisk = VesselScreeningPotentialRiskEnum.HIGH;
              classDispensationsRequest.potentialScore = 10;
              if (CDRequest.updatedObservedRisk !== true) {
                classDispensationsRequest.observedRisk = VesselScreeningObservedRiskEnum.HIGH;
                classDispensationsRequest.observedScore = 10;
              }
            } else {
              classDispensationsRequest.potentialRisk = VesselScreeningPotentialRiskEnum.MEDIUM;
              classDispensationsRequest.potentialScore = 5;
              if (CDRequest.updatedObservedRisk !== true) {
                classDispensationsRequest.observedRisk = VesselScreeningObservedRiskEnum.MEDIUM;
                classDispensationsRequest.observedScore = 5;
              }
            }
            preparedClassDispensationsRequests.push(classDispensationsRequest);
          }
          await this.connection.transaction(async (manager) => {
            const classDispensationRequests = await manager.save(
              ClassDispensationsRequest,
              preparedClassDispensationsRequests,
            );
            for (let i = 0; i < classDispensationRequests.length; i++) {
              const classDispensationRequestDetail = await manager.findOne(
                ClassDispensationsRequest,
                classDispensationRequests[i].id,
                {
                  select: ['vesselScreeningId'],
                },
              );

              await manager
                .getCustomRepository(VesselScreeningSummaryRepository)
                .updateVesselScreeningSummaryByRef(
                  manager,
                  VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS,
                  classDispensationRequestDetail.vesselScreeningId,
                );
            }
          });
        }

        // sent notification
        let dataNoti: INotificationEventModel;
        let dataSendMail: IEmailEventModel;

        const receiver = classDispensationsDB.createdUser;

        if (classDispensationsDB.status !== body.status) {
          const performer = await this.manager
            .getCustomRepository(UserRepository)
            .getUserDetailAndSelect(token.id, ['id', 'username', 'jobTitle', 'email']);
          dataNoti = {
            receivers: [receiver as IUser],
            module: ModuleType.SAIL_REPORTING,
            recordId: classDispensationsDB.vesselId,
            type: PushTypeEnum.CHANGE_RECORD_STATUS,
            performer: performer,
            executedAt: new Date(),
            currentStatus: body.status,
            previousStatus: classDispensationsDB.status,
            recordRef: classDispensationsDB.refId,
          };

          dataSendMail = {
            receiver: receiver as IUserEmail,
            type: EmailTypeEnum.CHANGE_RECORD_STATUS,
            templateKey: MailTemplate.CHANGE_RECORD_STATUS,
            subject: '[Notification] Change status in a record',
            data: {
              username: receiver.username,
              baseURL: APP_CONFIG.ENV.SHARE.PUBLIC.RESOURCE.ADMIN_PAGE,
              recordId: classDispensationsDB.vesselId,
              path: ModulePathEnum.SAIL_GENERAL_REPORT,
              queryParam: '?tab=technical&subTab=surveys',
              currentStatus: body.status,
              previousStatus: classDispensationsDB.status,
              performer: performer,
              executedAt: new Date(),
            },
          } as IEmailEventModel;
        }
        return { dataNoti, dataSendMail: dataSendMail };
      } else {
        throw new BaseError({ status: 404, message: 'classDispensations.NOT_FOUND' });
      }
    } catch (ex) {
      LoggerCommon.error(
        '[ClassDispensationsRepository] updateClassDispensations error ',
        ex.message || ex,
      );
      throw ex;
    }
  }

  async deleteClassDispensations(id: string, token: TokenPayloadModel) {
    try {
      const classDispensation = await this.findOne({
        where: { id },
        select: ['vesselId'],
      });

      const deleteClassDispensations = await this.softDelete({
        id: id,
        companyId: token.companyId,
      });

      if (deleteClassDispensations.affected === 0) {
        throw new BaseError({ status: 404, message: 'classDispensations.NOT_FOUND' });
      } else {
        // trigger
        if (classDispensation.vesselId) {
          const vesselScreeningIds = await this.manager
            .getCustomRepository(VesselScreeningRepository)
            .find({
              where: { vesselId: classDispensation.vesselId },
              select: ['id'],
            });
          for (let i = 0; i < vesselScreeningIds.length; i++) {
            await this.manager
              .getCustomRepository(VesselScreeningSummaryRepository)
              .updateVesselScreeningSummaryByRef(
                this.manager,
                VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS,
                vesselScreeningIds[i].id,
              );
          }
        }
        return 1;
      }
    } catch (ex) {
      throw ex;
    }
  }
  async listClassDispensationsByVesselScreeningId(
    vesselScreeningId: string,
    query: ListClassDispensationsDto,
    token: TokenPayloadModel,
  ) {
    const foundVesselScreening = await this.manager.findOne(VesselScreening, {
      where: {
        id: vesselScreeningId,
        deleted: false,
      },
      select: ['id', 'vesselId', 'createdAt'],
    });
    if (!foundVesselScreening) {
      throw new BaseError({
        status: 404,
        message: 'classDispensations.VESSEL_SCREENING_NOT_FOUND',
      });
    }
    const queryBuilder = this.createQueryBuilder('classDispensations')
      .leftJoinAndSelect(
        'classDispensations.classDispensationsRequests',
        'classDispensationsRequests',
        'classDispensationsRequests.vesselScreeningId = :vesselScreeningId',
        { vesselScreeningId },
      )
      .leftJoin('classDispensations.eventType', 'eventType')
      .leftJoin('classDispensations.authority', 'authority')
      .leftJoin('classDispensations.vessel', 'vessel')
      .leftJoinAndSelect('classDispensationsRequests.CDRComments', 'CDRComments')
      .addSelect([
        'eventType.code',
        'eventType.name',
        'authority.name',
        'vessel.code',
        'vessel.name',
        'vessel.imoNumber',
      ])
      .where(
        'classDispensations.companyId = :companyId and classDispensations.vesselId = :vesselId',
        {
          companyId: token.companyId,
          vesselId: foundVesselScreening.vesselId,
        },
      );

    if (query.issueDateFrom) {
      queryBuilder.andWhere('classDispensations.issueDate >= :issueDateFrom', {
        issueDateFrom: new Date(query.issueDateFrom),
      });
    }

    if (query.issueDateTo) {
      queryBuilder.andWhere('classDispensations.issueDate <= :issueDateTo', {
        issueDateTo: new Date(query.issueDateTo),
      });
    }

    if (query.createdAtFrom) {
      queryBuilder.andWhere('classDispensations.createdAt >= :createdAtFrom', {
        createdAtFrom: query.createdAtFrom,
      });
    }
    if (query.createdAtTo) {
      queryBuilder.andWhere('classDispensations.createdAt <= :createdAtTo', {
        createdAtTo: query.createdAtTo,
      });
    }

    const queryBuilderCount = this.createQueryBuilder('classDispensations')
      .leftJoin(
        'classDispensations.classDispensationsRequests',
        'classDispensationsRequests',
        'classDispensationsRequests.vesselScreeningId = :vesselScreeningId',
        { vesselScreeningId },
      )
      .where(
        'classDispensations.companyId = :companyId and classDispensations.vesselId = :vesselId and deleted = false',
        {
          companyId: token.companyId,
          vesselId: foundVesselScreening.vesselId,
        },
      );
    if (query.filterRisk && query.filterRisk === VesselScreeningFilterRiskEnum.OBSERVED) {
      queryBuilderCount
        .select(['classDispensationsRequests.observedRisk as risk', 'count(*) as count'])
        .groupBy('classDispensationsRequests.observedRisk');
    } else {
      queryBuilderCount
        .select(['classDispensationsRequests.potentialRisk as risk', 'count(*) as count'])
        .groupBy('classDispensationsRequests.potentialRisk');
    }

    const [listClassDispensations, groupRisk] = await Promise.all([
      this.list(
        {
          page: query.page,
          limit: query.pageSize,
        },
        {
          queryBuilder,
          sort:
            query.sort ||
            'classDispensations.createdAt:-1;CDRComments.createdAt:-1;CDRComments.updatedAt:-1',
        },
      ),
      queryBuilderCount.getRawMany(),
    ]);
    const vesselScreeningSummary = await this.manager.findOne(VesselScreeningSummary, {
      where: {
        vesselScreeningId: vesselScreeningId,
        reference: VesselScreeningSummaryReferenceEnum.CONDITION_OF_CLASS_DISPENSATIONS,
      },
    });
    return {
      vesselScreeningSummary,
      list: listClassDispensations,
      risk: groupRisk,
    };
  }
}
